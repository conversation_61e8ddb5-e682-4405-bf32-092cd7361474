import type { Component } from 'vue'

export const icons = {
  alertTriangle: import('./AlertTriangleIcon.vue'),
  announcement: import('./AnnouncementIcon.vue'),
  appleLogo: import('./AppleLogoIcon.vue'),
  arrowDown: import('./ArrowDownIcon.vue'),
  arrowLeft: import('./ArrowLeftIcon.vue'),
  arrowRight: import('./ArrowRightIcon.vue'),
  arrowUp: import('./ArrowUpIcon.vue'),
  barChartSquare: import('./BarChartSquareIcon.vue'),
  bell: import('./BellIcon.vue'),
  bold: import('./BoldIcon.vue'),
  bottomLeftCorner: import('./BottomLeftCornerIcon.vue'),
  bottomRightCorner: import('./BottomRightCornerIcon.vue'),
  building: import('./BuildingIcon.vue'),
  calendarCheck: import('./CalendarCheckIcon.vue'),
  calendarPlus: import('./CalendarPlusIcon.vue'),
  certificate: import('./CertificateIcon.vue'),
  check: import('./CheckIcon.vue'),
  checkCircle: import('./CheckCircleIcon.vue'),
  checkVerified: import('./CheckVerifiedIcon.vue'),
  chevronDown: import('./ChevronDownIcon.vue'),
  chevronLeft: import('./ChevronLeftIcon.vue'),
  chevronRight: import('./ChevronRightIcon.vue'),
  chevronUp: import('./ChevronUpIcon.vue'),
  clipboard: import('./ClipboardIcon.vue'),
  closeCircle: import('./CloseCircleIcon.vue'),
  copy: import('./CopyIcon.vue'),
  dashboard: import('./DashboardIcon.vue'),
  download: import('./DownloadIcon.vue'),
  edit: import('./EditIcon.vue'),
  externalLink: import('./ExternalLinkIcon.vue'),
  file: import('./FileIcon.vue'),
  fileAttachement: import('./FileAttachementIcon.vue'),
  filterLines: import('./FilterLinesIcon.vue'),
  googleLogo: import('./GoogleLogoIcon.vue'),
  imagePlus: import('./ImagePlusIcon.vue'),
  infoCircle: import('./InfoCircleIcon.vue'),
  italic: import('./ItalicIcon.vue'),
  language: import('./LanguageIcon.vue'),
  link: import('./LinkIcon.vue'),
  locationPin: import('./LocationPinIcon.vue'),
  lock: import('./LockIcon.vue'),
  logout: import('./LogoutIcon.vue'),
  menu: import('./MenuIcon.vue'),
  microscope: import('./MicroscopeIcon.vue'),
  news: import('./NewsIcon.vue'),
  package: import('./PackageIcon.vue'),
  packagePlus: import('./PackagePlusIcon.vue'),
  paperClip: import('./PaperClipIcon.vue'),
  pdf: import('./PdfIcon.vue'),
  pencil: import('./PencilIcon.vue'),
  receipt: import('./ReceiptIcon.vue'),
  refresh: import('./RefreshIcon.vue'),
  send: import('./SendIcon.vue'),
  settings: import('./SettingsIcon.vue'),
  settingsSlider: import('./SettingsSliderIcon.vue'),
  shield: import('./ShieldIcon.vue'),
  stars: import('./StarsIcon.vue'),
  switchHorizontal: import('./SwitchHorizontalIcon.vue'),
  switchVertical: import('./SwitchVerticalIcon.vue'),
  threeDots: import('./ThreeDotsIcon.vue'),
  threeLayerStack: import('./ThreeLayerStackIcon.vue'),
  trash: import('./TrashIcon.vue'),
  truck: import('./TruckIcon.vue'),
  twoLayerStack: import('./TwoLayerStackIcon.vue'),
  underline: import('./UnderlineIcon.vue'),
  upload: import('./UploadIcon.vue'),
  user: import('./UserIcon.vue'),
  userEdit: import('./UserEditIcon.vue'),
  users: import('./UsersIcon.vue'),
} satisfies Record<string, Component>

type CustomIcons = {
  [K in keyof typeof icons]: Component
}

declare module '@wisemen/vue-core-components' {
  interface Icons extends CustomIcons {}
}

declare module '@wisemen/vue-core-components' {
  interface Icons extends CustomIcons {}
}
