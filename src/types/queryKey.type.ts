import type { PaginationOptions } from '@wisemen/vue-core-components'
import type { ComputedRef } from 'vue'

import type { RequestType } from '@/client'
import type { DashboardAnnouncementIndexPagination } from '@/models/announcement/dashboard-index/dashboardAnnouncementIndexPagination.model.ts'
import type { NewsAnnouncementIndexPagination } from '@/models/announcement/index/announcementIndexPagination.model.ts'
import type {
  ContractLineIndexPagination,
  ContractLinePackagingRequestIndexPagination,
} from '@/models/contract-line/index/contractLineIndexPagination.model.ts'
import type { CustomerIndexPagination } from '@/models/customer/index/customerIndexPagination.model.ts'
import type { DocumentIndexPagination } from '@/models/document/index/documentIndexPagination.model'
import type { DynamicTableViewIndexPagination } from '@/models/dynamic-table/view/dynamicTableViewIndexPagination.model'
import type { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import type { InvoiceDraftIndexPagination } from '@/models/invoice/draft-index/invoiceDraftIndexPagination.model'
import type { InvoiceIndexPagination } from '@/models/invoice/index/invoiceIndexPagination.model'
import type { DashboardNewsIndexPagination } from '@/models/news/dashboard-index/dashboardNewsIndexPagination.model.ts'
import type { NewsArticleIndexPagination } from '@/models/news/index/newsArticleIndexPagination.model.ts'
import type { NewsAnnouncementUuid } from '@/models/news/newsAnnouncementUuid.model'
import type { NewsArticleUuid } from '@/models/news/newsArticleUuid.model'
import type { NewsUuid } from '@/models/news/newsUuid.model'
import type { PackagingRequestUuid } from '@/models/packaging-request/packagingRequestUuid.model'
import type { PickUpAddressIndexPagination } from '@/models/pick-up-address/index/pickUpAddressIndexPagination.model.ts'
import type { PickupRequestIndexPagination } from '@/models/pickup-request/index/pickupRequestIndexPagination.model'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { UnNumberIndexPagination } from '@/models/un-number/index/unNumberIndexPagination.model'
import type { WasteInquiryIndexPagination } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import type { WasteProducerIndexPagination } from '@/models/waste-producer/index/wasteProducerIndexPagination.model.ts'
import type { WeeklyPlanningUuid } from '@/models/weekly-planning/weeklyPlanningUuid.model'

interface ProjectQueryKeys {
  contactIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLineIndexPagination>>
  }
  containerTypeIndex: {
    customerId: ComputedRef<string>
  }
  contractLineIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLineIndexPagination>>
  }
  contractLinePackagingRequestIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLinePackagingRequestIndexPagination>>
  }
  customerCountryCode: {
    customerId: ComputedRef<string>
  }
  customerIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<CustomerIndexPagination>>
  }
  dashboardAnnouncementIndex: {
    locale: ComputedRef<string>
    paginationOptions?: ComputedRef<PaginationOptions<DashboardAnnouncementIndexPagination>>
  }
  dashboardNewsDetail: {
    newsUuid: ComputedRef<NewsUuid>
    locale: ComputedRef<string>
  }
  dashboardNewsIndex: {
    locale: ComputedRef<string>
    paginationOptions?: ComputedRef<PaginationOptions<DashboardNewsIndexPagination>>
  }
  documentIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<DocumentIndexPagination>>
  }
  documentSiteIndex: void
  dynamicTableColumnIndex: {
    dynamicTableName: DynamicTableName
  }
  dynamicTableDefaultView: {
    dynamicTableName: DynamicTableName
  }
  dynamicTableViewIndex: {
    dynamicTableName: DynamicTableName
    paginationOptions?: ComputedRef<PaginationOptions<DynamicTableViewIndexPagination>>
  }
  ewcCodeIndex: void
  invoiceDraftIndex: { paginationOptions?: ComputedRef<PaginationOptions<InvoiceDraftIndexPagination>> }
  invoiceIndex: { paginationOptions?: ComputedRef<PaginationOptions<InvoiceIndexPagination>> }
  newsAnnouncementDetail: {
    newsAnnouncementUuid: ComputedRef<NewsAnnouncementUuid>
  }
  newsAnnouncementIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<NewsAnnouncementIndexPagination>>
  }
  newsArticleDetail: {
    newsArticleUuid: ComputedRef<NewsArticleUuid>
  }
  newsArticleIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<NewsArticleIndexPagination>>
  }
  newsDetail: {
    newsUuid: ComputedRef<NewsUuid>
  }
  newsIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<NewsArticleIndexPagination>>
  }
  packagingRequestDetail: {
    packagingRequestUuid: ComputedRef<PackagingRequestUuid>
  }
  packagingTypeIndex: {
    customerId: ComputedRef<string>
  }
  permissions: void
  pickUpAddressIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<PickUpAddressIndexPagination>>
  }
  pickupRequestDetail: {
    pickupRequestUuid: ComputedRef<PickupRequestUuid>
  }
  pickupRequestDetailPoNumberAndCostCenterRequired: {
    customerId: ComputedRef<string>
  }
  pickupRequestIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<PickupRequestIndexPagination>>
  }
  pickupRequestSapDetail: {
    requestNumber: ComputedRef<string>
  }
  preference: {
    userUuid: ComputedRef<string | null>
  }
  roles: void
  suggestedCustomerIndex: void
  suggestedPickUpAddressIndex: {
    customerId: ComputedRef<string | null>
    requestType: RequestType
  }
  suggestedWasteProducerIndex: {
    customerId: ComputedRef<string | null>
  }
  tankerTypeIndex: void
  transportTypeIndex: void
  unNumberDetail: {
    number: ComputedRef<string | null>
  }
  unNumberIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<UnNumberIndexPagination>>
  }
  wasteInquiryDetail: {
    wasteInquiryUuid: ComputedRef<WasteInquiryUuid>
  }
  wasteInquiryIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<WasteInquiryIndexPagination>>
  }
  wasteInquirySapDetail: {
    inquiryNumber: ComputedRef<string>
  }
  wasteProducerIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<WasteProducerIndexPagination>>
  }
  weeklyPlanningDetail: {
    weeklyPlanningUuid: ComputedRef<WeeklyPlanningUuid>
  }
}

declare module '@wisemen/vue-core-query' {
  interface QueryKeys extends ProjectQueryKeys {}
}
