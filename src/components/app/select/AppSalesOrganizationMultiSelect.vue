<script setup lang="ts">
import {
  VcIconButton,
  VcSelect,
  VcSelectItem,
} from '@wisemen/vue-core-components'
import {
  computed,
  onMounted,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppBadge from '@/components/app/badge/AppBadge.vue'
import { BadgeColor } from '@/models/badgeColors.enum'
import { useSalesOrganizationSearchQuery } from '@/modules/news/api/queries/sapSalesOrganization.query'
import type { SalesOrganization } from '@/modules/news/api/services/sapSalesOrganization.service'

const props = withDefaults(defineProps<{
  isReadonly?: boolean
  isRequired?: boolean
  isTouched?: boolean
  errorMessage?: string | null
  label?: string | null
  modelValue?: SalesOrganization[]
  placeholder?: string | null
}>(), {
  isRequired: false,
  isTouched: false,
  errorMessage: null,
  label: null,
  modelValue: () => [],
  placeholder: null,
})

const emit = defineEmits<{
  'update:modelValue': [value: SalesOrganization[]]
}>()

const i18n = useI18n()

const searchQuery = ref<string>('')
const salesOrganizationSearchQuery = useSalesOrganizationSearchQuery(computed<string>(() => searchQuery.value))

const mockSalesOrganizations: SalesOrganization[] = [
  {
    id: '1000',
    name: 'Indaver UK',
  },
  {
    id: '2000',
    name: 'Indaver Spain',
  },
  {
    id: '3000',
    name: 'Indaver Belgium',
  },
  {
    id: '4000',
    name: 'Indaver Netherlands',
  },
  {
    id: '5000',
    name: 'Indaver Germany',
  },
]

const salesOrganizations = computed<SalesOrganization[]>(() => {
  const sapData = salesOrganizationSearchQuery.data.value ?? []

  if (sapData.length > 0) {
    return sapData
  }

  if (searchQuery.value) {
    return mockSalesOrganizations.filter((org) =>
      org.name.toLowerCase().includes(searchQuery.value.toLowerCase())
      || org.id.includes(searchQuery.value))
  }

  return mockSalesOrganizations
})

const isFetching = computed<boolean>(() => {
  return salesOrganizationSearchQuery.isFetching.value
})

onMounted(() => {
  searchQuery.value = ''
})

const modelValue = computed<SalesOrganization[]>({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

function displayFn(value: SalesOrganization): string {
  return value.name
}

function filterFn(option: SalesOrganization, query: string): boolean {
  return option.name.toLowerCase().includes(query.toLowerCase())
    || option.id.toLowerCase().includes(query.toLowerCase())
}

function onSearch(query: string): void {
  searchQuery.value = query
}

function removeOrganization(orgToRemove: SalesOrganization): void {
  const currentValue = modelValue.value
  const newValue = currentValue.filter((org) => org.id !== orgToRemove.id)

  modelValue.value = newValue
}

watch(() => searchQuery.value, () => {
}, { immediate: true })
</script>

<template>
  <div class="space-y-2">
    <div
      v-if="modelValue.length > 0"
      class="flex flex-wrap gap-2"
    >
      <AppBadge
        v-for="org in modelValue"
        :key="org.id"
        :color="BadgeColor.SUCCESS"
        :label="org.name"
        class="flex items-center gap-1"
      >
        <span>{{ org.name }}</span>
        <VcIconButton
          :label="`Remove ${org.name}`"
          :class-config="{
            root: 'h-4 w-4 rounded-full hover:bg-white/20',
            icon: 'size-3',
          }"
          icon="close"
          variant="unstyled"
          size="sm"
          @click="removeOrganization(org)"
        />
      </AppBadge>
    </div>

    <VcSelect
      v-model="modelValue"
      :display-fn="displayFn"
      :filter-fn="filterFn"
      :is-loading="isFetching"
      :is-required="props.isRequired"
      :is-touched="props.isTouched"
      :error-message="props.errorMessage"
      :placeholder="props.placeholder ?? i18n.t('module.news.article.fields.sales_organization_placeholder')"
      :filter="{
        isEnabled: true,
        isInline: true,
      }"
      :class-config="{
        inlineSearchInput: 'z-3',
      }"
      :is-disabled="props.isReadonly"
      :is-multiple="true"
      icon-left="search"
      @search="onSearch"
    >
      <VcSelectItem
        v-for="salesOrg of salesOrganizations"
        :key="salesOrg.id"
        :value="salesOrg"
      >
        {{ salesOrg.name }} ({{ salesOrg.id }})
      </VcSelectItem>
    </VcSelect>
  </div>
</template>
