<script setup lang="ts">
import {
  VcIconButton,
  VcSelect,
  VcSelectItem,
  VcTextField,
} from '@wisemen/vue-core-components'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import AppBadge from '@/components/app/badge/AppBadge.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import { BadgeColor } from '@/models/badgeColors.enum'
import type { SalesOrganization } from '@/modules/news/api/services/sapSalesOrganization.service'
import { useSalesOrganizationSearchQuery } from '@/modules/news/api/queries/sapSalesOrganization.query'

const props = withDefaults(defineProps<{
  modelValue?: SalesOrganization[]
  isReadonly?: boolean
  isRequired?: boolean
  isTouched?: boolean
  errorMessage?: string | null
  label?: string | null
  placeholder?: string | null
}>(), {
  modelValue: () => [],
  isRequired: false,
  isTouched: false,
  errorMessage: null,
  label: null,
  placeholder: null,
})

const emit = defineEmits<{
  'update:modelValue': [value: SalesOrganization[]]
}>()

const i18n = useI18n()

const searchQuery = ref('')
const salesOrganizationSearchQuery = useSalesOrganizationSearchQuery(computed(() => searchQuery.value))

// Temporary mock data for testing
const mockSalesOrganizations: SalesOrganization[] = [
  { id: '1000', name: 'Indaver UK' },
  { id: '2000', name: 'Indaver Spain' },
  { id: '3000', name: 'Indaver Belgium' },
  { id: '4000', name: 'Indaver Netherlands' },
  { id: '5000', name: 'Indaver Germany' },
]

const salesOrganizations = computed<SalesOrganization[]>(() => {
  const sapData = salesOrganizationSearchQuery.data.value ?? []

  // If SAP data is available, use it; otherwise use mock data for testing
  if (sapData.length > 0) {
    return sapData
  }

  // Filter mock data based on search query
  if (searchQuery.value) {
    return mockSalesOrganizations.filter(org =>
      org.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      org.id.includes(searchQuery.value)
    )
  }

  return mockSalesOrganizations
})

const isFetching = computed<boolean>(() => {
  return salesOrganizationSearchQuery.isFetching.value
})

// Load initial data on mount
onMounted(() => {
  // Trigger initial search to load some data
  searchQuery.value = ''
})

const modelValue = computed<SalesOrganization[]>({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

function displayFn(value: SalesOrganization): string {
  return value.name
}

function filterFn(option: SalesOrganization, query: string): boolean {
  return option.name.toLowerCase().includes(query.toLowerCase()) ||
         option.id.toLowerCase().includes(query.toLowerCase())
}

// Update search query when user types
function onSearch(query: string): void {
  searchQuery.value = query
}

// Remove a selected organization
function removeOrganization(orgToRemove: SalesOrganization): void {
  const currentValue = modelValue.value
  const newValue = currentValue.filter(org => org.id !== orgToRemove.id)
  modelValue.value = newValue
}

// Load initial data
watch(() => searchQuery.value, () => {
  // The query will automatically trigger due to the computed dependency
}, { immediate: true })
</script>

<template>
  <div class="space-y-2">
    <!-- Selected Organizations as Chips -->
    <div
      v-if="modelValue.length > 0"
      class="flex flex-wrap gap-2"
    >
      <AppBadge
        v-for="org in modelValue"
        :key="org.id"
        :color="BadgeColor.SUCCESS"
        :label="org.name"
        class="flex items-center gap-1"
      >
        <span>{{ org.name }}</span>
        <VcIconButton
          :label="`Remove ${org.name}`"
          :class-config="{
            root: 'h-4 w-4 rounded-full hover:bg-white/20',
            icon: 'size-3',
          }"
          icon="close"
          variant="unstyled"
          size="sm"
          @click="removeOrganization(org)"
        />
      </AppBadge>
    </div>

    <!-- Search and Select -->
    <VcSelect
      v-model="modelValue"
      :display-fn="displayFn"
      :filter-fn="filterFn"
      :is-loading="isFetching"
      :is-required="props.isRequired"
      :is-touched="props.isTouched"
      :error-message="props.errorMessage"
      :label="props.label"
      :placeholder="props.placeholder ?? i18n.t('module.news.article.fields.sales_organization_placeholder')"
      :filter="{
        isEnabled: true,
        isInline: true,
      }"
      :class-config="{
        inlineSearchInput: 'z-3',
      }"
      :is-disabled="props.isReadonly"
      :is-multiple="true"
      icon-left="search"
      @search="onSearch"
    >
      <VcSelectItem
        v-for="salesOrg of salesOrganizations"
        :key="salesOrg.id"
        :value="salesOrg"
      >
        {{ salesOrg.name }} ({{ salesOrg.id }})
      </VcSelectItem>
    </VcSelect>
  </div>
</template>
