<script setup lang="ts">
import {
  VcSelect,
  VcSelectItem,
} from '@wisemen/vue-core-components'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { SalesOrganization } from '@/modules/news/api/services/sapSalesOrganization.service'
import { useSalesOrganizationSearchQuery } from '@/modules/news/api/queries/sapSalesOrganization.query'

const props = withDefaults(defineProps<{
  modelValue?: SalesOrganization[]
  isReadonly?: boolean
  isRequired?: boolean
  isTouched?: boolean
  errorMessage?: string | null
  label?: string | null
  placeholder?: string | null
}>(), {
  modelValue: () => [],
  isRequired: false,
  isTouched: false,
  errorMessage: null,
  label: null,
  placeholder: null,
})

const emit = defineEmits<{
  'update:modelValue': [value: SalesOrganization[]]
}>()

const i18n = useI18n()

const searchQuery = ref('')
const salesOrganizationSearchQuery = useSalesOrganizationSearchQuery(computed(() => searchQuery.value))

const salesOrganizations = computed<SalesOrganization[]>(() => {
  return salesOrganizationSearchQuery.data.value ?? []
})

const isFetching = computed<boolean>(() => {
  return salesOrganizationSearchQuery.isFetching.value
})

const modelValue = computed<SalesOrganization[]>({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

function displayFn(value: SalesOrganization): string {
  return value.name
}

function filterFn(option: SalesOrganization, query: string): boolean {
  return option.name.toLowerCase().includes(query.toLowerCase()) ||
         option.id.toLowerCase().includes(query.toLowerCase())
}

// Update search query when user types
function onSearch(query: string): void {
  searchQuery.value = query
}

// Load initial data
watch(() => searchQuery.value, () => {
  // Trigger search query
}, { immediate: true })
</script>

<template>
  <VcSelect
    v-model="modelValue"
    :display-fn="displayFn"
    :filter-fn="filterFn"
    :is-loading="isFetching"
    :is-required="props.isRequired"
    :is-touched="props.isTouched"
    :error-message="props.errorMessage"
    :label="props.label"
    :placeholder="props.placeholder ?? i18n.t('module.news.article.fields.sales_organization_placeholder')"
    :filter="{
      isEnabled: true,
      isInline: true,
    }"
    :class-config="{
      inlineSearchInput: 'z-3',
    }"
    :is-disabled="props.isReadonly"
    icon-left="search"
    @search="onSearch"
  >
    <VcSelectItem
      v-for="salesOrg of salesOrganizations"
      :key="salesOrg.id"
      :value="salesOrg"
    >
      {{ salesOrg.name }} ({{ salesOrg.id }})
    </VcSelectItem>
  </VcSelect>
</template>
