<script setup lang="ts">
import type { FileUploadItem } from '@wisemen/vue-core-components'
import {
  VcFileUploadItem,
  VcFileUploadItemRemove,
  VcIcon,
  VcIconButton,
  VcSpin<PERSON>,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { useI18n } from 'vue-i18n'

import AppAnimatedNumber from '@/components/app/animated-number/AppAnimatedNumber.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppFileIcon from '@/components/app/file-icon/AppFileIcon.vue'
import { FileUtil } from '@/utils/file.util'

const props = withDefaults(defineProps<{
  hideDeleteButton?: boolean
  item: FileUploadItem
  size?: 'md' | 'sm'
}>(), { size: 'md' })

const i18n = useI18n()
</script>

<template>
  <Motion
    :layout="true"
    :initial="{
      opacity: 0,
      scale: 0.95,
      filter: 'blur(4px)',
    }"
    :animate="{
      opacity: 1,
      scale: 1,
      filter: 'blur(0px)',
    }"
    :exit="{
      opacity: 0,
      scale: 0.95,
      filter: 'blur(4px)',
    }"
    :transition="{
      bounce: 0,
      duration: 0.4,
      type: 'spring',
    }"
    class="size-full"
  >
    <div
      :class="{
        'border-error-subtle': props.item.status === 'error',
        'border-secondary': props.item.status !== 'error',
        'p-xl': props.size === 'md',
        'py-md px-lg': props.size === 'sm',
      }"
      class="
        bg-primary size-full rounded-xl border border-solid duration-200
        hover:brightness-99
      "
    >
      <VcFileUploadItem :item="props.item">
        <AppGroup
          gap="lg"
          justify="between"
        >
          <AppGroup
            gap="lg"
            class="overflow-hidden"
          >
            <div class="size-8 shrink-0">
              <img
                v-if="FileUtil.isImage(props.item.mimeType) && props.item.url !== null"
                :src="props.item.url"
                :alt="props.item.name"
                class="rounded-lg object-cover"
              >

              <AppFileIcon
                v-else
                :mime-type="props.item.mimeType"
              />
            </div>

            <AppGroup
              gap="none"
              direction="col"
              align="start"
              class="overflow-hidden"
            >
              <span
                :class="{
                  'text-error-primary': props.item.status === 'error',
                  'text-secondary': props.item.status !== 'error',
                }"
                class="w-full truncate text-sm"
              >
                {{ props.item.name }}
              </span>

              <AnimatePresence mode="wait">
                <Motion
                  v-if="props.item.status === 'success'"
                  :transition="{
                    type: 'spring',
                    bounce: 0,
                  }"
                  :initial="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  :animate="{
                    opacity: 1,
                    scale: 1,
                    filter: 'blur(0px)',
                  }"
                  :exit="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  class="overflow-clip"
                >
                  <AppGroup gap="sm">
                    <VcIcon
                      icon="checkCircle"
                      class="text-success-primary size-3.5"
                    />

                    <span class="text-success-primary text-xs">
                      {{ i18n.t('component.file_upload.status.finished') }}
                    </span>
                  </AppGroup>
                </Motion>

                <Motion
                  v-else-if="props.item.status === 'error'"
                  :transition="{
                    type: 'spring',
                    bounce: 0,
                  }"
                  :initial="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  :animate="{
                    opacity: 1,
                    scale: 1,
                    filter: 'blur(0px)',
                  }"
                  :exit="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  class="w-full overflow-hidden"
                >
                  <AppGroup
                    gap="sm"
                    class="overflow-hidden"
                  >
                    <VcIcon
                      icon="closeCircle"
                      class="text-error-primary size-3.5"
                    />

                    <span class="text-error-primary w-full truncate text-xs">
                      {{ i18n.t('component.file_upload.status.failed') }} - {{ props.item.errorMessage }}
                    </span>
                  </AppGroup>
                </Motion>

                <Motion
                  v-else-if="props.item.status === 'pending'"
                  :transition="{
                    type: 'spring',
                    bounce: 0,
                  }"
                  :initial="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  :animate="{
                    opacity: 1,
                    scale: 1,
                    filter: 'blur(0px)',
                  }"
                  :exit="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  class="overflow-clip"
                >
                  <AppGroup gap="sm">
                    <VcSpinner class="text-secondary size-3.5" />

                    <span class="text-secondary text-xs">
                      {{ i18n.t('component.file_upload.status.pending') }}
                    </span>
                  </AppGroup>
                </Motion>

                <Motion
                  v-else-if="props.item.status === 'uploading'"
                  :transition="{
                    type: 'spring',
                    bounce: 0,
                  }"
                  :initial="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                  }"
                  :animate="{
                    opacity: 1,
                    scale: 1,
                    filter: 'blur(0px)',
                  }"
                  :exit="{
                    opacity: 0,
                    scale: 0.95,
                    filter: 'blur(4px)',
                    transition: {
                      duration: 0.3,
                    },
                  }"
                  class="overflow-clip"
                >
                  <AppGroup gap="xs">
                    <AppAnimatedNumber
                      :format="{
                        style: 'percent',
                      }"
                      :value="props.item.progress / 100"
                      :suffix="` - ${props.item.mimeType}`"
                      class="text-tertiary text-xs"
                    />
                  </AppGroup>
                </Motion>
              </AnimatePresence>
            </AppGroup>
          </AppGroup>

          <VcFileUploadItemRemove v-if="!props.hideDeleteButton">
            <VcIconButton
              :label="i18n.t('shared.remove')"
              :class-config="{
                root: 'rounded-md',
              }"
              icon="trash"
              size="sm"
              variant="destructive-tertiary"
              class="shrink-0"
            />
          </VcFileUploadItemRemove>
        </AppGroup>
      </VcFileUploadItem>
    </div>
  </Motion>
</template>
