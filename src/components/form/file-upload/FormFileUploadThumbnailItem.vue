<script setup lang="ts">
import type { FileUploadItem } from '@wisemen/vue-core-components'
import {
  VcFileUploadItem,
  VcFileUploadItemRemove,
  VcIcon,
  VcIconButton,
  Vc<PERSON><PERSON><PERSON>,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { useI18n } from 'vue-i18n'

import AppAnimatedNumber from '@/components/app/animated-number/AppAnimatedNumber.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppFileIcon from '@/components/app/file-icon/AppFileIcon.vue'
import { FileUtil } from '@/utils/file.util'



const props = defineProps<{
  item: FileUploadItem
}>()

const i18n = useI18n()
</script>

<template>
  <Motion
    :layout="true"
    :initial="{
      opacity: 0,
      scale: 0.95,
      filter: 'blur(4px)',
    }"
    :animate="{
      opacity: 1,
      scale: 1,
      filter: 'blur(0px)',
    }"
    :exit="{
      opacity: 0,
      scale: 0.95,
      filter: 'blur(4px)',
    }"
    :transition="{
      bounce: 0,
      duration: 0.4,
      type: 'spring',
    }"
    class="size-fit"
  >
    <div
      :class="{
        'border-error-subtle': props.item.status === 'error',
        'border-secondary': props.item.status !== 'error',
      }"
      class="
        bg-primary relative h-52 w-72 overflow-hidden rounded-xl border
        border-solid duration-200
        hover:brightness-99
      "
    >
      <VcFileUploadItem :item="props.item">
        <img
          v-if="FileUtil.isImage(props.item.mimeType) && props.item.url !== null"
          :src="props.item.url"
          :alt="props.item.name"
          class="size-full rounded-lg object-cover"
        >

        <AppFileIcon
          v-else
          :mime-type="props.item.mimeType"
        />

        <VcFileUploadItemRemove>
          <VcIconButton
            :label="i18n.t('shared.remove')"
            :class-config="{
              root: 'rounded-md backdrop-blur-xl bg-white/50',
            }"
            icon="trash"
            size="sm"
            variant="destructive-tertiary"
            class="!absolute top-1 right-1"
          />
        </VcFileUploadItemRemove>

        <Motion
          :animate="{ width: 'auto' }"
          :data-status="props.item.status"
          layout="size"
          class="
            bg-primary/75 py-xs px-sm absolute right-2 bottom-2 rounded-full
            backdrop-blur-xl
          "
        >
          <AnimatePresence mode="wait">
            <Motion
              v-if="props.item.status === 'success'"
              :transition="{
                type: 'spring',
                bounce: 0,
              }"
              :initial="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              :animate="{
                opacity: 1,
                scale: 1,
                filter: 'blur(0px)',
              }"
              :exit="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              class="overflow-clip"
            >
              <AppGroup gap="sm">
                <VcIcon
                  icon="checkCircle"
                  class="text-success-primary size-3.5"
                />

                <span class="text-success-primary text-xs">
                  {{ i18n.t('component.file_upload.status.finished') }}
                </span>
              </AppGroup>
            </Motion>

            <Motion
              v-else-if="props.item.status === 'error'"
              :transition="{
                type: 'spring',
                bounce: 0,
              }"
              :initial="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              :animate="{
                opacity: 1,
                scale: 1,
                filter: 'blur(0px)',
              }"
              :exit="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              class="w-full overflow-hidden"
            >
              <AppGroup
                gap="sm"
                class="overflow-hidden"
              >
                <VcIcon
                  icon="closeCircle"
                  class="text-error-primary size-3.5"
                />

                <span class="text-error-primary w-full truncate text-xs">
                  {{ i18n.t('component.file_upload.status.failed') }} - {{ (props.item as any).errorMessage }}
                </span>
              </AppGroup>
            </Motion>

            <Motion
              v-else-if="props.item.status === FileUploadStatus.PENDING"
              :transition="{
                type: 'spring',
                bounce: 0,
              }"
              :initial="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              :animate="{
                opacity: 1,
                scale: 1,
                filter: 'blur(0px)',
              }"
              :exit="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              class="overflow-clip"
            >
              <AppGroup gap="sm">
                <VcSpinner class="text-secondary size-3.5" />

                <span class="text-secondary text-xs">
                  {{ i18n.t('component.file_upload.status.pending') }}
                </span>
              </AppGroup>
            </Motion>

            <Motion
              v-else-if="props.item.status === FileUploadStatus.UPLOADING"
              :transition="{
                type: 'spring',
                bounce: 0,
              }"
              :initial="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
              }"
              :animate="{
                opacity: 1,
                scale: 1,
                filter: 'blur(0px)',
              }"
              :exit="{
                opacity: 0,
                scale: 0.95,
                filter: 'blur(4px)',
                transition: {
                  duration: 0.3,
                },
              }"
              class="overflow-clip"
            >
              <AppGroup gap="xs">
                <AppAnimatedNumber
                  :format="{
                    style: 'percent',
                  }"
                  :value="props.item.progress / 100"
                  :suffix="` - ${props.item.mimeType}`"
                  class="text-tertiary text-xs"
                />
              </AppGroup>
            </Motion>
          </AnimatePresence>
        </Motion>
      </VcFileUploadItem>
    </div>
  </Motion>
</template>
