import type { DocumentIndex } from '@/models/document/index/documentIndex.model.ts'
import type { DocumentIndexDto } from '@/models/document/index/documentIndexDto.model.ts'
import type { DocumentIndexPagination } from '@/models/document/index/documentIndexPagination.model.ts'
import type { DocumentIndexPaginationDto } from '@/models/document/index/documentIndexPaginationDto.model.ts'

import { CalendarDateTransformer } from '../../date/calendarDate.transformer.ts'

export class DocumentIndexTransformer {
  static fromDto(dto: DocumentIndexDto): DocumentIndex {
    return {
      id: dto.id,
      actionAt: CalendarDateTransformer.fromNullableDto(dto.actionAt),
      name: dto.name,
      applicableFrom: CalendarDateTransformer.fromNullableDto(dto.applicableFrom),
      applicableTill: CalendarDateTransformer.fromNullableDto(dto.applicableTill),
      status: dto.status,
      tfsType: dto.tfsType,
      wasteProducer: dto.wasteProducer,
    }
  }
}

export class DocumentIndexPaginationTransformer {
  static toDto(pagination: DocumentIndexPagination): DocumentIndexPaginationDto {
    return {
      filter: {
        customerUuid: pagination.filter.customerUuid,
        status: pagination.filter.status,
        viewName: pagination.filter.viewName,
        wasteProducerIds: pagination.filter.wasteProducerIds,
        year: pagination.filter.year ? pagination.filter.year.toString() : undefined,
      },
      sort: pagination.sort,
    }
  }
}
