import { DynamicColumnNames } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin'
import { createI18nKeyMap } from '@/types/enum.type'

export class DynamicTableEnumUtil {
  private static i18nKeys = createI18nKeyMap<DynamicColumnNames>({
    [DynamicColumnNames.ACCOUNT_DOCUMENT_NUMBER]: 'enum.dynamic_table_column_name.account_document_number',
    [DynamicColumnNames.ACCOUNT_MANAGER]: 'enum.dynamic_table_column_name.account_manager',
    [DynamicColumnNames.ACCOUNT_MANAGER_NAME]: 'enum.dynamic_table_column_name.account_manager_name',
    [DynamicColumnNames.ASN]: 'enum.dynamic_table_column_name.asn',
    [DynamicColumnNames.AUTO_APPROVED_ON]: 'enum.dynamic_table_column_name.auto_approved_on',
    [DynamicColumnNames.COMPANY_NAME]: 'enum.dynamic_table_column_name.company_name',
    [DynamicColumnNames.CONFIRMED_TRANSPORT_DATE]: 'enum.dynamic_table_column_name.confirmed_transport_date',
    [DynamicColumnNames.CONTAINER_NUMBER]: 'enum.dynamic_table_column_name.container_number',
    [DynamicColumnNames.CONTAINER_TRANSPORT_TYPE]: 'enum.dynamic_table_column_name.container_transport_type',
    [DynamicColumnNames.CONTAINER_TYPE]: 'enum.dynamic_table_column_name.container_type',
    [DynamicColumnNames.CONTAINER_VOLUME_SIZE]: 'enum.dynamic_table_column_name.container_volume_size',
    [DynamicColumnNames.CONTRACT_ID]: 'enum.dynamic_table_column_name.contract_id',
    [DynamicColumnNames.CONTRACT_ITEM]: 'enum.dynamic_table_column_name.contract_item',
    [DynamicColumnNames.CONTRACT_NUMBER]: 'enum.dynamic_table_column_name.contract_number',
    [DynamicColumnNames.COST_CENTER]: 'enum.dynamic_table_column_name.cost_center',
    [DynamicColumnNames.CURRENCY]: 'enum.dynamic_table_column_name.currency',
    [DynamicColumnNames.CUSTOMER_ID]: 'enum.dynamic_table_column_name.customer_id',
    [DynamicColumnNames.CUSTOMER_NAME]: 'enum.dynamic_table_column_name.customer_name',
    [DynamicColumnNames.CUSTOMER_REFERENCE]: 'enum.dynamic_table_column_name.customer_reference',
    [DynamicColumnNames.DANGER_LABEL1]: 'enum.dynamic_table_column_name.danger_label1',
    [DynamicColumnNames.DANGER_LABEL2]: 'enum.dynamic_table_column_name.danger_label2',
    [DynamicColumnNames.DANGER_LABEL3]: 'enum.dynamic_table_column_name.danger_label3',
    [DynamicColumnNames.DATE]: 'enum.dynamic_table_column_name.date',
    [DynamicColumnNames.DATE_OF_REQUEST]: 'enum.dynamic_table_column_name.date_of_request',
    [DynamicColumnNames.DELIVERY_INFO]: 'enum.dynamic_table_column_name.delivery_info',
    [DynamicColumnNames.DISPOSAL_CERTIFICATE_NUMBER]: 'enum.dynamic_table_column_name.disposal_certificate_number',
    [DynamicColumnNames.DUE_ON]: 'enum.dynamic_table_column_name.due_on',
    [DynamicColumnNames.END_TREATMENT_CENTER_ID]: 'enum.dynamic_table_column_name.end_treatment_center_id',
    [DynamicColumnNames.END_TREATMENT_CENTER_NAME]: 'enum.dynamic_table_column_name.end_treatment_center_name',
    [DynamicColumnNames.ESN_NUMBER]: 'enum.dynamic_table_column_name.esn_number',
    [DynamicColumnNames.ESTIMATED_WEIGHT_OR_VOLUME_UNIT]: 'enum.dynamic_table_column_name.estimated_weight_or_volume_unit',
    [DynamicColumnNames.ESTIMATED_WEIGHT_OR_VOLUME_VALUE]: 'enum.dynamic_table_column_name.estimated_weight_or_volume_value',
    [DynamicColumnNames.EWC]: 'enum.dynamic_table_column_name.ewc_code',
    [DynamicColumnNames.EWC_CODE]: 'enum.dynamic_table_column_name.ewc_code',
    [DynamicColumnNames.FIRST_REMINDER_MAIL_STATUS]: 'enum.dynamic_table_column_name.first_reminder_mail_status',
    [DynamicColumnNames.FIRST_REMINDER_ON]: 'enum.dynamic_table_column_name.first_reminder_on',
    [DynamicColumnNames.HAZARD_INDUCERS]: 'enum.dynamic_table_column_name.hazard_inducers',
    [DynamicColumnNames.INQUIRY_NUMBER]: 'enum.dynamic_table_column_name.inquiry_number',
    [DynamicColumnNames.INSTALLATION_NAME]: 'enum.dynamic_table_column_name.installation_name',
    [DynamicColumnNames.INVOICE_NUMBER]: 'enum.dynamic_table_column_name.invoice_number',
    [DynamicColumnNames.IS_CONTAINER_COVERED]: 'enum.dynamic_table_column_name.is_container_covered',
    [DynamicColumnNames.IS_HAZARDOUS]: 'enum.dynamic_table_column_name.is_hazardous',
    [DynamicColumnNames.IS_RETURN_PACKAGING]: 'enum.dynamic_table_column_name.is_return_packaging',
    [DynamicColumnNames.IS_TRANSPORT_BY_INDAVER]: 'enum.dynamic_table_column_name.is_transport_by_indaver',
    [DynamicColumnNames.ISSUED_ON]: 'enum.dynamic_table_column_name.issued_on',
    [DynamicColumnNames.MATERIAL_ANALYSIS]: 'enum.dynamic_table_column_name.material_analysis',
    [DynamicColumnNames.MATERIAL_NUMBER]: 'enum.dynamic_table_column_name.material_number',
    [DynamicColumnNames.MATERIAL_TYPE]: 'enum.dynamic_table_column_name.material_type',
    [DynamicColumnNames.NAME_INSTALLATION]: 'enum.dynamic_table_column_name.name_installation',
    [DynamicColumnNames.NAME_OF_APPLICANT]: 'enum.dynamic_table_column_name.name_of_applicant',
    [DynamicColumnNames.NET_AMOUNT]: 'enum.dynamic_table_column_name.net_amount',
    [DynamicColumnNames.ORDER_NUMBER]: 'enum.dynamic_table_column_name.order_number',
    [DynamicColumnNames.PACKAGED]: 'enum.dynamic_table_column_name.packaged',
    [DynamicColumnNames.PACKAGING_INDICATOR]: 'enum.dynamic_table_column_name.packaging_indicator',
    [DynamicColumnNames.PACKAGING_REMARK]: 'enum.dynamic_table_column_name.packaging_remark',
    [DynamicColumnNames.PACKAGING_TYPE]: 'enum.dynamic_table_column_name.packaging_type',
    [DynamicColumnNames.PACKING_GROUP]: 'enum.dynamic_table_column_name.packing_group',
    [DynamicColumnNames.PAYER_ID]: 'enum.dynamic_table_column_name.payer_id',
    [DynamicColumnNames.PAYER_NAME]: 'enum.dynamic_table_column_name.payer_name',
    [DynamicColumnNames.PICK_UP_ADDRESS_ID]: 'enum.dynamic_table_column_name.pick_up_address_id',
    [DynamicColumnNames.PICK_UP_ADDRESS_NAME]: 'enum.dynamic_table_column_name.pick_up_address_name',
    [DynamicColumnNames.PO_NUMBER]: 'enum.dynamic_table_column_name.po_number',
    [DynamicColumnNames.PROCESS_CODE]: 'enum.dynamic_table_column_name.process_code',
    [DynamicColumnNames.QUANTITY_CONTAINERS]: 'enum.dynamic_table_column_name.quantity_containers',
    [DynamicColumnNames.QUANTITY_LABELS]: 'enum.dynamic_table_column_name.quantity_labels',
    [DynamicColumnNames.QUANTITY_PACKAGES]: 'enum.dynamic_table_column_name.quantity_packages',
    [DynamicColumnNames.QUANTITY_PALLETS]: 'enum.dynamic_table_column_name.quantity_pallets',
    [DynamicColumnNames.RECONCILIATION_NUMBER]: 'enum.dynamic_table_column_name.reconciliation_number',
    [DynamicColumnNames.REMARKS]: 'enum.dynamic_table_column_name.remarks',
    [DynamicColumnNames.REQUEST_NUMBER]: 'enum.dynamic_table_column_name.request_number',
    [DynamicColumnNames.REQUESTED_END_DATE]: 'enum.dynamic_table_column_name.requested_end_date',
    [DynamicColumnNames.REQUESTED_START_DATE]: 'enum.dynamic_table_column_name.requested_start_date',
    [DynamicColumnNames.REQUESTOR_NAME]: 'enum.dynamic_table_column_name.requested_by',
    [DynamicColumnNames.SALES_ORDER]: 'enum.dynamic_table_column_name.sales_order',
    [DynamicColumnNames.SALES_ORGANISATION_ID]: 'enum.dynamic_table_column_name.sales_organisation_id',
    [DynamicColumnNames.SALES_ORGANISATION_NAME]: 'enum.dynamic_table_column_name.sales_organisation_name',
    [DynamicColumnNames.SECOND_REMINDER_MAIL_STATUS]: 'enum.dynamic_table_column_name.second_reminder_mail_status',
    [DynamicColumnNames.SECOND_REMINDER_ON]: 'enum.dynamic_table_column_name.second_reminder_on',
    [DynamicColumnNames.SERIAL_NUMBER]: 'enum.dynamic_table_column_name.serial_number',
    [DynamicColumnNames.STATUS]: 'enum.dynamic_table_column_name.status',
    [DynamicColumnNames.TANKER_TYPE]: 'enum.dynamic_table_column_name.tanker_type',
    [DynamicColumnNames.TC_NUMBER]: 'enum.dynamic_table_column_name.tc_number',
    [DynamicColumnNames.TFS]: 'enum.dynamic_table_column_name.tfs_number',
    [DynamicColumnNames.TFS_NUMBER]: 'enum.dynamic_table_column_name.tfs_number',
    [DynamicColumnNames.THIRD_REMINDER_STATUS]: 'enum.dynamic_table_column_name.third_reminder_status',
    [DynamicColumnNames.TOTAL_QUANTITY_PALLETS]: 'enum.dynamic_table_column_name.total_quantity_pallets',
    [DynamicColumnNames.TRANSPORT_MODE]: 'enum.dynamic_table_column_name.transport_mode',
    [DynamicColumnNames.TREATMENT_CENTER_NAME]: 'enum.dynamic_table_column_name.treatment_center_name',
    [DynamicColumnNames.TYPE]: 'enum.dynamic_table_column_name.type',
    [DynamicColumnNames.UN_NUMBER]: 'enum.dynamic_table_column_name.un_number',
    [DynamicColumnNames.VAT_AMOUNT]: 'enum.dynamic_table_column_name.vat_amount',
    [DynamicColumnNames.WASTE_MATERIAL]: 'enum.dynamic_table_column_name.waste_material',
    [DynamicColumnNames.WASTE_PRODUCER_ID]: 'enum.dynamic_table_column_name.waste_producer_id',
    [DynamicColumnNames.WASTE_PRODUCER_NAME]: 'enum.dynamic_table_column_name.waste_producer',
    [DynamicColumnNames.WASTE_STREAM_NAME]: 'enum.dynamic_table_column_name.waste_item',
  })

  static getLabelI18nKey(value: DynamicColumnNames): I18nKey {
    return this.i18nKeys.get(value)!
  }
}
