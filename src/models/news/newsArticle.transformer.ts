import type { Locale } from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { NewsArticleCreateDto } from '@/models/news/article-update/newsArticleCreateDto.model'
import type { NewsArticleForm } from '@/models/news/article-update/newsArticleCreateForm.model'
import type { NewsArticleUpdateDto } from '@/models/news/article-update/newsArticleUpdateDto.model'
import type { DashboardNewsDetail } from '@/models/news/dashboard-detail/dashboardNewsDetail.model'
import type { DashboardNewsDetailDto } from '@/models/news/dashboard-detail/dashboardNewsDetailDto.model'
import type { DashboardNewsIndex } from '@/models/news/dashboard-index/dashboardNewsIndex.model'
import type { DashboardNewsIndexDto } from '@/models/news/dashboard-index/dashboardNewsIndexDto.model'
import type { DashboardNewsIndexPagination } from '@/models/news/dashboard-index/dashboardNewsIndexPagination.model'
import type { DashboardNewsIndexPaginationDto } from '@/models/news/dashboard-index/dashboardNewsIndexPaginationDto.model'
import type {
  NewsArticleDetail,
  NewsArticleDetailAuthor,
  NewsArticleDetailTranslation,
} from '@/models/news/detail/newsArticleDetail.model.ts'
import type { NewsArticleDetailDto } from '@/models/news/detail/newsArticleDetailDto.model.ts'
import type { NewsArticleIndex } from '@/models/news/index/newsArticleIndex.model.ts'
import type { NewsArticleIndexDto } from '@/models/news/index/newsArticleIndexDto.model.ts'
import type { NewsArticleIndexPagination } from '@/models/news/index/newsArticleIndexPagination.model'
import type { NewsArticleIndexPaginationDto } from '@/models/news/index/newsArticleIndexPaginationDto.model'
import type { NewsArticleUuid } from '@/models/news/newsArticleUuid.model'
import type { NewsAuthorUuid } from '@/models/news/newsAuthorUuid.model'
import type { NewsTranslationUuid } from '@/models/news/newsTranslationUuid.model'
import type { NewsUuid } from '@/models/news/newsUuid.model'
import { S3FileTransformer } from '@/models/s3-file/s3File.transformer'

export class NewsArticleCreateTransformer {
  static toDto(form: NewsArticleForm): NewsArticleCreateDto {
    return {
      imageUuid: form.image!.uuid,
      endDate: CalendarDateTransformer.toNullableDto(form.endDate),
      startDate: CalendarDateTransformer.toDto(form.startDate!),
      newsItemTranslations: form.newsItemTranslations
        .filter((item) => item.title !== null && item.content !== null)
        .map((item) => ({
          title: item.title!,
          content: item.content!,
          language: item.language as unknown as Locale,
        })),
      salesOrganizations: form.salesOrganizations.map((org) => ({
        id: org.id,
        name: org.name,
      })),
      videoIFrame: form.videoIFrame,
    }
  }
}

export class NewsArticleDetailTransformer {
  static fromDto(dto: NewsArticleDetailDto): NewsArticleDetail {
    return {
      uuid: dto.uuid as NewsArticleUuid,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      endDate: CalendarDateTransformer.fromNullableDto(dto.endDate),
      startDate: CalendarDateTransformer.fromNullableDto(dto.startDate),
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      author: this.mapAuthor(dto),
      image: S3FileTransformer.fromNullableDto(dto.image),
      newItemTranslations: this.mapTranslations(dto),
      publishStatus: dto.publishStatus,
      salesOrganizations: dto.salesOrganizations?.map(org => ({
        id: org.id,
        name: org.name,
      })) ?? [],
      videoIframe: dto.videoIFrame,
    }
  }

  static mapAuthor(dto: NewsArticleDetailDto | NewsArticleIndexDto): NewsArticleDetailAuthor {
    return {
      uuid: dto.author.uuid as NewsAuthorUuid,
      firstName: dto.author.firstName,
      fullName: `${dto.author.firstName ?? ''} ${dto.author.lastName ?? ''}`,
      lastName: dto.author.lastName,
    }
  }

  static mapTranslations(dto: NewsArticleDetailDto): NewsArticleDetailTranslation[] {
    return dto.translations.map((item) => ({
      uuid: item.uuid as NewsTranslationUuid,
      title: item.title,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      content: item.content,
      language: item.language,
    }))
  }
}

export class NewsArticleUpdateTransformer {
  static toDto(form: NewsArticleForm): NewsArticleUpdateDto {
    return {
      imageUuid: form.image!.uuid,
      endDate: CalendarDateTransformer.toNullableDto(form.endDate),
      startDate: CalendarDateTransformer.toDto(form.startDate!),
      newsItemTranslations: form.newsItemTranslations
        .filter((item) => item.title !== null && item.content !== null)
        .map((item) => ({
          title: item.title!,
          content: item.content!,
          language: item.language as unknown as Locale,
        })),
      salesOrganizations: form.salesOrganizations.map((org) => ({
        id: org.id,
        name: org.name,
      })),
      videoIFrame: form.videoIFrame,
    }
  }
}

export class NewsArticleIndexTransformer {
  static fromDto(dto: NewsArticleIndexDto): NewsArticleIndex {
    return {
      uuid: dto.uuid as NewsArticleUuid,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      endDate: dto.endDate ? new Date(dto.endDate) as CalendarDate : null,
      startDate: dto.startDate ? new Date(dto.startDate) as CalendarDate : null,
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      author: NewsArticleDetailTransformer.mapAuthor(dto),
      newsItemTranslations: dto.translations.map((item) => ({
        uuid: item.uuid as NewsTranslationUuid,
        title: item.title,
        language: item.language,
      })),
      publishStatus: dto.publishStatus,
    }
  }
}

export class NewsArticleIndexPaginationTransformer {
  static toDto(pagination: NewsArticleIndexPagination): NewsArticleIndexPaginationDto {
    return {
      filter: pagination.filter,
      sort: pagination.sort,
    }
  }
}

export class DashboardNewsIndexTransformer {
  static fromDto(dto: DashboardNewsIndexDto): DashboardNewsIndex {
    return {
      uuid: dto.uuid as NewsUuid,
      image: S3FileTransformer.fromNullableDto(dto.image),
      translation: {
        title: dto.translation.title ?? '',
        content: dto.translation.content ?? [],
      },
      videoIFrame: dto.videoIFrame,
    }
  }
}

export class DashboardNewsDetailTransformer {
  static fromDto(dto: DashboardNewsDetailDto): DashboardNewsDetail {
    return {
      uuid: dto.uuid as NewsUuid,
      image: S3FileTransformer.fromNullableDto(dto.image),
      translation: {
        title: dto.translation.title ?? '',
        content: dto.translation.content ?? [],
      },
      videoIFrame: dto.videoIFrame,
    }
  }
}

export class DashboardNewsIndexPaginationTransformer {
  static toDto(pagination: DashboardNewsIndexPagination): DashboardNewsIndexPaginationDto {
    return {
      filter: { excludeNewsItemUuids: pagination.filter?.excludeNewsItemUuids ?? [] },
      sort: pagination.sort,
    }
  }
}
