import { z } from 'zod'

import {
  AnnouncementType,
  Locale,
} from '@/client'
import { calendarDateSchema } from '@/models/date/calendarDate.model'
import { tiptapJSONContentSchema } from '@/models/editor/tiptapJsonContent.model'
import { i18nPlugin } from '@/plugins/i18n.plugin'
import { DateUtil } from '@/utils/date.util'
import { ZodUtil } from '@/utils/zod'

const salesOrganizationSchema = z.object({
  id: z.string(),
  name: z.string(),
})

export const newsTranslationFormSchema = z.object({
  title: z.string().nullable(),
  content: tiptapJSONContentSchema.nullable(),
  language: z.nativeEnum(Locale),
})

export const newsAnnouncementCreateFormSchema = z.object({
  endDate: calendarDateSchema.nullable(),
  startDate: calendarDateSchema.nullable(),
  salesOrganizations: salesOrganizationSchema.array().default([]),
  translations: newsTranslationFormSchema.array(),
  type: z.nativeEnum(AnnouncementType),
}).superRefine((data, ctx) => {
  ZodUtil.validateField('startDate', z.date(), data.startDate, ctx)

  if (data.startDate && data.endDate && DateUtil.isAfter(data.startDate, data.endDate)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: i18nPlugin.global.t('module.news.update.validation.end_date_after_start_date'),
      path: [
        'endDate',
      ],
    })
  }

  for (const [
    index,
    translation,
  ] of data.translations.entries()) {
    if ((translation.title && !translation.content) || (!translation.title && translation.content)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: i18nPlugin.global.t('module.news.update.validation.title_and_content_required'),
        path: [
          'translations',
          index,
        ],
      })
    }
  }

  const validTranslations = data.translations.some((translation) => {
    return translation.title && translation.content
  })

  if (!validTranslations) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: i18nPlugin.global.t('module.news.update.validation.at_least_one_translation'),
      path: [
        'translations',
      ],
    })
  }
})

export type NewsAnnouncementCreateForm = z.infer<typeof newsAnnouncementCreateFormSchema>
export type NewsAnnouncementTranslationForm = z.infer<typeof newsTranslationFormSchema>
