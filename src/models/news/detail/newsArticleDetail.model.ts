import type {
  Locale,
  PublishStatus,
  SalesOrganization,
} from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import type { TiptapJSONContent } from '@/models/editor/tiptapJsonContent.model'
import type { NewsArticleUuid } from '@/models/news/newsArticleUuid.model'
import type { NewsAuthorUuid } from '@/models/news/newsAuthorUuid.model'
import type { NewsTranslationUuid } from '@/models/news/newsTranslationUuid.model'
import type { S3File } from '@/models/s3-file/s3File.model'

export interface NewsArticleDetailTranslation {
  uuid: NewsTranslationUuid
  title: string | null
  createdAt: CalendarDate
  updatedAt: CalendarDate
  content: TiptapJSONContent | null
  language: Locale
}

export interface NewsArticleDetailAuthor {
  uuid: NewsAuthorUuid
  firstName: string | null
  fullName: string
  lastName: string | null
}

export interface NewsArticleDetail {
  uuid: NewsArticleUuid
  createdAt: CalendarDate
  endDate: CalendarDate | null
  startDate: CalendarDate | null
  updatedAt: CalendarDate
  author: NewsArticleDetailAuthor
  image: S3File | null
  newItemTranslations: NewsArticleDetailTranslation[]
  publishStatus: PublishStatus | null
  salesOrganizations: SalesOrganization[]
  videoIframe: string | null
}
