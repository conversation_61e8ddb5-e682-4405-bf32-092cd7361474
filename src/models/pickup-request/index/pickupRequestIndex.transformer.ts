import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { PickupRequestIndex } from '@/models/pickup-request/index/pickupRequestIndex.model'
import type { PickupRequestIndexDto } from '@/models/pickup-request/index/pickupRequestIndexDto.model'
import type { PickupRequestIndexPagination } from '@/models/pickup-request/index/pickupRequestIndexPagination.model'
import type { PickupRequestIndexPaginationDto } from '@/models/pickup-request/index/pickupRequestIndexPaginationDto.model'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'

export class PickupRequestIndexPaginationTransformer {
  static toDto(pagination: PickupRequestIndexPagination): PickupRequestIndexPaginationDto {
    return {
      filter: pagination.filter,
      sort: pagination.sort,
    }
  }
}

export class PickupRequestIndexTransformer {
  static fromDto(dto: PickupRequestIndexDto): PickupRequestIndex {
    return {
      uuid: dto.uuid as PickupRequestUuid | null,
      customerId: dto.customerId,
      pickupAddressId: dto.pickUpAddressId,
      wasteProducerId: dto.wasteProducerId,
      confirmedTransportDate: dto.confirmedTransportDate
        ? CalendarDateTransformer.fromDto(dto.confirmedTransportDate)
        : null,
      requestedEndDate: dto.requestedEndDate
        ? CalendarDateTransformer.fromDto(dto.requestedEndDate)
        : null,
      requestedStartDate: dto.requestedStartDate
        ? CalendarDateTransformer.fromDto(dto.requestedStartDate)
        : null,
      isHazardous: dto.isHazardous,
      isTransportByIndaver: dto.isTransportByIndaver,
      accountManager: dto.accountManager,
      containerNumber: dto.containerNumber,
      contractItem: dto.contractItem,
      contractNumber: dto.contractNumber,
      costCenter: dto.costCenter,
      customerName: dto.customerName,
      customerReference: dto.customerReference,
      dateOfRequest: dto.dateOfRequest
        ? CalendarDateTransformer.fromDto(dto.dateOfRequest)
        : null,
      deliveryInfo: dto.deliveryInfo,
      disposalCertificateNumber: dto.disposalCertificateNumber,
      ewc: dto.ewc,
      materialAnalysis: dto.materialAnalysis,
      nameInstallation: dto.nameInstallation,
      nameOfApplicant: dto.nameOfApplicant,
      orderNumber: dto.orderNumber,
      pickupAddressName: dto.pickUpAddressName,
      requestNumber: dto.requestNumber,
      salesOrder: dto.salesOrder,
      status: dto.status,
      tfsNumber: dto.tfsNumber,
      transportMode: dto.transportMode,
      treatmentCenterName: dto.treatmentCenterName,
      wasteMaterial: dto.wasteMaterial,
      wasteProducerName: dto.wasteProducerName,
    }
  }
}
