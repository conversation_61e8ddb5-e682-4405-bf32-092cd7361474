import { CustomerIndexTransformer } from '@/models/customer/customer.transformer'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { PickUpAddressIndexDto } from '@/models/pick-up-address/index/pickUpAddressIndexDto.model'
import { PickUpAddressIndexTransformer } from '@/models/pick-up-address/pickUpAddress.transformer'
import type {
  PickupRequestDetail,
  PickupRequestDetailCustomerAndLocationStep,
  PickupRequestDetailPlanningStep,
  PickupRequestDetailPoNumberAndCostCenterRequired,
  PickupRequestDetailSubmitStep,
  PickupRequestDetailWasteStep,
  PickupRequestPackagingRequestStep,
} from '@/models/pickup-request/detail/pickupRequestDetail.model'
import type {
  PickupRequestDetailDto,
  PickupRequestDetailPoNumberAndCostCenterRequiredDto,
} from '@/models/pickup-request/detail/pickupRequestDetailDto.model'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import { WasteProducerIndexTransformer } from '@/models/waste-producer/wasteProducer.transformer'

export class PickupRequestDetailTransformer {
  static fromDto(dto: PickupRequestDetailDto): PickupRequestDetail {
    return {
      uuid: dto.uuid as PickupRequestUuid,
      createdAt: CalendarDateTransformer.fromDto(dto.createdAt),
      updatedAt: CalendarDateTransformer.fromDto(dto.updatedAt),
      requestNumber: null,
      status: null,
      ...this.mapCustomerAndLocationStep(dto),
      ...this.mapWasteStep(dto),
      ...this.mapPlanningStep(dto),
      ...this.mapSubmitStep(dto),
      ...this.mapPackagingRequestStep(dto),
    }
  }

  static mapCustomerAndLocationStep(dto: PickupRequestDetailDto): PickupRequestDetailCustomerAndLocationStep {
    return {
      customer: CustomerIndexTransformer.fromNullableDto(dto.customer),
      pickupAddresses: dto.pickUpAddresses.map(
        (address: PickUpAddressIndexDto) => PickUpAddressIndexTransformer.fromDto(address),
      ),
      wasteProducer: WasteProducerIndexTransformer.fromNullableDto(dto.wasteProducer),
    }
  }

  static mapPackagingRequestStep(dto: PickupRequestDetailDto): PickupRequestPackagingRequestStep {
    return { packagingRequestMaterials: dto.packagingRequestMaterials }
  }

  static mapPlanningStep(dto: PickupRequestDetailDto): PickupRequestDetailPlanningStep {
    return {
      endDate: CalendarDateTransformer.fromNullableDto(dto.endDate),
      startDate: CalendarDateTransformer.fromNullableDto(dto.startDate),
      additionalFiles: S3FileArrayTransformer.fromDtoArray(dto.additionalFiles),
      remarks: dto.remarks,
    }
  }

  static mapSubmitStep(dto: PickupRequestDetailDto): PickupRequestDetailSubmitStep {
    return {
      contacts: dto.sendCopyToContacts.map((contact) => ({
        email: contact.email,
        firstName: contact.firstName,
        lastName: contact.lastName,
      })),
    }
  }

  static mapWasteStep(dto: PickupRequestDetailDto): PickupRequestDetailWasteStep {
    return {
      isReturnPackaging: dto.isReturnPackaging,
      isTransportByIndaver: dto.isTransportByIndaver,
      materials: dto.materials.map((material) => {
        return {
          contractLineId: material.contractLineId,
          customerId: material.customerId,
          endTreatmentCenterId: material.endTreatmentCenterId,
          pickupAddressId: material.pickUpAddressId,
          wasteProducerId: material.wasteProducerId,
          isContainerCovered: material.isContainerCovered,
          isHazardous: material.isHazardous,
          asn: material.asn,
          containerNumber: material.containerNumber,
          containerTransportType: material.containerTransportType,
          containerType: material.containerType,
          containerVolumeSize: material.containerVolumeSize,
          contractItem: material.contractItem,
          contractNumber: material.contractNumber,
          costCenter: material.costCenter,
          customerName: material.customerName,
          customerReference: material.customerReference,
          deliveryInfo: material.deliveryInfo,
          endTreatmentCenterName: material.endTreatmentCenterName,
          esnNumber: material.esnNumber,
          estimatedWeightOrVolumeUnit: material.estimatedWeightOrVolumeUnit,
          estimatedWeightOrVolumeValue: material.estimatedWeightOrVolumeValue,
          ewcCode: material.ewcCode,
          hazardInducers: material.hazardInducers,
          installationName: material.installationName,
          materialAnalysis: material.materialAnalysis,
          materialNumber: material.materialNumber,
          materialType: material.materialType,
          packaged: material.packaged,
          packagingIndicator: material.packagingIndicator,
          packagingType: material.packagingType,
          pickupAddressName: material.pickUpAddressName,
          poNumber: material.poNumber,
          position: material.position,
          processCode: material.processCode,
          quantityContainers: material.quantityContainers,
          quantityLabels: material.quantityLabels,
          quantityPackages: material.quantityPackages,
          quantityPallets: material.quantityPallets,
          reconciliationNumber: material.reconciliationNumber,
          remarks: material.remarks,
          serialNumber: material.serialNumber,
          tankerType: material.tankerType,
          tcNumber: material.tcNumber,
          tfs: material.tfs,
          tfsNumber: material.tfsNumber,
          treatmentCenterName: material.treatmentCenterName,
          unNumber: material.unNumber !== null
            ? {
                isHazardous: material.unNumberHazardous,
                dangerLabel1: material.dangerLabel1,
                dangerLabel2: material.dangerLabel2,
                dangerLabel3: material.dangerLabel3,
                description: material.unNumberDescription,
                number: material.unNumber,
                packingGroup: material.packingGroup,
              }
            : null,
          wasteMaterial: material.wasteMaterial,
          wasteProducerName: material.wasteProducerName,
        }
      }),
      packagingRemark: dto.packagingRemark,
      totalQuantityPallets: dto.totalQuantityPallets,
      transportMode: dto.transportMode,
    }
  }
}

export class PickupRequestDetailPoNumberAndCostCenterRequiredTransformer {
  static fromDto(dto: PickupRequestDetailPoNumberAndCostCenterRequiredDto):
  PickupRequestDetailPoNumberAndCostCenterRequired {
    return {
      isCostCenterRequired: dto.isCostCenterRequired,
      isPoNumberRequired: dto.isPoNumberRequired,
    }
  }
}
