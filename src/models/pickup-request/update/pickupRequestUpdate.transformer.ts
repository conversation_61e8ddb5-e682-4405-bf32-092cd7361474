import type { PackingGroup } from '@/client'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer'
import type { CalendarDateTimeDto } from '@/models/date/calendarDateTimeDto.model'
import { CalendarTimeTransformer } from '@/models/date/calendarTime.transformer'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import { PickupRequestValidationUtil } from '@/models/pickup-request/pickupRequestValidation.util'
import type { PickupRequestSubmitDto } from '@/models/pickup-request/update/pickupRequestSubmitDto.model'
import type {
  PickupRequestCustomerAndLocationStepUpdateDto,
  PickupRequestPackagingRequestStepUpdateDto,
  PickupRequestPackagingStepUpdateDto,
  PickupRequestPlanningStepUpdateDto,
  PickupRequestStartDateUpdateDto,
  PickupRequestUpdateDto,
  PickupRequestWasteStepUpdateDto,
} from '@/models/pickup-request/update/pickupRequestUpdateDto.model'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestStartDateForm } from '@/models/pickup-request/update/steps/pickupRequestStartDateForm.model'
import type { PickupRequestSubmitRequestForm } from '@/models/pickup-request/update/submit/pickupRequestSubmitForm.model'
import type { PickupRequestSubmitResponse } from '@/models/pickup-request/update/submit/pickupRequestSubmitResponse.model'
import type { PickupRequestSubmitResponseDto } from '@/models/pickup-request/update/submit/pickupRequestSubmitResponseDto.model'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import { StringUtil } from '@/utils/string.util'

export class PickupRequestUpdateTransformer {
  static mapCustomerAndLocationStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestCustomerAndLocationStepUpdateDto {
    return {
      customerId: form.customer?.id ?? null,
      wasteProducerId: form.wasteProducer?.id ?? null,
      pickUpAddressIds: form.pickupAddresses?.map((address) => address.id) ?? [],
    }
  }

  static mapPackagingRequestStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestPackagingRequestStepUpdateDto {
    const pickupRequestValidationUtil = new PickupRequestValidationUtil(form)

    return {
      packagingRequestMaterials: pickupRequestValidationUtil.isPackagingRequestMaterialsAllowed
        ? form.packagingRequestMaterials?.map((material) => ({
            contractLineId: material.contractLineId,
            isSales: material.isSales,
            contractItem: material.contractItem,
            contractNumber: material.contractNumber,
            costCenter: StringUtil.trimOrNull(material.costCenter),
            materialNumber: material.materialNumber,
            poNumber: StringUtil.trimOrNull(material.poNumber),
            quantity: material.quantity,
            wasteMaterial: material.wasteMaterial,
          }))
        : [],
    }
  }

  static mapPackagingStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestPackagingStepUpdateDto {
    const pickupRequestValidationUtil = new PickupRequestValidationUtil(form)

    let packagingRemark

    if (pickupRequestValidationUtil.isPackagingRemarkAllowed) {
      packagingRemark = form.packagingRemark?.trim() === '' ? null : form.packagingRemark
    }
    else {
      packagingRemark = null
    }

    return {
      isReturnPackaging: pickupRequestValidationUtil.isReturnPackagingAllowed
        ? form.isReturnPackaging
        : null,
      packagingRemark,
      totalQuantityPallets: pickupRequestValidationUtil.isPackagingRemarkAllowed
        ? form.totalQuantityPallets
        : null,
    }
  }

  static mapPlanningStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestPlanningStepUpdateDto {
    let endDate = null

    if (form.range?.until) {
      endDate = CalendarDateTransformer.toNullableDto(form.range.until)
    }
    else if (form.range?.from) {
      endDate = CalendarDateTransformer.toNullableDto(form.range.from)
    }

    return {
      endDate,
      startDate: form.range?.from ? CalendarDateTransformer.toNullableDto(form.range?.from) : null,
      additionalFiles: S3FileArrayTransformer.toArrayDto(form.additionalFiles ?? []),
      remarks: form.remarks,
    }
  }

  static mapStartDates(
    form: PickupRequestStartDateForm,
  ): PickupRequestStartDateUpdateDto {
    return {
      startDate: form.startDate ? CalendarDateTransformer.toNullableDto(form.startDate) : null,
      startTime: form.startTime ? CalendarTimeTransformer.toDto(form.startTime) : null,
    }
  }

  static mapWasteStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestWasteStepUpdateDto {
    const pickupRequestValidationUtil = new PickupRequestValidationUtil(form)

    return {
      isTransportByIndaver: form.isTransportByIndaver,
      materials: form.materials?.map((material) => ({
        contractLineId: material.contractLineId,
        pickUpAddressId: material.pickupAddressId,
        isContainerCovered: pickupRequestValidationUtil.isMaterialContainerCoveredAllowed
          ? material.isContainerCovered
          : null,
        isHazardous: material.isHazardous,
        asn: material.asn,
        containerNumber: pickupRequestValidationUtil.isMaterialContainerNumberAllowed
          ? material.containerNumber
          : null,
        containerTransportType: pickupRequestValidationUtil.isMaterialContainerTransportTypeAllowed
          ? material.containerTransportType
          : null,
        containerType: pickupRequestValidationUtil.isMaterialContainerTypeAllowed
          ? material.containerType?.name
          : null,
        containerVolumeSize: material.containerVolumeSize,
        contractItem: material.contractItem,
        contractNumber: material.contractNumber,
        costCenter: StringUtil.trimOrNull(material.costCenter),
        customerReference: material.customerReference,
        dangerLabel1: material.unNumber?.dangerLabel1 ?? null,
        dangerLabel2: material.unNumber?.dangerLabel2 ?? null,
        dangerLabel3: material.unNumber?.dangerLabel3 ?? null,
        estimatedWeightOrVolumeUnit: material.estimatedWeightOrVolumeUnit,
        estimatedWeightOrVolumeValue: material.estimatedWeightOrVolumeValue,
        ewcCode: material.ewcCode,
        hazardInducers: pickupRequestValidationUtil.isHazardInducersAllowed ? material.hazardInducers : null,
        materialNumber: material.materialNumber,
        packagingType: pickupRequestValidationUtil.isMaterialPackagingTypeAllowed
          ? material.packagingType?.name
          : null,
        packingGroup: material.unNumber?.packingGroup
          ? StringUtil.trimOrNull(material.unNumber?.packingGroup) as PackingGroup
          : null,
        poNumber: StringUtil.trimOrNull(material.poNumber),
        quantityContainers: material.quantityContainers,
        quantityLabels: pickupRequestValidationUtil.isMaterialQuantityLabelsAllowed
          ? material.quantityLabels
          : null,
        quantityPackages: pickupRequestValidationUtil.isMaterialQuantityPackagesAllowed
          ? material.quantityPackages
          : null,
        quantityPallets: pickupRequestValidationUtil.isMaterialQuantityPalletsAllowed
          ? material.quantityPallets
          : null,
        reconciliationNumber: pickupRequestValidationUtil.isReconciliationNumberAllowed
          ? material.reconciliationNumber
          : null,
        serialNumber: pickupRequestValidationUtil.isSerialNumberAllowed(material)
          ? material.serialNumber
          : null,
        tankerType: pickupRequestValidationUtil.isMaterialTankerTypeAllowed
          ? material.tankerType
          : null,
        tcNumber: material.tcNumber,
        tfs: material.tfs,
        tfsNumber: pickupRequestValidationUtil.isMaterialTfsNumberAllowed(material)
          ? material.tfsNumber
          : null,
        unNumber: material.unNumber?.number ? StringUtil.trimOrNull(material.unNumber.number) : null,
        unNumberDescription: material.unNumber?.description ?? null,
        unNumberHazardous: material.unNumber?.isHazardous ?? null,
        wasteMaterial: material.wasteMaterial,
      })),
      transportMode: form.transportMode,
    }
  }

  static toDto(form: Partial<PickupRequestUpdateForm>): PickupRequestUpdateDto {
    return {
      ...this.mapCustomerAndLocationStep(form),
      ...this.mapWasteStep(form),
      ...this.mapPackagingStep(form),
      ...this.mapPlanningStep(form),
      ...this.mapPackagingRequestStep(form),
    }
  }

  static toWeeklyPlanningDto(form: Partial<PickupRequestUpdateForm>): PickupRequestUpdateDto {
    return {
      ...this.mapWasteStep(form),
      ...this.mapPackagingStep(form),
      ...this.mapStartDates(form),
    }
  }
}

export class PickupRequestSubmitFormTransformer {
  static toDto(form: PickupRequestSubmitRequestForm): PickupRequestSubmitDto {
    return { sendCopyToContacts: form.contacts.filter((contact) => StringUtil.trimOrNull(contact.email) !== null) }
  }
}

export class PickupRequestSubmitResponseTransformer {
  static fromDto(dto: PickupRequestSubmitResponseDto): PickupRequestSubmitResponse {
    return {
      uuid: dto.uuid as PickupRequestUuid,
      submittedOn: CalendarDateTimeTransformer.fromDto(dto.submittedOn as CalendarDateTimeDto),
      requestNumber: dto.requestNumber,
    }
  }
}
