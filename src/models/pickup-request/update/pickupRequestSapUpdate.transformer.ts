import type { PickupRequestSapSubmitDto } from '@/models/pickup-request/update/pickupRequestSapSubmitDto.model'
import type { PickupRequestSapUpdateDto } from '@/models/pickup-request/update/pickupRequestSapUpdateDto.model'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestSubmitRequestForm } from '@/models/pickup-request/update/submit/pickupRequestSubmitForm.model'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import { StringUtil } from '@/utils/string.util'

export class PickupRequestSapUpdateTransformer {
  static toDto(form: Partial<PickupRequestUpdateForm>): PickupRequestSapUpdateDto {
    return {
      additionalFiles: S3FileArrayTransformer.toArrayDto(form.additionalFiles ?? []),
      materials: form.materials?.map((material) => ({
        costCenter: StringUtil.trimOrNull(material.costCenter),
        poNumber: StringUtil.trimOrNull(material.poNumber),
        position: material.position ?? '',
      })),
      remarks: form.remarks,
    }
  }
}

export class PickupRequestSapSubmitFormTransformer {
  static toDto(form: PickupRequestSubmitRequestForm): PickupRequestSapSubmitDto {
    return { contacts: form.contacts.filter((contact) => contact.email !== '') }
  }
}
