import { CustomerIndexTransformer } from '@/models/customer/customer.transformer'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { PickUpAddressIndexDto } from '@/models/pick-up-address/index/pickUpAddressIndexDto.model'
import { PickUpAddressIndexTransformer } from '@/models/pick-up-address/pickUpAddress.transformer'
import type { PickupRequestDetail } from '@/models/pickup-request/detail/pickupRequestDetail.model'
import type { PickupRequestSapDetail } from '@/models/pickup-request/sap-detail/pickupRequestSapDetail.model'
import type { PickupRequestSapDetailDto } from '@/models/pickup-request/sap-detail/pickupRequestSapDetailDto.model'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import { WasteProducerIndexTransformer } from '@/models/waste-producer/wasteProducer.transformer'

export class PickupRequestSapDetailTransformer {
  static fromDto(dto: PickupRequestSapDetailDto): PickupRequestSapDetail {
    return {
      endDate: CalendarDateTransformer.fromNullableDto(dto.endDate),
      startDate: CalendarDateTransformer.fromNullableDto(dto.startDate),
      isReturnPackaging: dto.isReturnPackaging,
      isTransportByIndaver: dto.isTransportByIndaver,
      additionalFiles: S3FileArrayTransformer.fromDtoArray(dto.additionalFiles),
      contacts: dto.sendCopyToContacts.map((contact) => ({
        email: contact.email,
        firstName: contact.firstName,
        lastName: contact.lastName,
      })),
      customer: CustomerIndexTransformer.fromNullableDto(dto.customer),
      materials: dto.materials.map((material) => {
        return {
          contractLineId: material.contractLineId,
          customerId: material.customerId,
          endTreatmentCenterId: material.endTreatmentCenterId,
          pickupAddressId: material.pickUpAddressId,
          wasteProducerId: material.wasteProducerId,
          isContainerCovered: material.isContainerCovered,
          isHazardous: material.isHazardous,
          asn: material.asn,
          containerNumber: material.containerNumber,
          containerTransportType: material.containerTransportType,
          containerType: material.containerType,
          containerVolumeSize: material.containerVolumeSize,
          contractItem: material.contractItem,
          contractNumber: material.contractNumber,
          costCenter: material.costCenter,
          customerName: material.customerName,
          customerReference: material.customerReference,
          deliveryInfo: material.deliveryInfo,
          endTreatmentCenterName: material.endTreatmentCenterName,
          esnNumber: material.esnNumber,
          estimatedWeightOrVolumeUnit: material.estimatedWeightOrVolumeUnit,
          estimatedWeightOrVolumeValue: material.estimatedWeightOrVolumeValue,
          ewcCode: material.ewcCode,
          hazardInducers: material.hazardInducers,
          installationName: material.installationName,
          materialAnalysis: material.materialAnalysis,
          materialNumber: material.materialNumber,
          materialType: material.materialType,
          packaged: material.packaged,
          packagingIndicator: material.packagingIndicator,
          packagingType: material.packagingType,
          pickupAddressName: material.pickUpAddressName,
          poNumber: material.poNumber,
          position: material.position,
          processCode: material.processCode,
          quantityContainers: material.quantityContainers,
          quantityLabels: material.quantityLabels,
          quantityPackages: material.quantityPackages,
          quantityPallets: material.quantityPallets,
          reconciliationNumber: material.reconciliationNumber,
          remarks: material.remarks,
          serialNumber: material.serialNumber,
          tankerType: material.tankerType,
          tcNumber: material.tcNumber,
          tfs: material.tfs,
          tfsNumber: material.tfsNumber,
          treatmentCenterName: material.treatmentCenterName,
          unNumber: material.unNumber !== null
            ? {
                isHazardous: material.unNumberHazardous,
                dangerLabel1: material.dangerLabel1,
                dangerLabel2: material.dangerLabel2,
                dangerLabel3: material.dangerLabel3,
                description: material.unNumberDescription,
                number: material.unNumber,
                packingGroup: material.packingGroup,
              }
            : null,
          wasteMaterial: material.wasteMaterial,
          wasteProducerName: material.wasteProducerName,
        }
      }),
      packagingRemark: dto.packagingRemark,
      packagingRequestMaterials: dto.packagingRequestMaterials,
      pickupAddresses: dto.pickUpAddresses.map(
        (address: PickUpAddressIndexDto) => PickUpAddressIndexTransformer.fromDto(address),
      ),
      remarks: dto.remarks,
      requestNumber: dto.requestNumber,
      status: dto.status,
      totalQuantityPallets: dto.totalQuantityPallets,
      transportMode: dto.transportMode,
      wasteProducer: WasteProducerIndexTransformer.fromNullableDto(dto.wasteProducer),
    }
  }

  static toDetail(sap: PickupRequestSapDetail): PickupRequestDetail {
    return {
      uuid: null,
      createdAt: sap.startDate,
      endDate: sap.endDate,
      startDate: sap.startDate,
      updatedAt: sap.startDate,
      isReturnPackaging: sap.isReturnPackaging,
      isTransportByIndaver: sap.isTransportByIndaver,
      additionalFiles: sap.additionalFiles,
      contacts: sap.contacts.map((contact) => ({
        email: contact.email,
        firstName: contact.firstName,
        lastName: contact.lastName,
      })),
      customer: CustomerIndexTransformer.fromNullableDto(sap.customer),
      materials: sap.materials.map((material) => {
        return {
          contractLineId: material.contractLineId,
          customerId: material.customerId,
          endTreatmentCenterId: material.endTreatmentCenterId,
          pickupAddressId: material.pickupAddressId,
          wasteProducerId: material.wasteProducerId,
          isContainerCovered: material.isContainerCovered,
          isHazardous: material.isHazardous,
          asn: material.asn,
          containerNumber: material.containerNumber,
          containerTransportType: material.containerTransportType,
          containerType: material.containerType,
          containerVolumeSize: material.containerVolumeSize,
          contractItem: material.contractItem,
          contractNumber: material.contractNumber,
          costCenter: material.costCenter,
          customerName: material.customerName,
          customerReference: material.customerReference,
          deliveryInfo: material.deliveryInfo,
          endTreatmentCenterName: material.endTreatmentCenterName,
          esnNumber: material.esnNumber,
          estimatedWeightOrVolumeUnit: material.estimatedWeightOrVolumeUnit,
          estimatedWeightOrVolumeValue: material.estimatedWeightOrVolumeValue,
          ewcCode: material.ewcCode,
          hazardInducers: material.hazardInducers,
          installationName: material.installationName,
          materialAnalysis: material.materialAnalysis,
          materialNumber: material.materialNumber,
          materialType: material.materialType,
          packaged: material.packaged,
          packagingIndicator: material.packagingIndicator,
          packagingType: material.packagingType,
          pickupAddressName: material.pickupAddressName,
          poNumber: material.poNumber,
          position: material.position,
          processCode: material.processCode,
          quantityContainers: material.quantityContainers,
          quantityLabels: material.quantityLabels,
          quantityPackages: material.quantityPackages,
          quantityPallets: material.quantityPallets,
          reconciliationNumber: material.reconciliationNumber,
          remarks: material.remarks,
          serialNumber: material.serialNumber,
          tankerType: material.tankerType,
          tcNumber: material.tcNumber,
          tfs: material.tfs,
          tfsNumber: material.tfsNumber,
          treatmentCenterName: material.treatmentCenterName,
          unNumber: material.unNumber,
          wasteMaterial: material.wasteMaterial,
          wasteProducerName: material.wasteProducerName,
        }
      }),
      packagingRemark: sap.packagingRemark,
      packagingRequestMaterials: sap.packagingRequestMaterials,
      pickupAddresses: sap.pickupAddresses.map(
        (address: PickUpAddressIndexDto) => PickUpAddressIndexTransformer.fromDto(address),
      ),
      remarks: sap.remarks,
      requestNumber: sap.requestNumber,
      status: sap.status,
      totalQuantityPallets: sap.totalQuantityPallets,
      transportMode: sap.transportMode,
      wasteProducer: WasteProducerIndexTransformer.fromNullableDto(sap.wasteProducer),
    }
  }
}
