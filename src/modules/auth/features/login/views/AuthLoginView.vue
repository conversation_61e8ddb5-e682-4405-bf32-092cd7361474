<script setup lang="ts">
import { useRouteQuery } from '@vueuse/router'
import {
  useVcToast,
  VcButton,
  VcIcon,
} from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import loginImage from '@/assets/images/login-image-construction.jpg'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { useAuthStore } from '@/stores/auth.store'

const authStore = useAuthStore()

const isSigningIn = ref<boolean>(false)

const hadRolesError = useRouteQuery<string>('roleError')

const i18n = useI18n()
const toast = useVcToast()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.auth.login.page_title'))

async function onSignInWithZitadel(): Promise<void> {
  isSigningIn.value = true

  try {
    const loginUrl = await authStore.getLoginUrl()

    window.location.replace(loginUrl)
  }
  catch {
    isSigningIn.value = false

    toast.error({
      title: i18n.t('module.auth.login.error.title'),
      description: i18n.t('module.auth.login.error.description'),
    })
  }
}

if (hadRolesError.value) {
  toast.error({
    title: i18n.t('module.auth.roles.error.title'),
    description: i18n.t('module.auth.roles.error.description'),
  })
}
</script>

<template>
  <div class="bg-primary grid h-full grid-cols-[1fr_1.8fr]">
    <div class="relative size-full overflow-hidden">
      <div class="px-3xl py-8xl absolute inset-0 flex flex-col justify-center">
        <div class="z-10 mx-auto w-full max-w-80">
          <div>
            <Motion
              :initial="{
                opacity: 0,
                filter: 'blur(16px)',
                scale: 0.95,
              }"
              :animate="{
                opacity: 1,
                filter: 'blur(0)',
                scale: 1,
              }"
              :transition="{
                duration: 0.75,
                ease: 'easeInOut',
              }"
              tabindex="-1"
            >
              <img
                src="@/assets/svgs/logo-dark.svg"
                alt="Logo"
                class="mx-auto w-full max-w-28"
              >
            </Motion>

            <Motion
              :initial="{
                opacity: 0,
                y: 50,
                filter: 'blur(16px)',
                scale: 0.95,
              }"
              :animate="{
                opacity: 1,
                y: 0,
                filter: 'blur(0)',
                scale: 1,
              }"
              :transition="{
                duration: 0.75,
                ease: 'easeInOut',
              }"
              tabindex="-1"
            >
              <h1
                class="
                  text-display-sm mt-3xl text-primary text-center font-bold
                "
              >
                {{ i18n.t('module.auth.login.title') }}
              </h1>
            </Motion>

            <Motion
              :initial="{
                opacity: 0,
                y: 50,
                filter: 'blur(16px)',
                scale: 0.95,
              }"
              :animate="{
                opacity: 1,
                y: 0,
                filter: 'blur(0)',
                scale: 1,
              }"
              :transition="{
                duration: 0.75,
                delay: 0.15,
                ease: 'easeInOut',
              }"
              tabindex="-1"
            >
              <p class="mt-lg text-tertiary relative text-center">
                {{ i18n.t('module.auth.login.description') }}
              </p>
            </Motion>

            <div class="mt-4xl gap-y-md flex flex-col">
              <Motion
                :initial="{
                  opacity: 0,
                  y: 50,
                  filter: 'blur(16px)',
                  scale: 0.95,
                }"
                :animate="{
                  opacity: 1,
                  y: 0,
                  filter: 'blur(0)',
                  scale: 1,
                }"
                :transition="{
                  duration: 0.75,
                  delay: 0.3,
                  ease: 'easeInOut',
                }"
                tabindex="-1"
              >
                <VcButton
                  :is-loading="isSigningIn"
                  :test-id="TEST_ID.AUTH.LOGIN.SUBMIT_BUTTON"
                  class="mx-auto"
                  icon-right="arrowRight"
                  @click="onSignInWithZitadel"
                >
                  {{ i18n.t('module.auth.login.sign_in') }}
                </VcButton>
              </Motion>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-xl size-full">
      <div class="relative size-full overflow-hidden rounded-2xl">
        <div
          class="
            gap-md absolute -top-[15%] -left-[10%] grid h-[130%] w-[130%]
            min-w-[800px] grid-cols-4 grid-rows-4 rounded-2xl
          "
        >
          <div
            v-for="i in 16"
            :key="i"
            :style="{
              backgroundImage: `url(${loginImage})`,
              backgroundPosition: '35vw center',
              backgroundAttachment: 'fixed',
              backgroundSize: '80%',
              backgroundRepeat: 'no-repeat',
            }"
            class="overflow-hidden rounded-4xl"
          >
            <div
              v-if="i === 10"
              class="
                bg-accent-primary-500 p-3xl flex size-full flex-col justify-end
              "
            >
              <p
                class="
                  text-display-sm leading-10 font-thin text-white
                  xl:leading-6xl xl:text-[36px]
                "
              >
                {{ i18n.t('module.auth.login.leading_text') }}
              </p>
            </div>

            <a
              v-else-if="i === 11"
              href="https://indaver.com/"
              target="_blank"
              referrerpolicy="no-referrer"
              class="
                bg-brand-500 group p-3xl flex size-full flex-col justify-end
                duration-200
                hover:brightness-90
              "
            >
              <!-- eslint-disable @intlify/vue-i18n/no-v-html -->
              <p
                class="
                  text-display-sm leading-10 font-thin text-white duration-200
                  xl:leading-6xl xl:text-[36px]
                  group-hover:-translate-y-4
                "
                v-html="i18n.t('module.auth.login.visit_website')"
              />

              <VcIcon
                icon="arrowRight"
                class="
                  mt-md size-6 h-0 text-white duration-200
                  group-hover:h-6
                "
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
