export interface SalesOrganization {
  id: string
  name: string
}

export interface SapSalesOrganizationResponse {
  value: Array<{
    SalesOrganization: string
    SalesOrganizationName: string
  }>
}

export class SapSalesOrganizationService {
  static async search(query: string): Promise<SalesOrganization[]> {
    try {
      // Get the SAP OData URL from environment
      const sapUrl = import.meta.env.VITE_SAP_SALES_ORG_ODATA_URL

      if (!sapUrl) {
        console.warn('SAP_SALES_ORG_ODATA_URL not configured')
        return []
      }

      // Build the OData query with search filter
      const searchFilter = query ? `$filter=contains(SalesOrganizationName,'${encodeURIComponent(query)}')` : ''
      const url = searchFilter ? `${sapUrl}&${searchFilter}` : sapUrl

      console.log('🔍 Attempting SAP request to:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.error(`❌ SAP API request failed: ${response.status} ${response.statusText}`)
        throw new Error(`SAP API request failed: ${response.status} ${response.statusText}`)
      }

      const data: SapSalesOrganizationResponse = await response.json()
      console.log('✅ SAP request successful, got', data.value?.length || 0, 'results')

      return data.value.map((item) => ({
        id: item.SalesOrganization,
        name: item.SalesOrganizationName,
      }))
    } catch (error) {
      console.error('❌ Error fetching sales organizations:', error)

      // Check if it's a CORS error
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.error('🚫 This looks like a CORS error - SAP server is blocking cross-origin requests')
      }

      // Return empty array on error to prevent form from breaking
      return []
    }
  }

  static async getAll(): Promise<SalesOrganization[]> {
    return this.search('')
  }
}
