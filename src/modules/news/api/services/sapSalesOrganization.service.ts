export interface SalesOrganization {
  id: string
  name: string
}

export interface SapSalesOrganizationResponse {
  value: Array<{
    SalesOrganization: string
    SalesOrganizationName: string
  }>
}

export class SapSalesOrganizationService {
  static async search(query: string): Promise<SalesOrganization[]> {
    try {
      const sapUrl = import.meta.env.VITE_SAP_SALES_ORG_ODATA_URL

      if (!sapUrl) {
        console.warn('SAP_SALES_ORG_ODATA_URL not configured')

        return []
      }

      const searchFilter = query ? `$filter=contains(SalesOrganizationName,'${encodeURIComponent(query)}')` : ''
      const url = searchFilter ? `${sapUrl}&${searchFilter}` : sapUrl

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        method: 'GET',
      })

      if (!response.ok) {
        console.error(`❌ SAP API request failed: ${response.status} ${response.statusText}`)

        throw new Error(`SAP API request failed: ${response.status} ${response.statusText}`)
      }

      const data: SapSalesOrganizationResponse = await response.json()

      return data.value.map((item) => ({
        id: item.SalesOrganization,
        name: item.SalesOrganizationName,
      }))
    }
    catch (error) {
      console.error('❌ Error fetching sales organizations:', error)

      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.error('🚫 This looks like a CORS error - SAP server is blocking cross-origin requests')
      }

      return []
    }
  }

  static async getAll(): Promise<SalesOrganization[]> {
    return this.search('')
  }
}
