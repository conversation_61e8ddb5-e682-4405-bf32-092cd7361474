import type { SalesOrganization } from '@/client'
import { viewSalesOrganizationIndexV1 } from '@/client'

export class SalesOrganizationService {
  static async getAll(): Promise<SalesOrganization[]> {
    const response = await viewSalesOrganizationIndexV1()

    return response.data.items
  }

  static async search(query: string): Promise<SalesOrganization[]> {
    const response = await viewSalesOrganizationIndexV1({ query: { search: query } })

    return response.data.items
  }
}
