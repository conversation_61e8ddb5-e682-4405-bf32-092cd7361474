import type { SalesOrganization } from '@/client'
import { viewSalesOrganizationIndexV1 } from '@/client'

// Mock data for fallback when API is not available
const MOCK_SALES_ORGANIZATIONS: SalesOrganization[] = [
  {
    id: 'BE01',
    name: 'Indaver Belgium',
  },
  {
    id: 'NL01',
    name: 'Indaver Netherlands',
  },
  {
    id: 'UK01',
    name: 'Indaver UK',
  },
  {
    id: 'DE01',
    name: 'Indaver Germany',
  },
  {
    id: 'ES01',
    name: 'Indaver Spain',
  },
  {
    id: 'IE01',
    name: 'Indaver Ireland',
  },
]

export class SalesOrganizationService {
  static async getAll(): Promise<SalesOrganization[]> {
    try {
      const response = await viewSalesOrganizationIndexV1()

      return response.data.items
    }
    catch (error) {
      console.warn('Sales organizations API not available, using mock data:', error)

      return MOCK_SALES_ORGANIZATIONS
    }
  }

  static async search(query: string): Promise<SalesOrganization[]> {
    try {
      const response = await viewSalesOrganizationIndexV1({ query: { search: query } })

      return response.data.items
    }
    catch (error) {
      console.warn('Sales organizations API not available, using mock data:', error)

      // Filter mock data based on search query
      if (!query.trim()) {
        return MOCK_SALES_ORGANIZATIONS
      }

      return MOCK_SALES_ORGANIZATIONS.filter((org) =>
        org.name.toLowerCase().includes(query.toLowerCase())
        || org.id.toLowerCase().includes(query.toLowerCase()))
    }
  }
}
