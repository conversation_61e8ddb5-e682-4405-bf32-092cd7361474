import type { SalesOrganization } from '@/client'

export class SalesOrganizationService {
  static async getAll(): Promise<SalesOrganization[]> {
    // TODO: Replace with actual API call when backend endpoint is available
    // const response = await viewSalesOrganizationsV1()
    // return response.data.items
    
    // For now, return mock data that matches the SalesOrganization interface
    return [
      { id: 'BE01', name: 'Indaver Belgium' },
      { id: 'NL01', name: 'Indaver Netherlands' },
      { id: 'UK01', name: 'Indaver UK' },
      { id: 'DE01', name: 'Indaver Germany' },
      { id: 'ES01', name: 'Indaver Spain' },
      { id: 'IE01', name: 'Indaver Ireland' },
    ]
  }

  static async search(query: string): Promise<SalesOrganization[]> {
    // TODO: Replace with actual API call when backend endpoint is available
    // const response = await viewSalesOrganizationsV1({ 
    //   query: { search: query } 
    // })
    // return response.data.items
    
    // For now, filter mock data
    const allOrganizations = await this.getAll()
    
    if (!query.trim()) {
      return allOrganizations
    }
    
    return allOrganizations.filter(org => 
      org.name.toLowerCase().includes(query.toLowerCase()) ||
      org.id.toLowerCase().includes(query.toLowerCase())
    )
  }
}
