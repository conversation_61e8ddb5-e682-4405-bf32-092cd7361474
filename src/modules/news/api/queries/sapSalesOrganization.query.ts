import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { SalesOrganization } from '@/modules/news/api/services/sapSalesOrganization.service'
import { SapSalesOrganizationService } from '@/modules/news/api/services/sapSalesOrganization.service'

export function useSalesOrganizationSearchQuery(
  searchQuery: ComputedRef<string>,
): UseQueryReturnType<SalesOrganization[]> {
  return useQuery({
    queryFn: () => SapSalesOrganizationService.search(searchQuery.value),
    queryKey: { salesOrganizationSearch: { searchQuery } },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}
