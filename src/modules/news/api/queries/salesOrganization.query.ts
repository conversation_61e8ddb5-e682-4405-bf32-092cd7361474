import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { SalesOrganization } from '@/client'
import { SalesOrganizationService } from '@/modules/news/api/services/salesOrganization.service'

export function useSalesOrganizationSearchQuery(
  searchQuery: ComputedRef<string>,
): UseQueryReturnType<SalesOrganization[]> {
  return useQuery({
    queryFn: () => SalesOrganizationService.search(searchQuery.value),
    queryKey: { salesOrganizationSearch: { searchQuery } },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useSalesOrganizationAllQuery(): UseQueryReturnType<SalesOrganization[]> {
  return useQuery({
    queryFn: () => SalesOrganizationService.getAll(),
    queryKey: { salesOrganizationAll: {} },
    staleTime: 10 * 60 * 1000, // 10 minutes - longer cache for all organizations
  })
}
