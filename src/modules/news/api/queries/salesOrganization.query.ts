import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { SalesOrganization } from '@/client'

import { SalesOrganizationService } from '../services/salesOrganization.service'

export function useSalesOrganizationSearchQuery(
  searchQuery: ComputedRef<string>,
): UseQueryReturnType<SalesOrganization[]> {
  return useQuery({
    queryFn: () => SalesOrganizationService.search(searchQuery.value),
    queryKey: { salesOrganizationSearch: { searchQuery } },
  })
}

export function useSalesOrganizationAllQuery(): UseQueryReturnType<SalesOrganization[]> {
  return useQuery({
    queryFn: () => SalesOrganizationService.getAll(),
    queryKey: { salesOrganizationAll: {} },
  })
}
