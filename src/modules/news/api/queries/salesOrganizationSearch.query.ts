import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'
import { SapSalesOrganizationService } from '@/modules/news/api/services/sapSalesOrganization.service'
import type { SalesOrganization } from '@/modules/news/api/services/sapSalesOrganization.service'

export function useSalesOrganizationSearchQuery(
  search: ComputedRef<string>,
): UseQueryReturnType<SalesOrganization[]> {
  return useQuery({
    queryFn: () => SapSalesOrganizationService.search(search.value),
    queryKey: { sapSalesOrganizationSearch: { q: search } },
  })
}
=