<script setup lang="ts">
import { useForm } from 'formango'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { Locale } from '@/client/types.gen'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import FormPage from '@/components/form/FormPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { newsAnnouncementCreateFormSchema } from '@/models/news/announcement-create/newsAnnouncementCreateForm.model'
import type { NewsArticleTranslationForm } from '@/models/news/article-update/newsArticleCreateForm.model'
import { useNewsAnnouncementCreateMutation } from '@/modules/news/api/mutations/newsAnnouncementCreate.mutation'
import NewsAnnouncementCreateForm from '@/modules/news/features/announcement-create/components/NewsAnnouncementCreateForm.vue'

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const newsAnnouncementCreateMutation = useNewsAnnouncementCreateMutation()
const apiErrorToast = useApiErrorToast()
const router = useRouter()

documentTitle.set(() => i18n.t('module.news.announcements.create.page_title'))

const form = useForm({
  initialState: {
    endDate: null,
    startDate: null,
    salesOrganizations: [],
    translations: setTranslations(),
    type: null,
  },
  schema: newsAnnouncementCreateFormSchema,
  onSubmit: async (values) => {
    try {
      await newsAnnouncementCreateMutation.execute({ body: values })
      await router.push({ name: 'news-announcements-overview' })
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
})

function setTranslations(): NewsArticleTranslationForm[] {
  return Object.values(Locale).map((language) => ({
    title: null,
    content: null,
    language,
  }))
}
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.news.announcements.update.return_to_overview'),
        to: {
          name: 'news-announcements-overview',
        },
      }"
    />
  </AppTeleport>

  <FormPage :title="i18n.t('module.news.announcements.create.page_title')">
    <NewsAnnouncementCreateForm :form="form" />
  </FormPage>
</template>
