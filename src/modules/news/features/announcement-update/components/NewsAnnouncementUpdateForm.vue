<script setup lang="ts">
import {
  VcButton,
  VcDateField,
  VcSelect,
  VcSelectItem,
  VcTabs,
  VcTabsContent,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import dayjs from 'dayjs'
import type { Form } from 'formango'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import {
  AnnouncementType,
  Locale,
} from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHorizontalFieldLayout from '@/components/app/AppHorizontalFieldLayout.vue'
import AppSalesOrganizationMultiSelect from '@/components/app/select/AppSalesOrganizationMultiSelect.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import AppForm from '@/components/form/AppForm.vue'
import FormLayout from '@/components/form/FormLayout.vue'
import FormSubmitButton from '@/components/form/FormSubmitButton.vue'
import { TEST_ID } from '@/constants/testId.constant'
import { AnnouncementTypeEnumUtil } from '@/models/enums/announcementType.enum'
import { LanguageEnumUtil } from '@/models/enums/language.enum'
import type { newsAnnouncementUpdateFormSchema } from '@/models/news/announcement-update/newsAnnouncementUpdateForm.model'
import NewsAnnouncementFormContent from '@/modules/news/components/announcement-form/NewsAnnouncementFormContent.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  form: Form<typeof newsAnnouncementUpdateFormSchema>
}>()

const emit = defineEmits<{
  delete: []
}>()

const i18n = useI18n()

const today = dayjs().startOf('day').toDate()

const translations = props.form.registerArray('translations')
const salesOrganizations = props.form.register('salesOrganizations', [])
const startDate = props.form.register('startDate', today)
const endDate = props.form.register('endDate')
const type = props.form.register('type', AnnouncementType.INFORMATIONAL)

const supportedLanguages = Object.values(Locale)
const activeLanguageTab = ref<Locale>(supportedLanguages[0])
</script>

<template>
  <AppForm
    v-slot="{ formId }"
    :form="props.form"
  >
    <AppTeleport to="headerActions">
      <AppGroup>
        <VcButton
          :test-id="TEST_ID.NEWS.ANNOUNCEMENT_UPDATE.DELETE_BUTTON"
          variant="destructive-secondary"
          icon-left="trash"
          @click="emit('delete')"
        >
          {{ i18n.t('module.news.update.delete_article') }}
        </VcButton>

        <FormSubmitButton
          :test-id="TEST_ID.NEWS.ANNOUNCEMENT_UPDATE.PUBLISH_BUTTON"
          :form-id="formId"
          :form="props.form"
          :label="i18n.t('shared.save_changes')"
        />
      </AppGroup>
    </AppTeleport>

    <FormLayout>
      <AppHorizontalFieldLayout :label="i18n.t('module.news.update_announcement.fields.type')">
        <VcSelect
          v-bind="toFormField(type)"
          :display-fn="(value) => i18n.t(AnnouncementTypeEnumUtil.getLabelI18nKey(value))"
        >
          <VcSelectItem
            v-for="option of AnnouncementTypeEnumUtil.getOptions()"
            :key="option"
            :value="option"
          >
            {{ i18n.t(AnnouncementTypeEnumUtil.getLabelI18nKey(option)) }}
          </VcSelectItem>
        </VcSelect>
      </AppHorizontalFieldLayout>

      <AppHorizontalFieldLayout label="Sales organization">
        <AppSalesOrganizationMultiSelect
          v-bind="toFormField(salesOrganizations)"
          label="Sales organization"
          placeholder="Search and select sales organizations..."
        />
      </AppHorizontalFieldLayout>

      <VcTabs
        v-model="activeLanguageTab"
        :class-config="{
          base: 'pl-64 border-b border-solid border-secondary',
        }"
      >
        <template #items>
          <VcTabsItem
            v-for="language in supportedLanguages"
            :key="language"
            :value="language"
          >
            {{ i18n.t(LanguageEnumUtil.getLabelI18nKey(language)) }}
          </VcTabsItem>
        </template>

        <template #content>
          <VcTabsContent
            v-for="(language, index) of supportedLanguages"
            :key="language"
            :value="language"
          >
            <NewsAnnouncementFormContent
              :index="index"
              :translations="translations"
              :language="language"
            />
          </VcTabsContent>
        </template>
      </VcTabs>

      <AppHorizontalFieldLayout :label="i18n.t('module.news.article.fields.publishing_date')">
        <FormGrid :cols="2">
          <VcDateField
            v-bind="toFormField(startDate)"
            :label="i18n.t('module.news.article.fields.start_date')"
            :is-required="true"
            :is-date-disabled="date => (endDate.modelValue.value !== null && date > endDate.modelValue.value)"
          />

          <VcDateField
            v-bind="toFormField(endDate)"
            :label="i18n.t('module.news.article.fields.end_date')"
            :allow-deselect="true"
            :is-date-disabled="date => (startDate.modelValue.value === null
              ? false
              : date < startDate.modelValue.value)"
          />
        </FormGrid>
      </AppHorizontalFieldLayout>
    </FormLayout>
  </AppForm>
</template>
