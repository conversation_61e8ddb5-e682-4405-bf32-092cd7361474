<script setup lang="ts">
import type {
  PaginatedData,
  Pagination,
  TableColumn,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { SharepointDocumentViewName } from '@/client'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppErrorState from '@/components/app/error-state/AppErrorState.vue'
import AppTable from '@/components/app/table/AppTable.vue'
import AppTableEmptyStateWithoutClear from '@/components/app/table/AppTableEmptyStateWithoutClear.vue'
import AppTableSearchInputField from '@/components/app/table/AppTableSearchInputField.vue'
import AppTableActiveFilters from '@/components/app/table/filters/AppTableActiveFilters.vue'
import AppTableFilters from '@/components/app/table/filters/AppTableFilters.vue'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import type { DocumentIndexPagination } from '@/models/document/index/documentIndexPagination.model'
import { SharepointDocumentViewNameEnumUtil } from '@/models/enums/sharepointDocumentViewName.enum'
import DocumentsDownloadTableCell from '@/modules/document/components/DocumentsDownloadTableCell.vue'
import {
  useDocumentActionDateColumn,
  useDocumentNameColumn,
  useDocumentStatusColumn,
  useDocumentWasteProducerColumn,
} from '@/modules/document/composables/documentTableColumns.composable'
import type { TableFilter } from '@/types/tableFilter.type'

const props = defineProps<{
  isDownloadLoadingId: string | null
  isLoading: boolean
  data: PaginatedData<DocumentIndex>
  error: unknown | null
  filters: TableFilter<DocumentIndexPagination>[]
  pagination: Pagination<DocumentIndexPagination>
  onNextPage: () => Promise<void>
}>()

const emit = defineEmits<{
  download: [id: string]
}>()

const i18n = useI18n()

const columns = computed<TableColumn<DocumentIndex>[]>(() => {
  return [
    useDocumentNameColumn(),
    useDocumentActionDateColumn(),
    useDocumentWasteProducerColumn(),
    useDocumentStatusColumn(),
    {
      cell: (row): VNode => h(DocumentsDownloadTableCell, {
        isLoading: props.isDownloadLoadingId === row.id,
        onDownload: () => {
          emit('download', row.id)
        },
      }),
      headerLabel: '',
      key: 'actions',
    },
  ]
})
</script>

<template>
  <AppTablePage
    :title="i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MEETINGS))"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <AppErrorState
      v-if="!props.isLoading && props.error !== null"
      :error="props.error"
    />

    <AppTable
      v-else
      :columns="columns"
      :data="props.data"
      :is-loading="props.isLoading"
      :pagination="props.pagination"
      :class-config="{
        cell: 'group-hover/row:bg-secondary !py-md',
        row: 'hover:bg-secondary',
      }"
      :is-table-results-hint-hidden="true"
      @next-page="props.onNextPage"
    >
      <template #top>
        <AppAnimateHeight class="!overflow-visible">
          <AppGroup
            justify="between"
            class="px-xl p-md"
          >
            <AppTableActiveFilters
              :pagination="props.pagination"
              :filters="props.filters"
              :dynamic-table="null"
            />
            <AppGroup>
              <AppTableFilters
                :pagination="props.pagination"
                :filters="props.filters"
                :dynamic-table="null"
              />
              <AppTableSearchInputField
                :pagination="props.pagination"
                :is-loading="props.isLoading"
              />
            </AppGroup>
          </AppGroup>
        </AppAnimateHeight>
      </template>
      <template #empty-state>
        <AppTableEmptyStateWithoutClear />
      </template>
    </AppTable>
  </AppTablePage>
</template>
