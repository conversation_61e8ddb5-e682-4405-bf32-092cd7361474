<script setup lang="ts">
import type { AcceptableValue } from '@wisemen/vue-core-components'
import {
  usePagination,
  VcRouterLinkTabs,
  VcRouterLinkTabsItem,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'
import {
  RouterView,
  useRoute,
} from 'vue-router'

import {
  Permission,
  SharepointDocumentViewName,
} from '@/client'
import AppPage from '@/components/layout/page/AppPage.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type { DocumentIndexPagination } from '@/models/document/index/documentIndexPagination.model'
import type { DocumentSiteIndex } from '@/models/document/site/documentSiteIndex.model'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import { SharepointDocumentStatusEnumUtil } from '@/models/enums/sharepointDocumentStatus.enum'
import { SharepointDocumentViewNameEnumUtil } from '@/models/enums/sharepointDocumentViewName.enum'
import { useDocumentDownloadMutation } from '@/modules/document/api/mutations/documentDownload.mutation'
import { useDocumentIndexQuery } from '@/modules/document/api/queries/documentIndex.query'
import { useDocumentSiteIndexQuery } from '@/modules/document/api/queries/documentSiteIndex.query'
import { useAuthStore } from '@/stores/auth.store'
import type { RouteLocationCurrent } from '@/types/global/vueRouter'
import type { TableFilter } from '@/types/tableFilter.type'

interface Tab {
  id: SharepointDocumentViewName
  label: string
  to: RouteLocationCurrent
}

const i18n = useI18n()
const apiErrorToast = useApiErrorToast()
const authStore = useAuthStore()
const route = useRoute()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.document.overview.title'))

const tabs = computed<Tab[]>(() => {
  const items: Tab[] = []

  if (authStore.hasPermission(Permission.DOCUMENT_TRANSPORT)) {
    items.push({
      id: SharepointDocumentViewName.TRANSPORT,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.TRANSPORT)),
      to: { name: 'documents-overview-transport' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_TFS)) {
    items.push({
      id: SharepointDocumentViewName.TFS,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.TFS)),
      to: { name: 'documents-overview-tfs' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_BSC)) {
    items.push({
      id: SharepointDocumentViewName.BSC,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.BSC)),
      to: { name: 'documents-overview-balanced-score-card' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_CONTRACT)) {
    items.push({
      id: SharepointDocumentViewName.CONTRACT,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.CONTRACT)),
      to: { name: 'documents-overview-contract' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_MASTER_TABLE)) {
    items.push({
      id: SharepointDocumentViewName.MASTERTABLE,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MASTERTABLE)),
      to: { name: 'documents-overview-mastertable' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_MINUTES_AND_PRESENTATIONS)) {
    items.push({
      id: SharepointDocumentViewName.MEETINGS,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MEETINGS)),
      to: { name: 'documents-overview-minutes-and-presentations' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_QUOTATION)) {
    items.push({
      id: SharepointDocumentViewName.QUOTATION,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.QUOTATION)),
      to: { name: 'documents-overview-quotation' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_MANUAL)) {
    items.push({
      id: SharepointDocumentViewName.MANUAL,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MANUAL)),
      to: { name: 'documents-overview-twm-manual' },
    })
  }

  return items
})

const documentDownloadMutation = useDocumentDownloadMutation()
const documentIdDownloaded = ref<string | null>(null)
const documentSiteIndexQuery = useDocumentSiteIndexQuery()
const documentSiteIndex = computed<DocumentSiteIndex[]>(() => documentSiteIndexQuery.data.value ?? [])

const pagination = usePagination<DocumentIndexPagination>({
  isRouteQueryEnabled: false,
  options: () => ({
    filter: {
      customerUuid: documentSiteIndex.value[0]?.uuid,
      wasteProducerIds: getWasteProducerIdsFromCustomer(documentSiteIndex.value[0]?.uuid),
    },
    search: undefined,
  }),
  type: 'keyset',
})

const documentIndexQuery = useDocumentIndexQuery(pagination.paginationOptions)

const sharedFilters = computed<TableFilter<DocumentIndexPagination>[]>(() => {
  return [
    {
      dynamicTableColumnUuid: null,
      isStatic: true,
      displayFn: (value): string => {
        const site = documentSiteIndex.value.find((site) => site.uuid === value)

        return site?.name ?? ''
      },
      items: documentSiteIndex.value.map((site) => ({
        type: 'option',
        value: site.uuid,
      })),
      key: 'customerUuid',
      label: i18n.t('module.document.overview.filters.customer'),
      placeholder: i18n.t('module.document.overview.filters.customer'),
      type: 'select',
      onUpdate: (value): void => {
        updateWasteProducerIdsFromCustomer(value)
      },
    },
    {
      dynamicTableColumnUuid: null,
      displayFn: (value): string => {
        const wasteProducer = documentSiteIndex.value
          .find((site) => site.uuid === pagination.paginationOptions.value?.filter?.customerUuid)
          ?.wasteProducers
          .find((producer) => producer.id.toString() === value)

        return wasteProducer?.name ?? ''
      },
      items: getWasteProducerIdsFromCustomer(pagination.paginationOptions.value?.filter?.customerUuid ?? '')
        .map((id) => ({
          type: 'option',
          value: id,
        })),
      key: 'wasteProducerIds',
      label: i18n.t('module.document.overview.filters.waste_producer'),
      min: 1,
      placeholder: i18n.t('module.document.overview.filters.waste_producer'),
      type: 'multiSelect',
    },
    {
      dynamicTableColumnUuid: null,
      displayFn: (value): string => {
        if (value == null) {
          return '-'
        }

        return i18n.t(SharepointDocumentStatusEnumUtil.getLabelI18nKey(value))
      },
      items: SharepointDocumentStatusEnumUtil.getSelectOptions(),
      key: 'status',
      label: i18n.t('module.document.overview.filters.status'),
      placeholder: i18n.t('module.document.overview.filters.status'),
      type: 'select',
    },
    {
      dynamicTableColumnUuid: null,
      formatOptions: {
        minimumFractionDigits: 0,
        useGrouping: false,
      },
      hideControls: true,
      key: 'year',
      label: i18n.t('module.document.overview.filters.year'),
      max: 2100,
      min: 2000,
      placeholder: i18n.t('module.document.overview.filters.year'),
      type: 'number',
    },
  ]
})

async function onDownloadDocument(id: string): Promise<void> {
  try {
    documentIdDownloaded.value = id

    await documentDownloadMutation.execute({
      params: {
        customerUuid: pagination.paginationOptions.value?.filter?.customerUuid as DocumentSiteUuid,
        documentId: id,
      },
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    documentIdDownloaded.value = null
  }
}

function getWasteProducerIdsFromCustomer(uuid: string): string[] {
  return documentSiteIndex.value
    .find((site) => site.uuid === uuid)
    ?.wasteProducers
    .map((wasteProducer) => wasteProducer.id.toString()) ?? []
}

function updateWasteProducerIdsFromCustomer(customerId: AcceptableValue): void {
  const selectedWasteProducerIds = pagination.paginationOptions.value?.filter?.wasteProducerIds

  if (customerId == null) {
    return
  }

  const newWasteProducerIds = getWasteProducerIdsFromCustomer(customerId as string)

  if (JSON.stringify(selectedWasteProducerIds) === JSON.stringify(newWasteProducerIds)) {
    return
  }

  pagination.handleFilterChange({ wasteProducerIds: newWasteProducerIds })
}

watch(
  // @ts-expect-error ts-plugin(2589)
  () => route.name as string | undefined,
  (routeName: string | undefined) => {
    const tab = tabs.value.find((tab) => tab.to.name === routeName)

    pagination.handleFilterChange({ viewName: tab?.id ?? SharepointDocumentViewName.TRANSPORT })
  },
  { immediate: true },
)
</script>

<template>
  <AppPage
    :title="i18n.t('module.document.overview.title')"
    :tooltip="i18n.t('module.document.overview.tooltip')"
  >
    <VcRouterLinkTabs
      :class-config="{
        scrollContainer: 'p-[3px] pb-0',
      }"
      class="mb-xl border-secondary border-b"
    >
      <template #items>
        <VcRouterLinkTabsItem
          v-for="(tab) of tabs"
          :key="tab.id"
          :value="tab.id"
          :to="tab.to"
        >
          {{ tab.label }}
        </VcRouterLinkTabsItem>
      </template>
    </VcRouterLinkTabs>
    <template v-if="!documentSiteIndexQuery.isLoading.value">
      <RouterView
        :data="documentIndexQuery.data.value"
        :is-loading="documentIndexQuery.isLoading.value"
        :error="documentIndexQuery.error.value"
        :pagination="pagination"
        :on-next-page="documentIndexQuery.getNextPage"
        :filters="sharedFilters"
        :is-download-loading-id="documentDownloadMutation.isLoading.value ? documentIdDownloaded : null"
        @download="onDownloadDocument"
      />
    </template>
  </AppPage>
</template>
