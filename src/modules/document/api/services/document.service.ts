import type {
  PaginatedData,
  PaginationOptions,
} from '@wisemen/vue-core-components'
import { PaginationParamsBuilder } from '@wisemen/vue-core-components'

import {
  downloadDocumentV1,
  viewDocumentIndexV1,
  viewUserSiteIndexV1,
} from '@/client/sdk.gen'
import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import {
  DocumentIndexPaginationTransformer,
  DocumentIndexTransformer,
} from '@/models/document/index/documentIndex.transformer'
import type { DocumentIndexPagination } from '@/models/document/index/documentIndexPagination.model'
import type { DocumentSiteIndex } from '@/models/document/site/documentSiteIndex.model'
import { DocumentSiteIndexTransformer } from '@/models/document/site/documentSiteIndex.transformer'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import { DownloadUtil } from '@/utils/download.util'

export class DocumentService {
  static async download(documentId: string, customerUuid: DocumentSiteUuid): Promise<void> {
    const response = await downloadDocumentV1({
      body: {
        customerUuid,
        documentId,
      },
    })

    const disposition = response.response.headers.get('Content-Disposition')

    DownloadUtil.downloadBlob(response.data as Blob, disposition)
  }

  static async getAll(paginationOptions: PaginationOptions<DocumentIndexPagination>):
  Promise<PaginatedData<DocumentIndex>> {
    const query = new PaginationParamsBuilder(paginationOptions)
      .buildKeyset(DocumentIndexPaginationTransformer.toDto)

    const response = await viewDocumentIndexV1({ query })

    return {
      data: response.data.items.map(DocumentIndexTransformer.fromDto),
      meta: {
        next: response.data.meta.next,
        total: response.data.items.length,
      },
    }
  }

  static async getSites(): Promise<DocumentSiteIndex[]> {
    const response = await viewUserSiteIndexV1({})

    return response.data.map(DocumentSiteIndexTransformer.fromDto)
  }
}
