import type { PaginationOptions } from '@wisemen/vue-core-components'
import type { UseInfiniteQueryReturnType } from '@wisemen/vue-core-query'
import { useInfiniteQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import type { DocumentIndexPagination } from '@/models/document/index/documentIndexPagination.model'
import { DocumentService } from '@/modules/document/api/services/document.service'

export function useDocumentIndexQuery(paginationOptions: ComputedRef<PaginationOptions<DocumentIndexPagination>>):
UseInfiniteQueryReturnType<DocumentIndex> {
  return useInfiniteQuery<DocumentIndex, DocumentIndexPagination>({
    enabled: computed<boolean>(() => {
      return paginationOptions.value.filter?.customerUuid !== undefined
        && paginationOptions.value.filter?.viewName !== undefined
        && (paginationOptions.value.filter?.wasteProducerIds?.length ?? 0) > 0
    }),
    paginationOptions,
    queryFn: (options) => {
      return DocumentService.getAll(options)
    },
    queryKey: { documentIndex: { paginationOptions } },
  })
}
