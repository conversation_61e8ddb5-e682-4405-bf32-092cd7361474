
import type { TableColumn } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import { DynamicColumnNames } from '@/client'
import {
  useGenericContractIdColumn,
  useGenericContractItemColumn,
  useGenericCustomerIdColumn,
  useGenericCustomerNameColumn,
  useGenericDateColumn,
  useGenericInquiryNumberColumn,
  useGenericPickupAddressIdColumn,
  useGenericPickupAddressNameColumn,
  useGenericRequestorNameColumn,
  useGenericSalesOrganisationIdColumn,
  useGenericSalesOrganisationNameColumn,
  useGenericWasteProducerIdColumn,
  useGenericWasteProducerNameColumn,
  useGenericWasteStreamNameColumn,
} from '@/composables/table-columns/genericTableColumns.composable'
import { TEST_ID } from '@/constants/testId.constant'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import WasteInquiryOverviewTableEwcCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableEwcCell.vue'
import WasteInquiryOverviewTableStatusCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableStatusCell.vue'

export function useWasteInquiryTableColumns(): Partial<{
  [K in DynamicColumnNames]: (label: string, isSortable: boolean) => TableColumn<WasteInquiryIndex>
}> {
  return {
    [DynamicColumnNames.CONTRACT_ID]: useGenericContractIdColumn,
    [DynamicColumnNames.CONTRACT_ITEM]: useGenericContractItemColumn,
    [DynamicColumnNames.CUSTOMER_ID]: useGenericCustomerIdColumn,
    [DynamicColumnNames.CUSTOMER_NAME]: useGenericCustomerNameColumn,
    [DynamicColumnNames.DATE]: useGenericDateColumn,
    [DynamicColumnNames.EWC_CODE]: useEwcCodeColumn,
    [DynamicColumnNames.INQUIRY_NUMBER]: useGenericInquiryNumberColumn,
    [DynamicColumnNames.PICK_UP_ADDRESS_ID]: useGenericPickupAddressIdColumn,
    [DynamicColumnNames.PICK_UP_ADDRESS_NAME]: useGenericPickupAddressNameColumn,
    [DynamicColumnNames.REQUESTOR_NAME]: useGenericRequestorNameColumn,
    [DynamicColumnNames.SALES_ORGANISATION_ID]: useGenericSalesOrganisationIdColumn,
    [DynamicColumnNames.SALES_ORGANISATION_NAME]: useGenericSalesOrganisationNameColumn,
    [DynamicColumnNames.STATUS]: useStatusColumn,
    [DynamicColumnNames.WASTE_PRODUCER_ID]: useGenericWasteProducerIdColumn,
    [DynamicColumnNames.WASTE_PRODUCER_NAME]: useGenericWasteProducerNameColumn,
    [DynamicColumnNames.WASTE_STREAM_NAME]: useGenericWasteStreamNameColumn,
  }
}

function useEwcCodeColumn(
  label: string,
  isSortable: boolean,
): TableColumn<WasteInquiryIndex> {
  return {
    isSortable,
    cell: (row): VNode => h(WasteInquiryOverviewTableEwcCell, {
      ewcLevel1: row.ewcLevel1,
      ewcLevel2: row.ewcLevel2,
      ewcLevel3: row.ewcLevel3,
    }),
    headerLabel: label,
    key: 'ewc',
  }
}

// remove status from draft
function useStatusColumn(
  label: string,
  isSortable: boolean,
): TableColumn<PickupRequestIndex> {
  return {
    isSortable,
    cell: (row): VNode => {
      // Hide status column content for DRAFT status
      if (row.status === PickUpRequestStatus.DRAFT || row.status === PickUpRequestStatus.INDASCAN_DRAFT) {
        return h(VcTableCell, '')
      }
      return h(PickupRequestOverviewTableStatusCell, { status: row.status })
    },
    headerLabel: label,
    key: 'status',
  }
}
