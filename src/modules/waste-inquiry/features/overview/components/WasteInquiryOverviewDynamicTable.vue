<script setup lang="ts">
import type {
  PaginatedData,
  Pagination,
  TableColumn,
} from '@wisemen/vue-core-components'
import { VcButton } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { WasteInquiryStatus } from '@/client'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppErrorState from '@/components/app/error-state/AppErrorState.vue'
import AppTable from '@/components/app/table/AppTable.vue'
import AppTableBulkActions from '@/components/app/table/AppTableBulkActions.vue'
import AppTableDynamicViewsDropdown from '@/components/app/table/AppTableDynamicViewsDropdown.vue'
import AppTableActiveFilters from '@/components/app/table/filters/AppTableActiveFilters.vue'
import AppTableFilters from '@/components/app/table/filters/AppTableFilters.vue'
import TableSettingsPopover from '@/components/table/TableSettingsPopover.vue'
import type { DynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { DynamicTableColumnIndex } from '@/models/dynamic-table/column/dynamicTableColumnIndex.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import { WasteInquiryStatusEnumUtil } from '@/models/enums/wasteInquiryStatus.enum'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import { WasteInquiryIndexTableTabs } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import type { WasteInquiryIndexPagination } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import { WASTE_INQUIRY_STATUS_FILTER_NAME } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import WasteInquiryOverviewTableActionsCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableActionsCell.vue'
import WasteInquiryOverviewTableCheckboxCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableCheckboxCell.vue'
import { useWasteInquiryTableColumns } from '@/modules/waste-inquiry/features/overview/composables/wasteInquiryTableColumns.composable'
import type { TableFilter } from '@/types/tableFilter.type'

const props = defineProps<{
  isCopyingId?: string | null
  hasActiveViewBeenUpdated: boolean
  hasBulkActions?: boolean
  hasWasteInquiryManagePermission: boolean
  isDeleteLoading?: boolean
  isLoading: boolean
  isUpdatingActiveView: boolean
  activeTab: string
  data: PaginatedData<WasteInquiryIndex> | null
  dynamicColumns: DynamicTableColumnIndex[]
  dynamicTable: DynamicTable
  error: unknown | null
  pagination: Pagination<WasteInquiryIndexPagination>
  tableViews: DynamicTableViewIndex[]
  onViewsGetNextPage: () => Promise<void>
}>()

const emit = defineEmits<{
  bulkDelete: [WasteInquiryUuid[]]
  changeView: [DynamicTableViewIndex]
  copy: [WasteInquiryIndex]
  edit: [WasteInquiryIndex]
  nextPage: []
}>()

const i18n = useI18n()
const activeView = defineModel<DynamicTableViewIndex>('activeView', { required: true })

const itemsSelectedInBulk = defineModel<WasteInquiryUuid[]>('itemsSelectedInBulk', { required: true })

const wasteInquiryTableColumns = useWasteInquiryTableColumns()

const columns = computed<TableColumn<WasteInquiryIndex>[]>(() => {
  const cols = props.dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .filter((column) => {
      if (props.activeTab === WasteInquiryIndexTableTabs.DRAFTS && column.name === 'status') {
        return false
      }

      return true
    })
    .map((column) => {
      const sort = props.dynamicTable.sorts.value.find((sort) => sort.uuid === column.uuid) ?? null

      const isSortable = sort !== null && !sort.isDisabled && sort.direction !== null

      //  TODO: refactor this, it's better to just pass the column and handle the other logic inside the function
      const columnFn = wasteInquiryTableColumns[column.name]

      return columnFn ? columnFn(column.label, isSortable) : null
    })
    .filter((col): col is TableColumn<WasteInquiryIndex> => col !== null)

  if (props.hasBulkActions && props.hasWasteInquiryManagePermission) {
    cols.unshift({
      cell: (row): VNode => h(WasteInquiryOverviewTableCheckboxCell, {
        rowUuid: row.uuid ?? '' as WasteInquiryUuid,
        selectedItems: itemsSelectedInBulk.value,
        onUpdate: (value: boolean) => {
          if (row.uuid === null) {
            return
          }
          if (value) {
            itemsSelectedInBulk.value?.push(row.uuid)
          }
          else {
            itemsSelectedInBulk.value = itemsSelectedInBulk.value.filter((uuid) =>
              uuid !== row.uuid) ?? []
          }
        },
      }),
      headerLabel: '',
      key: 'checkbox',
      width: '2.5rem',
    })
  }

  if (props.activeTab !== WasteInquiryIndexTableTabs.DRAFTS || props.hasWasteInquiryManagePermission) {
    cols.push({
      cell: (row): VNode => h(WasteInquiryOverviewTableActionsCell, {
        isCopying: props.isCopyingId === row.inquiryNumber,
        canCopy: row.status !== WasteInquiryStatus.DRAFT,
        status: row.status,
        onCopy: () => onCopy(row),
        onEdit: () => onEdit(row),
      }),
      headerLabel: '',
      key: 'actions',
      maxWidth: 'max-content',
    })
  }

  return cols
})

const filters = computed<TableFilter<WasteInquiryIndexPagination>[]>(() => {
  return [
    {
      dynamicTableColumnUuid: props.dynamicTable.availableFilters.value.find((filter) =>
        filter.name === WASTE_INQUIRY_STATUS_FILTER_NAME)?.uuid ?? null,
      displayFn: (value: WasteInquiryStatus): string => {
        return i18n.t(WasteInquiryStatusEnumUtil.getI18nKey(value))
      },
      items: WasteInquiryStatusEnumUtil.getSelectOptions().filter((item) => {
        return item.value !== WasteInquiryStatus.DRAFT
      }),
      key: 'statuses',
      label: i18n.t('module.waste_inquiry.overview.status'),
      type: 'multiSelect',
    },
  ]
})

function onNextPage(): void {
  emit('nextPage')
}

function onChangeView(view: DynamicTableViewIndex): void {
  emit('changeView', view)
}

async function onViewsGetNextPage(): Promise<void> {
  await props.onViewsGetNextPage()
}

function onEdit(wasteInquiry: WasteInquiryIndex): void {
  emit('edit', wasteInquiry)
}

function onCancelBulkSelection(): void {
  itemsSelectedInBulk.value = []
}

function onDeleteInBulk(): void {
  if (itemsSelectedInBulk.value.length === 0) {
    return
  }

  emit('bulkDelete', itemsSelectedInBulk.value)
}

function onCopy(wasteInquiry: WasteInquiryIndex): void {
  emit('copy', wasteInquiry)
}
</script>

<template>
  <AppErrorState
    v-if="!isLoading && error !== null"
    :error="error"
  />

  <AppTable
    v-else
    :columns="columns"
    :data="props.data"
    :is-loading="props.isLoading"
    :pagination="props.pagination"
    :is-first-column-sticky="true"
    :is-last-column-sticky="props.activeTab !== WasteInquiryIndexTableTabs.DRAFTS
      || props.hasWasteInquiryManagePermission"
    :class-config="{
      cell: 'group-hover/row:bg-secondary !py-md',
      row: 'hover:bg-secondary',
      root: 'rounded-tr-lg rounded-tl-none rounded-b-2xl',
    }"
    :is-table-results-hint-hidden="true"
    @next-page="onNextPage"
  >
    <template #top>
      <AppTableBulkActions
        :count="itemsSelectedInBulk.length"
        @cancel-bulk-selection="onCancelBulkSelection"
      >
        <template #actions>
          <VcButton
            :is-loading="props.isDeleteLoading"
            variant="destructive-tertiary"
            @click="onDeleteInBulk"
          >
            {{ i18n.t('module.waste_inquiry.overview.bulk.delete_draft') }}
          </VcButton>
        </template>
      </AppTableBulkActions>

      <AppAnimateHeight class="!overflow-visible">
        <AppGroup
          v-if="activeTab === WasteInquiryIndexTableTabs.SUBMITTED"
          justify="between"
          class="px-xl"
        >
          <AppTableActiveFilters
            :pagination="props.pagination"
            :filters="filters"
            :dynamic-table="props.dynamicTable"
          />
          <AppGroup class="p-md">
            <AppTableDynamicViewsDropdown
              v-model:active-view="activeView"
              :dynamic-table="props.dynamicTable"
              :is-updating-active-view="props.isUpdatingActiveView"
              :table-views="props.tableViews"
              :has-active-view-been-updated="props.hasActiveViewBeenUpdated"
              :is-add-button-disabled="!props.hasActiveViewBeenUpdated"
              :on-views-get-next-page="onViewsGetNextPage"
              @change-view="onChangeView"
            />

            <AppTableFilters
              :pagination="props.pagination"
              :filters="filters"
              :dynamic-table="props.dynamicTable"
            />

            <!-- TODO: Uncomment once feature ready in backend -->
            <!-- <AppTableSearchInputField
              :is-loading="props.isLoading"
              :pagination="props.pagination"
            /> -->

            <TableSettingsPopover
              :dynamic-table="props.dynamicTable"
              :table-views="props.tableViews"
              :has-active-view-been-updated="props.hasActiveViewBeenUpdated"
              :active-view="activeView"
            />
          </AppGroup>
        </AppGroup>
      </AppAnimateHeight>
    </template>
  </AppTable>
</template>
