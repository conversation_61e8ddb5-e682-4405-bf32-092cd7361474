import type {
  PaginatedData,
  PaginationOptions,
} from '@wisemen/vue-core-components'
import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'
import type { WasteProducerIndexPagination } from '@/models/waste-producer/index/wasteProducerIndexPagination.model.ts'
import { WasteProducerService } from '@/modules/waste-inquiry/api/services/wasteProducer.service'

export function useWasteProducerIndexQuery(
  paginationOptions: ComputedRef<PaginationOptions<WasteProducerIndexPagination>>,
): UseQueryReturnType<PaginatedData<WasteProducerIndex>> {
  return useQuery<PaginatedData<WasteProducerIndex>>({
    isEnabled: computed<boolean>(
      () => paginationOptions.value.search !== undefined && paginationOptions.value.search.trim().length > 0,
    ),
    queryFn: () => {
      return WasteProducerService.getAll(paginationOptions.value)
    },
    queryKey: { wasteProducerIndex: { paginationOptions } },
  })
}
