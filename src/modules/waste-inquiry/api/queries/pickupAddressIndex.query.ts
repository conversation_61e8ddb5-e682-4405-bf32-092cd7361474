import type {
  PaginatedData,
  PaginationOptions,
} from '@wisemen/vue-core-components'
import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import type { PickUpAddressIndex } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import type { PickUpAddressIndexPagination } from '@/models/pick-up-address/index/pickUpAddressIndexPagination.model.ts'
import { PickUpAddressService } from '@/modules/waste-inquiry/api/services/pickUpAddress.service'

export function usePickUpAddressIndexQuery(
  paginationOptions: ComputedRef<PaginationOptions<PickUpAddressIndexPagination>>,
): UseQueryReturnType<PaginatedData<PickUpAddressIndex>> {
  return useQuery<PaginatedData<PickUpAddressIndex>>({
    isEnabled: computed<boolean>(
      () => paginationOptions.value.search !== undefined && paginationOptions.value.search.trim().length > 0,
    ),
    queryFn: () => {
      return PickUpAddressService.getAll(paginationOptions.value)
    },
    queryKey: { pickUpAddressIndex: { paginationOptions } },
  })
}
