import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { ContractLineGeneratePdf } from '@/models/contract-line/pdf/contractLineGeneratePdf.model'
import type { ContractLineGeneratePdfResponse } from '@/models/contract-line/pdf/contractLineGeneratePdfResponse.model'
import { ContractLineService } from '@/modules/contract-line/api/services/contractLine.service'

interface Params {
  data: ContractLineGeneratePdf
}

export function useContractGenerateBulkPdfMutation(): UseMutationReturnType<Params, ContractLineGeneratePdfResponse> {
  return useMutation<Params, ContractLineGeneratePdfResponse>({
    queryFn: async ({ body }) => {
      return await ContractLineService.generateBulkPdf(body.data)
    },
    queryKeysToInvalidate: {},
  })
}
