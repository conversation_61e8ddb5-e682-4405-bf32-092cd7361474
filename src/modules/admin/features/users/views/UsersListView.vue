<script setup lang="ts">
import { useDebounce } from '@vueuse/core'
import { usePagination, type PaginatedData } from '@wisemen/vue-core-components'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { useDynamicTableColumnIndexQuery } from '@/api/queries/dynamicTableColumnIndex.query'
import { useDynamicTableDefaultViewQuery } from '@/api/queries/dynamicTableDefaultView.query'
import { useDynamicTableViewIndexQuery } from '@/api/queries/dynamicTableViewIndex.query'
import { Permission } from '@/client'
import type { UserIndexView } from '@/client/types.gen'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { DynamicTableColumnIndex } from '@/models/dynamic-table/column/dynamicTableColumnIndex.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { DynamicTableViewIndexPagination } from '@/models/dynamic-table/view/dynamicTableViewIndexPagination.model'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { useSyncEntraUsersMutation } from '@/modules/admin/api/mutations/syncEntraUsers.mutation'
import { useUserIndexQuery } from '@/modules/admin/api/queries/userIndex.query'
import type { UserIndexPagination } from '@/modules/admin/api/services/user.service'
import UsersListDynamicTable from '@/modules/admin/features/users/components/UsersListDynamicTable.vue'
import { useAuthStore } from '@/stores/auth.store'
import { ObjectUtil } from '@/utils/object.util'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'

const i18n = useI18n()
const authStore = useAuthStore()
const documentTitle = useDocumentTitle()
const apiErrorToast = useApiErrorToast()

documentTitle.set(() => i18n.t('module.user.overview.title'))

const hasUserManagePermission = computed<boolean>(() => authStore.hasPermission(Permission.USER_MANAGE))
const hasUserImpersonatePermission = computed<boolean>(() => authStore.hasPermission(Permission.USER_IMPERSONATE))

const dynamicTableIndexPagination = usePagination<DynamicTableViewIndexPagination>({ isRouteQueryEnabled: false })
const dynamicTableColumns = useDynamicTableColumnIndexQuery(DynamicTableName.USER)
const dynamicTableDefaultView = useDynamicTableDefaultViewQuery(DynamicTableName.USER)
const dynamicTableViewIndex = useDynamicTableViewIndexQuery(
  DynamicTableName.USER,
  dynamicTableIndexPagination.paginationOptions,
)

const defaultView = computed<DynamicTableViewIndex | null>(() => dynamicTableDefaultView.data.value)
const activeView = ref<DynamicTableViewIndex | null>(ObjectUtil.deepClone(defaultView.value))
const dynamicTableViews = computed<DynamicTableViewIndex[]>(() => dynamicTableViewIndex.data.value?.data ?? [])

const dynamicTable = useDynamicTable({
  activeView,
  columns: computed<DynamicTableColumnIndex[]>(() => dynamicTableColumns.data.value ?? []),
  dynamicTableViews,
  tableName: DynamicTableName.USER,
})

watch(defaultView, (dv) => {
  activeView.value = ObjectUtil.deepClone(dv)
})

const searchQuery = ref<string>('')

const pagination = usePagination<UserIndexPagination>({
  isRouteQueryEnabled: false,
  type: 'offset',
})

watch(searchQuery, (search) => {
  pagination.handleSearchChange(search)
})

const userIndexQuery = useUserIndexQuery(pagination.paginationOptions)
const isUserIndexLoading = useDebounce(userIndexQuery.isLoading, 250)

const EMAIL_DOMAIN = '@indaver.com'

const filteredUserData = computed<PaginatedData<UserIndexView> | null>(() => {
  if (!userIndexQuery.data.value) {
    return null
  }

  const users = userIndexQuery.data.value.data.filter(user => !user.email.endsWith(EMAIL_DOMAIN))

  return {
    data: users,
    meta: {
      ...userIndexQuery.data.value.meta,
      total: users.length,
    }
  }
})

const syncEntraUsersMutation = useSyncEntraUsersMutation()

function onImpersonate(user: UserIndexView): void {
  // TODO: Implement impersonation logic
  // eslint-disable-next-line no-console
  console.log('Impersonate user:', user.uuid)
}

function onChangeView(view: DynamicTableViewIndex): void {
  dynamicTable.changeView(view)
}

async function onViewsGetNextPage(): Promise<void> {
  await dynamicTableViewIndex.getNextPage()
}

async function onSyncUsers(): Promise<void> {
  try {
    await syncEntraUsersMutation.execute()
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <AppTablePage
    :title="i18n.t('module.user.overview.title')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <UsersListDynamicTable
      v-model:active-view="activeView as DynamicTableViewIndex"
      v-model:search-query="searchQuery"
      :dynamic-table="dynamicTable"
      :table-views="dynamicTableViews"
      :has-active-view-been-updated="dynamicTable.hasActiveViewBeenUpdated.value"
      :is-updating-active-view="dynamicTable.isUpdateViewLoading.value"
      :is-loading="isUserIndexLoading || userIndexQuery.isFetching.value"
      :data="filteredUserData"
      :pagination="pagination"
      :error="userIndexQuery.error.value"
      :is-sync-loading="syncEntraUsersMutation.isLoading.value"
      :has-user-manage-permission="hasUserManagePermission"
      :has-user-impersonate-permission="hasUserImpersonatePermission"
      :on-views-get-next-page="onViewsGetNextPage"
      @next-page="userIndexQuery.getNextPage"
      @change-view="onChangeView"
      @impersonate="onImpersonate"
      @sync-users="onSyncUsers"
    />
  </AppTablePage>
</template>
