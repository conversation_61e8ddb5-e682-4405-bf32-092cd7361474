<script setup lang="ts">
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import AppPackagingTypeSelect from '@/components/app/select/AppPackagingTypeSelect.vue'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  customerId: string
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const packagingType = props.materials.register(`${props.index}.packagingType`, null)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <AppPackagingTypeSelect
      v-bind="toFormField(packagingType)"
      :customer-id="props.customerId"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.packaging_type')"
    />
  </PickupRequestDetailsFormTableCell>
</template>
