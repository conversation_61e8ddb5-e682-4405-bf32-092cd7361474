<script setup lang="ts">
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import type { PackingGroup } from '@/client'
import { WastePackagingSizeEnumUtil } from '@/models/enums/wastePackagingSize.enum'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import PickupRequestTransportMaterialUnNumberInfo from '@/modules/pickup-request/features/update/components/steps/transport/PickupRequestTransportMaterialUnNumberInfo.vue'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const unNumber = props.materials.register(`${props.index}.unNumber`)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <PickupRequestTransportMaterialUnNumberInfo
      :content="unNumber.value.value?.packingGroup
        ? i18n.t(WastePackagingSizeEnumUtil.getI18nKey(unNumber.value.value.packingGroup as PackingGroup))
        : null"
    />
  </PickupRequestDetailsFormTableCell>
</template>
