<script setup lang="ts">
import type { FieldArray } from 'formango'

import AppContainerTypeSelect from '@/components/app/select/AppContainerTypeSelect.vue'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  customerId: string
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const containerType = props.materials.register(`${props.index}.containerType`, null)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <AppContainerTypeSelect
      :customer-id="props.customerId"
      v-bind="toFormField(containerType)"
    />
  </PickupRequestDetailsFormTableCelL>
</template>
