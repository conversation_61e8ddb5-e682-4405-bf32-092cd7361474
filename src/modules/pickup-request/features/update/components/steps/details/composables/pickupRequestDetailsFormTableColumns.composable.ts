import type { TableColumn } from '@wisemen/vue-core-components'
import type { FieldArray } from 'formango'
import type { VNode } from 'vue'
import { h } from 'vue'

import { DynamicColumnNames } from '@/client'
import { PickupRequestRequiredUtil } from '@/models/pickup-request/pickupRequestValidation.util'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormContainerCoveredTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormContainerCoveredTableCell.vue'
import PickupRequestDetailsFormContainerNumberTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormContainerNumberTableCell.vue'
import PickupRequestDetailsFormContainerSizeTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormContainerSizeTableCell.vue'
import PickupRequestDetailsFormContainerTransportTypeTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormContainerTransportTypeTableCell.vue'
import PickupRequestDetailsFormContainerTypeTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormContainerTypeTableCell.vue'
import PickupRequestDetailsFormCostCenterTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormCostCenterTableCell.vue'
import PickupRequestDetailsFormEstimatedVolumeTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormEstimatedVolumeTableCell.vue'
import PickupRequestDetailsFormPackagingTypeTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormPackagingTypeTableCell.vue'
import PickupRequestDetailsFormPoNumberTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormPoNumberTableCell.vue'
import PickupRequestDetailsFormQuantityContainersTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormQuantityContainersTableCell.vue'
import PickupRequestDetailsFormQuantityLabelsTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormQuantityLabelsTableCell.vue'
import PickupRequestDetailsFormQuantityPackagesTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormQuantityPackagesTableCell.vue'
import PickupRequestDetailsFormQuantityPalletsTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormQuantityPalletsTableCell.vue'
import PickupRequestDetailsFormReconciliationNumberTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormReconciliationNumberTableCell.vue'
import PickupRequestDetailsFormSerialNumberTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormSerialNumberTableCell.vue'
import PickupRequestDetailsFormTankerTypeTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTankerTypeTableCell.vue'
import PickupRequestDetailsFormUnNumberDangerLabel1TableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormUnNumberDangerLabel1TableCell.vue'
import PickupRequestDetailsFormUnNumberDangerLabel2TableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormUnNumberDangerLabel2TableCell.vue'
import PickupRequestDetailsFormUnNumberDangerLabel3TableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormUnNumberDangerLabel3TableCell.vue'
import PickupRequestDetailsFormUnNumberPackingGroupTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormUnNumberPackingGroupTableCell.vue'
import PickupRequestDetailsFormUnNumberTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormUnNumberTableCell.vue'

export function usePickupRequestDetailsFormTableColumns(
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  form: Partial<PickupRequestUpdateForm>,
  customerId: string,
): Partial<{
    [K in DynamicColumnNames]: (
      label: string,
      isSortable: boolean,
    ) => TableColumn<PickupRequestWasteMaterialForm>
  }> {
  const pickupRequestRequiredFields = new PickupRequestRequiredUtil(form)

  return {
    [DynamicColumnNames.CONTAINER_NUMBER]: (label: string, isSortable: boolean) =>
      useContainerNumberColumn(label, isSortable, materials),
    [DynamicColumnNames.CONTAINER_TRANSPORT_TYPE]: (label: string, isSortable: boolean) =>
      useContainerTransportTypeColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isContainerTransportTypeRequired,
      ),
    [DynamicColumnNames.CONTAINER_TYPE]: (label: string, isSortable: boolean) =>
      useContainerTypeColumn(
        label,
        isSortable,
        materials,
        customerId,
        pickupRequestRequiredFields.isContainerTypeRequired,

      ),
    [DynamicColumnNames.CONTAINER_VOLUME_SIZE]: (label: string, isSortable: boolean) =>
      useContainerVolumeSizeColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isContainerVolumeSizeRequired,
      ),
    [DynamicColumnNames.COST_CENTER]: (label: string, isSortable: boolean) =>
      useCostCenterColumn(label, isSortable, materials),
    [DynamicColumnNames.DANGER_LABEL1]: (label: string, isSortable: boolean) =>
      useUnNumberDangerLabel1Column(label, isSortable, materials),
    [DynamicColumnNames.DANGER_LABEL2]: (label: string, isSortable: boolean) =>
      useUnNumberDangerLabel2Column(label, isSortable, materials),
    [DynamicColumnNames.DANGER_LABEL3]: (label: string, isSortable: boolean) =>
      useUnNumberDangerLabel3Column(label, isSortable, materials),
    [DynamicColumnNames.ESTIMATED_WEIGHT_OR_VOLUME_VALUE]: (label: string, isSortable: boolean) =>
      useEstimatedWeightOrVolumeUnitColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isEstimatedWeightOrVolumeRequired,
      ),
    [DynamicColumnNames.IS_CONTAINER_COVERED]: (label: string, isSortable: boolean) =>
      useContainerCoveredColumn(label, isSortable, materials),
    [DynamicColumnNames.PACKAGING_TYPE]: (label: string, isSortable: boolean) =>
      usePackagingTypeColumn(
        label,
        isSortable,
        materials,
        customerId,
        pickupRequestRequiredFields.isPackagingTypeRequired,
      ),
    [DynamicColumnNames.PACKING_GROUP]: (label: string, isSortable: boolean) =>
      useUnNumberPackingGroupColumn(label, isSortable, materials),
    [DynamicColumnNames.PO_NUMBER]: (label: string, isSortable: boolean) =>
      usePoNumberColumn(label, isSortable, materials),
    [DynamicColumnNames.QUANTITY_CONTAINERS]: (label: string, isSortable: boolean) =>
      useQuantityContainersColumn(label, isSortable, materials),
    [DynamicColumnNames.QUANTITY_LABELS]: (label: string, isSortable: boolean) =>
      useQuantityLabelsColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isQuantityLabelsRequired,
      ),
    [DynamicColumnNames.QUANTITY_PACKAGES]: (label: string, isSortable: boolean) =>
      useQuantityPackagesColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isQuantityPackagesRequired,
      ),
    [DynamicColumnNames.QUANTITY_PALLETS]: (label: string, isSortable: boolean) =>
      useQuantityPalletsColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isQuantityPalletsRequired,
      ),
    [DynamicColumnNames.RECONCILIATION_NUMBER]: (label: string, isSortable: boolean) =>
      useReconciliationNumberColumn(label, isSortable, materials),
    [DynamicColumnNames.SERIAL_NUMBER]: (label: string, isSortable: boolean) =>
      useSerialNumberColumn(label, isSortable, materials),
    [DynamicColumnNames.TANKER_TYPE]: (label: string, isSortable: boolean) =>
      useTankerTypeColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isTankerTypeRequired,
      ),
    [DynamicColumnNames.TFS_NUMBER]: (label: string, isSortable: boolean) =>
      useTfsNumberColumn(label, isSortable, materials),
    [DynamicColumnNames.UN_NUMBER]: (label: string, isSortable: boolean) =>
      useUnNumberColumn(
        label,
        isSortable,
        materials,
        pickupRequestRequiredFields.isUnNumberRequired,
      ),
  }
}

function getMaterialRowIndex(row: PickupRequestWasteMaterialForm, materials: PickupRequestWasteMaterialForm[]): number {
  return materials.indexOf(row)
}

function useEstimatedWeightOrVolumeUnitColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormEstimatedVolumeTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'estimatedWeightOrVolume',
    width: '16rem',
  }
}

function usePackagingTypeColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  customerId: string,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormPackagingTypeTableCell, {
      customerId,
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'packagingType',
    width: '15rem',
  }
}

function useQuantityPackagesColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormQuantityPackagesTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'quantityPackages',
    width: '10rem',
  }
}

function useQuantityLabelsColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormQuantityLabelsTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'quantityLabels',
    width: '10rem',
  }
}

function useQuantityPalletsColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormQuantityPalletsTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'quantityPallets',
    width: '10rem',
  }
}

function useUnNumberColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormUnNumberTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'unNumber',
    width: '15rem',
  }
}

function useUnNumberPackingGroupColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormUnNumberPackingGroupTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'unNumberPackingGroup',
    width: '7rem',
  }
}

function useUnNumberDangerLabel1Column(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormUnNumberDangerLabel1TableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'unNumberDangerLabel1',
    width: '7rem',
  }
}

function useUnNumberDangerLabel2Column(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormUnNumberDangerLabel2TableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'unNumberDangerLabel2',
    width: '7rem',
  }
}

function useUnNumberDangerLabel3Column(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormUnNumberDangerLabel3TableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'unNumberDangerLabel3',
    width: '7rem',
  }
}

function useContainerCoveredColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormContainerCoveredTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'isContainerCovered',
    width: '10rem',
  }
}

function usePoNumberColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormPoNumberTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'poNumber',
    width: '10rem',
  }
}

function useCostCenterColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormCostCenterTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'costCenter',
    width: '10rem',
  }
}

function useContainerNumberColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormContainerNumberTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'containerNumber',
    width: '9rem',
  }
}

function useContainerTypeColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  customerId: string,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormContainerTypeTableCell, {
      customerId,
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'containerType',
    width: '15rem',
  }
}

function useContainerTransportTypeColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormContainerTransportTypeTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'containerTransportType',
    width: '15rem',
  }
}

function useTankerTypeColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormTankerTypeTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'tankerType',
    width: '15rem',
  }
}

function useReconciliationNumberColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormReconciliationNumberTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'reconciliationNumber',
    width: '11rem',
  }
}

function useQuantityContainersColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormQuantityContainersTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'quantityContainers',
    width: '10rem',
  }
}

function useContainerVolumeSizeColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
  isRequired?: boolean,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormContainerSizeTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: isRequired ? `${label}*` : label,
    key: 'containerVolumeSize',
    width: '12rem',
  }
}

function useSerialNumberColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormSerialNumberTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'serialNumber',
    width: '10rem',
  }
}

function useTfsNumberColumn(
  label: string,
  isSortable: boolean,
  materials: FieldArray<PickupRequestWasteMaterialForm>,
): TableColumn<PickupRequestWasteMaterialForm> {
  return {
    isSortable,
    cell: (row): VNode => h(PickupRequestDetailsFormSerialNumberTableCell, {
      index: getMaterialRowIndex(row, materials.value.value),
      materials,
    }),
    headerLabel: label,
    key: 'tfsNumber',
    width: '10rem',
  }
}
