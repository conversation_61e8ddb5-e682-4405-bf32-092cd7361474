<script setup lang="ts">
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import AppTankerTypeSelect from '@/components/app/select/AppTankerTypeSelect.vue'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const tankerType = props.materials.register(`${props.index}.tankerType`, null)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <AppTankerTypeSelect
      v-bind="toFormField(tankerType)"
      :label="i18n.t('module.pickup_request.update.packaging.placeholder.tanker_type')"
      :is-label-hidden="true"
    />
  </PickupRequestDetailsFormTableCell>
</template>
