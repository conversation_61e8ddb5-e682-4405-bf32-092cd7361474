<script setup lang="ts">
import { VcNumberField } from '@wisemen/vue-core-components'
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const quantityLabels = props.materials.register(`${props.index}.quantityLabels`, null)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <VcNumberField
      v-bind="toFormField(quantityLabels)"
      :hide-controls="true"
      :min="0"
      :max="999"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
    />
  </PickupRequestDetailsFormTableCell>
</template>
