<script setup lang="ts">
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import { WasteMeasurementUnit } from '@/client'
import FormMeasurementField from '@/components/form/fields/FormMeasurementField.vue'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const estimatedWeightOrVolumeValue = props.materials.register(`${props.index}.estimatedWeightOrVolumeValue`, null)
const estimatedWeightOrVolumeUnit = props.materials.register(`${props.index}.estimatedWeightOrVolumeUnit`, WasteMeasurementUnit.TO)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <FormMeasurementField
      v-bind="estimatedWeightOrVolumeValue"
      :unit="estimatedWeightOrVolumeUnit"
      :is-required="false"
      :min="0.01"
      :max="99999999999.99"
      :label="i18n.t('module.pickup_request.update.packaging.fields.estimated_weight_volume')"
      :is-label-hidden="true"
    />
  </PickupRequestDetailsFormTableCell>
</template>
