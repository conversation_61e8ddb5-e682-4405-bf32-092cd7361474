<script setup lang="ts">
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import AppTransportTypeSelect from '@/components/app/select/AppTransportTypeSelect.vue'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const containerTransportType = props.materials.register(`${props.index}.containerTransportType`, null)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <AppTransportTypeSelect
      v-bind="toFormField(containerTransportType)"
      :label="i18n.t('module.pickup_request.update.packaging.placeholder.transport_type')"
      :is-label-hidden="true"
    />
  </PickupRequestDetailsFormTableCelL>
</template>
