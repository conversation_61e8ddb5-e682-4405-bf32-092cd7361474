<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { CustomerUtil } from '@/utils/customer.util'

const props = defineProps<{
  customerCountryCode: CustomerCountryCode | null
  material: PickupRequestWasteMaterialForm
}>()

const i18n = useI18n()

const contractInfoFormatted = computed<string>(() => {
  const contractItemWithoutLeadingZeros = props.material.contractItem?.replace(/^0+/, '') || ''

  return `${props.material.contractNumber} / ${contractItemWithoutLeadingZeros}`
})

const contractInfoFormattedWithTCNumber = computed<string>(() => {
  return `${contractInfoFormatted.value} / ${props.material.tcNumber ?? '-'}`
})
</script>

<template>
  <div
    v-if="material"
    class="
      bg-primary px-3xl py-md relative flex h-full flex-col border-r-2
      border-transparent text-sm whitespace-nowrap shadow duration-100
      group-[&:has(:focus-visible)]/row:bg-brand-50
      group-[&:has(:focus-visible)]/row:border-brand-500
      group-hover/row:bg-brand-50 group-hover/row:border-brand-500
    "
  >
    <p
      class="
        text-tertiary font-semibold
        group-focus-within/row:text-primary
        group-hover/row:text-primary
      "
    >
      {{ props.material.wasteMaterial }}
    </p>
    <template v-if="props.customerCountryCode !== null">
      <p
        v-if="CustomerUtil.isIrish(props.customerCountryCode)"
        class="text-secondary"
      >
        {{ contractInfoFormattedWithTCNumber }}
      </p>
      <p
        v-else
        class="text-secondary"
      >
        {{ contractInfoFormatted }}
      </p>

      <template v-if="CustomerUtil.isIrish(props.customerCountryCode)">
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.material_analysis') }}: {{ props.material.materialAnalysis ?? '-' }}
        </span>
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.delivery_info') }}: {{ props.material.deliveryInfo ?? '-' }}
        </span>
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.process_code') }}: {{ props.material.processCode ?? '-' }}
        </span>
      </template>

      <template v-if="CustomerUtil.isBelgian(props.customerCountryCode)">
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.customer_reference') }}:
          {{ props.material.customerReference ?? '-' }}
        </span>
      </template>

      <template v-if="CustomerUtil.isDutch(props.customerCountryCode)">
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.customer_reference') }}:
          {{ props.material.customerReference ?? '-' }}
        </span>
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.asn') }}: {{ props.material.asn ?? '-' }}
        </span>
      </template>

      <template v-if="CustomerUtil.isGerman(props.customerCountryCode)">
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.ewc_code') }}: {{ props.material.ewcCode ?? '-' }}
        </span>
        <span class="text-secondary text-xs">
          {{ i18n.t('module.pickup_request.update.details.esn_number') }}: {{ props.material.esnNumber ?? '-' }}
        </span>
      </template>
    </template>
  </div>
</template>
