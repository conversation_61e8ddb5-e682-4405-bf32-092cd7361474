<script setup lang="ts">
import { VcTextField } from '@wisemen/vue-core-components'
import type { FieldArray } from 'formango'
import { useI18n } from 'vue-i18n'

import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const costCenter = props.materials.register(`${props.index}.costCenter`, null)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <VcTextField
      v-bind="toFormField(costCenter)"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.long_number')"
    />
  </PickupRequestDetailsFormTableCelL>
</template>
