<script setup lang="ts">
import type { FieldArray } from 'formango'

import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import PickupRequestTransportMaterialUnNumberInfo from '@/modules/pickup-request/features/update/components/steps/transport/PickupRequestTransportMaterialUnNumberInfo.vue'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const unNumber = props.materials.register(`${props.index}.unNumber`)
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <PickupRequestTransportMaterialUnNumberInfo :content="unNumber.value.value?.dangerLabel1" />
  </PickupRequestDetailsFormTableCell>
</template>
