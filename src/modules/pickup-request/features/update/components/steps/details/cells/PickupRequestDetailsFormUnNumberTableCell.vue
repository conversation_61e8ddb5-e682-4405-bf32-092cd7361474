<script setup lang="ts">
import {
  usePagination,
  VcAutocomplete,
} from '@wisemen/vue-core-components'
import type { FieldArray } from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import type { UnNumberIndex } from '@/models/un-number/index/unNumberIndex.model'
import type { UnNumberIndexPagination } from '@/models/un-number/index/unNumberIndexPagination.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { useUnNumberIndexQuery } from '@/modules/un-number/api/queries/unNumberIndex.query'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const unNumber = props.materials.register(`${props.index}.unNumber`)

const pagination = usePagination<UnNumberIndexPagination>({
  isRouteQueryEnabled: false,
  options: {},
})
const unNumberIndexQuery = useUnNumberIndexQuery(pagination.paginationOptions)
const unNumbers = computed<UnNumberIndex[]>(() => unNumberIndexQuery.data.value?.data ?? [])

function onSearch(search: string): void {
  pagination.handleSearchChange(search)
}
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <VcAutocomplete
      v-bind="toFormField(unNumber)"
      :items="unNumbers"
      :is-loading="unNumberIndexQuery.isFetching.value"
      :display-fn="(value) => `${value.number} - ${value.description ?? ''}`"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.un_number')"
      :is-search-term-optional="true"
      :class-config="{
        inlineSearchInput: 'z-3',
      }"
      popover-align="start"
      @search="onSearch"
    />
  </PickupRequestDetailsFormTableCell>
</template>
