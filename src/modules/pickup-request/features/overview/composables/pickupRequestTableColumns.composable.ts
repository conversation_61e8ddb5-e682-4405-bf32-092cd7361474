import type { TableColumn } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import { DynamicColumnNames } from '@/client'
import {
  useGenericAccountManagerColumn,
  useGenericConfirmedTransportDateColumn,
  useGenericContainerNumberColumn,
  useGenericContractItemColumn,
  useGenericContractNumberColumn,
  useGenericCostCenterColumn,
  useGenericCustomerIdColumn,
  useGenericCustomerNameColumn,
  useGenericCustomerReferenceColumn,
  useGenericDateOfRequestColumn,
  useGenericDeliveryInfoColumn,
  useGenericDisposalCertificateNumberColumn,
  useGenericEwcColumn,
  useGenericIsHazardousColumn,
  useGenericIsTransportByIndaverColumn,
  useGenericMaterialAnalysisColumn,
  useGenericNameInstallationColumn,
  useGenericNameOfApplicantColumn,
  useGenericOrderNumberColumn,
  useGenericPickupAddressIdColumn,
  useGenericPickupAddressNameColumn,
  useGenericRequestedEndDateColumn,
  useGenericRequestedStartDateColumn,
  useGenericRequestNumberColumn,
  useGenericSalesOrderColumn,
  useGenericTfsNumberColumn,
  useGenericTransportModeColumn,
  useGenericTreatmentCenterNameColumn,
  useGenericWasteMaterialColumn,
  useGenericWasteProducerIdColumn,
  useGenericWasteProducerNameColumn,
} from '@/composables/table-columns/genericTableColumns.composable'
import type { PickupRequestIndex } from '@/models/pickup-request/index/pickupRequestIndex.model'
import PickupRequestOverviewTableStatusCell from '@/modules/pickup-request/features/overview/components/cells/PickupRequestOverviewTableStatusCell.vue'

export function usePickupRequestTableColumns(): Partial<{
  [K in DynamicColumnNames]: (label: string, isSortable: boolean) => TableColumn<PickupRequestIndex>
}> {
  return {
    [DynamicColumnNames.ACCOUNT_MANAGER]: useGenericAccountManagerColumn,
    [DynamicColumnNames.CONFIRMED_TRANSPORT_DATE]: useGenericConfirmedTransportDateColumn,
    [DynamicColumnNames.CONTAINER_NUMBER]: useGenericContainerNumberColumn,
    [DynamicColumnNames.CONTRACT_ITEM]: useGenericContractItemColumn,
    [DynamicColumnNames.CONTRACT_NUMBER]: useGenericContractNumberColumn,
    [DynamicColumnNames.COST_CENTER]: useGenericCostCenterColumn,
    [DynamicColumnNames.CUSTOMER_ID]: useGenericCustomerIdColumn,
    [DynamicColumnNames.CUSTOMER_NAME]: useGenericCustomerNameColumn,
    [DynamicColumnNames.CUSTOMER_REFERENCE]: useGenericCustomerReferenceColumn,
    [DynamicColumnNames.DATE_OF_REQUEST]: useGenericDateOfRequestColumn,
    [DynamicColumnNames.DELIVERY_INFO]: useGenericDeliveryInfoColumn,
    [DynamicColumnNames.DISPOSAL_CERTIFICATE_NUMBER]: useGenericDisposalCertificateNumberColumn,
    [DynamicColumnNames.EWC]: useGenericEwcColumn,
    [DynamicColumnNames.IS_HAZARDOUS]: useGenericIsHazardousColumn,
    [DynamicColumnNames.IS_TRANSPORT_BY_INDAVER]: useGenericIsTransportByIndaverColumn,
    [DynamicColumnNames.MATERIAL_ANALYSIS]: useGenericMaterialAnalysisColumn,
    [DynamicColumnNames.NAME_INSTALLATION]: useGenericNameInstallationColumn,
    [DynamicColumnNames.NAME_OF_APPLICANT]: useGenericNameOfApplicantColumn,
    [DynamicColumnNames.ORDER_NUMBER]: useGenericOrderNumberColumn,
    [DynamicColumnNames.PICK_UP_ADDRESS_ID]: useGenericPickupAddressIdColumn,
    [DynamicColumnNames.PICK_UP_ADDRESS_NAME]: useGenericPickupAddressNameColumn,
    [DynamicColumnNames.REQUEST_NUMBER]: useGenericRequestNumberColumn,
    [DynamicColumnNames.REQUESTED_END_DATE]: useGenericRequestedEndDateColumn,
    [DynamicColumnNames.REQUESTED_START_DATE]: useGenericRequestedStartDateColumn,
    [DynamicColumnNames.SALES_ORDER]: useGenericSalesOrderColumn,
    [DynamicColumnNames.STATUS]: useStatusColumn,
    [DynamicColumnNames.TFS_NUMBER]: useGenericTfsNumberColumn,
    [DynamicColumnNames.TRANSPORT_MODE]: useGenericTransportModeColumn,
    [DynamicColumnNames.TREATMENT_CENTER_NAME]: useGenericTreatmentCenterNameColumn,
    [DynamicColumnNames.WASTE_MATERIAL]: useGenericWasteMaterialColumn,
    [DynamicColumnNames.WASTE_PRODUCER_ID]: useGenericWasteProducerIdColumn,
    [DynamicColumnNames.WASTE_PRODUCER_NAME]: useGenericWasteProducerNameColumn,
  }
}

function useStatusColumn(
  label: string,
  isSortable: boolean,
): TableColumn<PickupRequestIndex> {
  return {
    isSortable,
    cell: (row): VNode => (
      h(PickupRequestOverviewTableStatusCell, { status: row.status })
    ),
    headerLabel: label,
    key: 'status',
  }
}
