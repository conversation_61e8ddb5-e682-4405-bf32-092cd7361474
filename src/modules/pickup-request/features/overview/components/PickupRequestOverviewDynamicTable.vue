<script setup lang="ts">
import type {
  PaginatedData,
  Pagination,
  TableColumn,
} from '@wisemen/vue-core-components'
import { VcButton } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpRequestStatus } from '@/client'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppErrorState from '@/components/app/error-state/AppErrorState.vue'
import AppTable from '@/components/app/table/AppTable.vue'
import AppTableBulkActions from '@/components/app/table/AppTableBulkActions.vue'
import AppTableDynamicViewsDropdown from '@/components/app/table/AppTableDynamicViewsDropdown.vue'
import AppTableActiveFilters from '@/components/app/table/filters/AppTableActiveFilters.vue'
import AppTableFilters from '@/components/app/table/filters/AppTableFilters.vue'
import TableSettingsPopover from '@/components/table/TableSettingsPopover.vue'
import type { DynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { DynamicTableColumnIndex } from '@/models/dynamic-table/column/dynamicTableColumnIndex.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import { PickupRequestStatusEnumUtil } from '@/models/enums/pickupRequestStatus.enum'
import type { PickupRequestIndex } from '@/models/pickup-request/index/pickupRequestIndex.model'
import { PickupRequestIndexTableTabs } from '@/models/pickup-request/index/pickupRequestIndex.model'
import type { PickupRequestIndexPagination } from '@/models/pickup-request/index/pickupRequestIndexPagination.model'
import { PICKUP_REQUEST_STATUS_FILTER_NAME } from '@/models/pickup-request/index/pickupRequestIndexPagination.model'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import PickupRequestOverviewTableActionsCell from '@/modules/pickup-request/features/overview/components/cells/PickupRequestOverviewTableActionsCell.vue'
import PickupRequestOverviewTableCheckboxCell from '@/modules/pickup-request/features/overview/components/cells/PickupRequestOverviewTableCheckboxCell.vue'
import { usePickupRequestTableColumns } from '@/modules/pickup-request/features/overview/composables/pickupRequestTableColumns.composable'
import type { TableFilter } from '@/types/tableFilter.type'

const props = defineProps<{
  hasActiveViewBeenUpdated: boolean
  hasBulkActions?: boolean
  hasPickupRequestManagePermission: boolean
  isDeleteLoading?: boolean
  isLoading: boolean
  isUpdatingActiveView: boolean
  activeTab: string
  data: PaginatedData<PickupRequestIndex> | null
  dynamicColumns: DynamicTableColumnIndex[]
  dynamicTable: DynamicTable
  error: unknown | null
  pagination: Pagination<PickupRequestIndexPagination>
  tableViews: DynamicTableViewIndex[]
  onViewsGetNextPage: () => Promise<void>
}>()

const emit = defineEmits<{
  bulkDelete: [PickupRequestUuid[]]
  changeView: [DynamicTableViewIndex]
  copy: [PickupRequestIndex]
  edit: [PickupRequestIndex]
  nextPage: []
}>()

const i18n = useI18n()
const activeView = defineModel<DynamicTableViewIndex>('activeView', { required: true })

const itemsSelectedInBulk = defineModel<PickupRequestUuid[]>('itemsSelectedInBulk', { required: true })

const pickupRequestTableColumns = usePickupRequestTableColumns()

const columns = computed<TableColumn<PickupRequestIndex>[]>(() => {
  const cols = props.dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .filter((column) => {
      if (props.activeTab === PickupRequestIndexTableTabs.DRAFTS && column.name === 'status') {
        return false
      }

      return true
    })
    .map((column) => {
      const sort = props.dynamicTable.sorts.value.find((sort) => sort.uuid === column.uuid) ?? null

      const isSortable = sort !== null && !sort.isDisabled && sort.direction !== null

      const columnFn = pickupRequestTableColumns[column.name]

      return columnFn ? columnFn(column.label, isSortable) : null
    })
    .filter((col): col is TableColumn<PickupRequestIndex> => col !== null)

  if (props.hasBulkActions && props.hasPickupRequestManagePermission) {
    cols.unshift({
      cell: (row): VNode => h(PickupRequestOverviewTableCheckboxCell, {
        rowUuid: row.uuid ?? '' as PickupRequestUuid,
        selectedItems: itemsSelectedInBulk.value,
        onUpdate: (value: boolean) => {
          if (row.uuid === null) {
            return
          }
          if (value) {
            itemsSelectedInBulk.value?.push(row.uuid)
          }
          else {
            itemsSelectedInBulk.value = itemsSelectedInBulk.value.filter((uuid) =>
              uuid !== row.uuid) ?? []
          }
        },
      }),
      headerLabel: '',
      key: 'checkbox',
      width: '2.5rem',
    })
  }

  return cols
})

const filters = computed<TableFilter<PickupRequestIndexPagination>[]>(() => {
  return [
    {
      dynamicTableColumnUuid: props.dynamicTable.availableFilters.value.find((filter) =>
        filter.name === PICKUP_REQUEST_STATUS_FILTER_NAME)?.uuid ?? null,
      displayFn: (value: PickUpRequestStatus): string => {
        return i18n.t(PickupRequestStatusEnumUtil.getI18nKey(value))
      },
      items: PickupRequestStatusEnumUtil.getSelectOptions().filter((item) => {
        return item.value !== PickUpRequestStatus.DRAFT
          && item.value !== PickUpRequestStatus.INDASCAN_DRAFT
      }),
      key: 'statuses',
      label: i18n.t('module.waste_inquiry.overview.status'),
      type: 'multiSelect',
    },
  ]
})

function onNextPage(): void {
  emit('nextPage')
}

function onChangeView(view: DynamicTableViewIndex): void {
  emit('changeView', view)
}

async function onViewsGetNextPage(): Promise<void> {
  await props.onViewsGetNextPage()
}

function onEdit(row: PickupRequestIndex): void {
  emit('edit', row)
}

function onCancelBulkSelection(): void {
  itemsSelectedInBulk.value = []
}

function onDeleteInBulk(): void {
  if (itemsSelectedInBulk.value.length === 0) {
    return
  }

  emit('bulkDelete', itemsSelectedInBulk.value)
}

function onCopy(row: PickupRequestIndex): void {
  emit('copy', row)
}
</script>

<template>
  <AppErrorState
    v-if="!isLoading && error !== null"
    :error="error"
  />

  <AppTable
    v-else
    :columns="[
      ...columns,
      {
        cell: (row): VNode => h(PickupRequestOverviewTableActionsCell, {
          hideEditAction: props.activeTab === PickupRequestIndexTableTabs.DRAFTS
            && !props.hasPickupRequestManagePermission,
          canCopy: row.status !== PickUpRequestStatus.DRAFT,
          onEdit: () => onEdit(row),
          onCopy: () => onCopy(row),
        }),
        headerLabel: '',
        key: 'actions',
        maxWidth: 'max-content',
      },
    ]"
    :data="props.data"
    :is-loading="props.isLoading"
    :pagination="props.pagination"
    :is-first-column-sticky="true"
    :is-last-column-sticky="props.activeTab !== PickupRequestIndexTableTabs.DRAFTS
      || props.hasPickupRequestManagePermission"
    :class-config="{
      cell: 'group-hover/row:bg-secondary !py-md',
      row: 'hover:bg-secondary',
      root: 'rounded-tr-lg rounded-tl-none rounded-b-2xl',
    }"
    :is-table-results-hint-hidden="true"
    @next-page="onNextPage"
  >
    <template #top>
      <AppTableBulkActions
        :count="itemsSelectedInBulk.length"
        @cancel-bulk-selection="onCancelBulkSelection"
      >
        <template #actions>
          <VcButton
            :is-loading="props.isDeleteLoading"
            variant="destructive-tertiary"
            @click="onDeleteInBulk"
          >
            {{ i18n.t('module.pickup_request.overview.bulk.delete_draft') }}
          </VcButton>
        </template>
      </AppTableBulkActions>

      <AppAnimateHeight class="!overflow-visible">
        <AppGroup
          v-if="activeTab === PickupRequestIndexTableTabs.SUBMITTED"
          justify="between"
          class="px-xl"
        >
          <AppTableActiveFilters
            :pagination="props.pagination"
            :filters="filters"
            :dynamic-table="props.dynamicTable"
          />
          <AppGroup class="p-md">
            <AppTableDynamicViewsDropdown
              v-model:active-view="activeView"
              :dynamic-table="props.dynamicTable"
              :is-updating-active-view="props.isUpdatingActiveView"
              :table-views="props.tableViews"
              :has-active-view-been-updated="props.hasActiveViewBeenUpdated"
              :is-add-button-disabled="!props.hasActiveViewBeenUpdated"
              :on-views-get-next-page="onViewsGetNextPage"
              @change-view="onChangeView"
            />

            <AppTableFilters
              :pagination="props.pagination"
              :filters="filters"
              :dynamic-table="props.dynamicTable"
            />

            <TableSettingsPopover
              :dynamic-table="props.dynamicTable"
              :table-views="props.tableViews"
              :has-active-view-been-updated="props.hasActiveViewBeenUpdated"
              :active-view="activeView"
            />
          </AppGroup>
        </AppGroup>
      </AppAnimateHeight>
    </template>
  </AppTable>
</template>
