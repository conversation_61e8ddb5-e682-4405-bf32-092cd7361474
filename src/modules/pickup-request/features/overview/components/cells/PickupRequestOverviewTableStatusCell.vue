<script setup lang="ts">
import type { VcBadgeProps } from '@wisemen/vue-core-components'
import {
  VcBadge,
  VcTableCell,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpRequestStatus } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import { PickupRequestStatusEnumUtil } from '@/models/enums/pickupRequestStatus.enum'

const props = defineProps<{
  status: PickUpRequestStatus
}>()

const i18n = useI18n()

const statusBars = [
  [
    PickUpRequestStatus.PENDING,
  ],
  [
    PickUpRequestStatus.CONFIRMED,
  ],
  [
    PickUpRequestStatus.CANCELLED,
  ],
]

const hasReachedFinalStatus = computed<boolean>(() => (
  props.status === PickUpRequestStatus.CANCELLED
))

const statusColor = computed<VcBadgeProps['color']>(() => {
  switch (props.status) {
    case PickUpRequestStatus.DRAFT:
    case PickUpRequestStatus.INDASCAN_DRAFT:
      return 'light-blue'
    case PickUpRequestStatus.PENDING:
      return 'purple'
    case PickUpRequestStatus.CONFIRMED:
      return 'success'
    case PickUpRequestStatus.CANCELLED:
      return 'gray'
    default:
      return 'gray'
  }
})

function isStatusBarActive(status: PickUpRequestStatus[]): boolean {
  return status.includes(props.status)
}

function statusBarWasCompleted(index: number): boolean {
  const currentStatusIndex = statusBars.findIndex((statusBar) => statusBar.includes(props.status))

  return index < currentStatusIndex
}
</script>

<template>
  <VcTableCell>
    <AppGroup gap="2xl">
      <div
        v-if="props.status !== PickUpRequestStatus.INDASCAN_DRAFT"
        class="gap-x-sm flex items-center">
        <div
          v-for="(statusBarStatus, index) of statusBars"
          :key="index"
          :class="{
            'h-1 w-5': isStatusBarActive(statusBarStatus),
            'h-0.5 w-3': !isStatusBarActive(statusBarStatus),
            'bg-brand-solid': isStatusBarActive(statusBarStatus) && !hasReachedFinalStatus,
            'bg-fg-disabled': props.status === PickUpRequestStatus.CANCELLED || props.status === PickUpRequestStatus.CONFIRMED,
            'bg-tertiary': !isStatusBarActive(statusBarStatus) && (hasReachedFinalStatus || !statusBarWasCompleted(index)),
            'bg-brand-primary': !isStatusBarActive(statusBarStatus) && !hasReachedFinalStatus && statusBarWasCompleted(index),
          }"
          class="rounded-full"
        />
      </div>

      <VcBadge
        :color="statusColor"
        variant="translucent"
        size="sm"
      >
        {{ i18n.t(PickupRequestStatusEnumUtil.getI18nKey(props.status)) }}
      </VcBadge>
    </AppGroup>
  </VcTableCell>
</template>
