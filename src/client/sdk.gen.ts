// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-fetch';
import type { MigrateCollectionsV1Data, MigrateCollectionsV1Error, ImportCollectionsV1Data, ImportCollectionsV1Error, ViewCollectionsV1Data, ViewCollectionsV1Error, ViewMeV1Data, ViewMeV1Response, ViewMeV1Error, ViewUserDetailV1Data, ViewUserDetailV1Response, ViewUserDetailV1Error, ViewUserIndexV1Data, ViewUserIndexV1Response, ViewUserIndexV1Error, ViewPermissionIndexV1Data, ViewPermissionIndexV1Response, ViewPermissionIndexV1Error, ViewCustomerIndexV1Data, ViewCustomerIndexV1Response, ViewCustomerIndexV1Error, ViewSuggestedCustomersV1Data, ViewSuggestedCustomersV1Response, ViewSuggestedCustomersV1Error, ViewCustomerCountryV1Data, ViewCustomerCountryV1Response, ViewCustomerCountryV1Error, ViewDomainEventLogIndexV1Data, ViewDomainEventLogIndexV1Response, ViewDomainEventLogIndexV1Error, CreateFileV1Data, CreateFileV1Response, CreateFileV1Error, ConfirmFileUploadV1Data, ConfirmFileUploadV1Error, DownloadFileV1Data, DownloadFileV1Error, ProxyExternalFileV1Data, ProxyExternalFileV1Error, SearchCollectionsV1Data, SearchCollectionsV1Response, SearchCollectionsV1Error, ViewJobsIndexV1Data, ViewJobsIndexV1Response, ViewJobsIndexV1Error, ViewJobDetailV1Data, ViewJobDetailV1Response, ViewJobDetailV1Error, GetMyNotificationPreferencesV1Data, GetMyNotificationPreferencesV1Response, GetMyNotificationPreferencesV1Error, GetNotificationTypesConfigV1Data, GetNotificationTypesConfigV1Response, GetNotificationTypesConfigV1Error, UpdateMyChannelNotificationPreferenceV1Data, UpdateMyChannelNotificationPreferenceV1Response, UpdateMyChannelNotificationPreferenceV1Error, SendTestNotificationV1Data, SendTestNotificationV1Response, SendTestNotificationV1Error, GetMyNotificationsV1Data, GetMyNotificationsV1Response, GetMyNotificationsV1Error, ViewUnreadNotificationsCountV1Data, ViewUnreadNotificationsCountV1Response, ViewUnreadNotificationsCountV1Error, ViewUserNotificationDetailV1Data, ViewUserNotificationDetailV1Response, ViewUserNotificationDetailV1Error, MarkAllNotificationAsReadV1Data, MarkAllNotificationAsReadV1Response, MarkAllNotificationAsReadV1Error, UpdateMyNotificationTypePreferenceV1Data, UpdateMyNotificationTypePreferenceV1Response, UpdateMyNotificationTypePreferenceV1Error, MarkNotificationAsReadV1Data, MarkNotificationAsReadV1Response, MarkNotificationAsReadV1Error, MarkNotificationAsUnreadV1Data, MarkNotificationAsUnreadV1Response, MarkNotificationAsUnreadV1Error, UpdateMyNotificationPreferencePresetV1Data, UpdateMyNotificationPreferencePresetV1Response, UpdateMyNotificationPreferencePresetV1Error, MigrateNotificationTypesV1Data, MigrateNotificationTypesV1Response, MigrateNotificationTypesV1Error, ViewRoleIndexV1Data, ViewRoleIndexV1Response, ViewRoleIndexV1Error, UpdateRolesPermissionsV1Data, UpdateRolesPermissionsV1Response, UpdateRolesPermissionsV1Error, CreateRoleV1Data, CreateRoleV1Response, CreateRoleV1Error, ClearRolePermissionsCacheV1Data, ClearRolePermissionsCacheV1Response, ClearRolePermissionsCacheV1Error, DeleteRoleV1Data, DeleteRoleV1Response, DeleteRoleV1Error, ViewRoleDetailV1Data, ViewRoleDetailV1Response, ViewRoleDetailV1Error, UpdateRoleV1Data, UpdateRoleV1Error, GetApiInfoData, GetApiInfoResponse2, GetApiInfoError, SwaggerData, SwaggerError, ViewUiPreferencesV1Data, ViewUiPreferencesV1Response, ViewUiPreferencesV1Error, UpdateUiPreferencesV1Data, UpdateUiPreferencesV1Error, ViewAnnouncementIndexV1Data, ViewAnnouncementIndexV1Response, ViewAnnouncementIndexV1Error, CreateAnnouncementV1Data, CreateAnnouncementV1Response, CreateAnnouncementV1Error, DeleteAnnouncementV1Data, DeleteAnnouncementV1Error, ViewAnnouncementV1Data, ViewAnnouncementV1Response, ViewAnnouncementV1Error, UpdateAnnouncementV1Data, UpdateAnnouncementV1Response, UpdateAnnouncementV1Error, ViewDashboardAnnouncementIndexV1Data, ViewDashboardAnnouncementIndexV1Response, ViewDashboardAnnouncementIndexV1Error, ViewDashboardAnnouncementV1Data, ViewDashboardAnnouncementV1Response, ViewDashboardAnnouncementV1Error, ViewContactIndexV1Data, ViewContactIndexV1Response, ViewContactIndexV1Error, CreateContactV1Data, CreateContactV1Response, CreateContactV1Error, DeleteContactV1Data, DeleteContactV1Response, DeleteContactV1Error, UpdateContactV1Data, UpdateContactV1Response, UpdateContactV1Error, ViewContainerTypeIndexV1Data, ViewContainerTypeIndexV1Response, ViewContainerTypeIndexV1Error, ViewContractLineIndexV1Data, ViewContractLineIndexV1Response, ViewContractLineIndexV1Error, ViewWprContractLineIndexV1Data, ViewWprContractLineIndexV1Response, ViewWprContractLineIndexV1Error, ViewPackagingRequestContractLineIndexV1Data, ViewPackagingRequestContractLineIndexV1Response, ViewPackagingRequestContractLineIndexV1Error, GenerateContractLinesPdfV1Data, GenerateContractLinesPdfV1Response, GenerateContractLinesPdfV1Error, DownloadDocumentV1Data, DownloadDocumentV1Error, ViewDocumentIndexV1Data, ViewDocumentIndexV1Response, ViewDocumentIndexV1Error, ViewUserSiteIndexV1Data, ViewUserSiteIndexV1Response, ViewUserSiteIndexV1Error, ViewDynamicTableColumnIndexV1Data, ViewDynamicTableColumnIndexV1Response, ViewDynamicTableColumnIndexV1Error, ViewDynamicTableViewIndexV1Data, ViewDynamicTableViewIndexV1Response, ViewDynamicTableViewIndexV1Error, CreateDynamicTableViewV1Data, CreateDynamicTableViewV1Response, CreateDynamicTableViewV1Error, ViewDefaultDynamicTableViewV1Data, ViewDefaultDynamicTableViewV1Response, ViewDefaultDynamicTableViewV1Error, DeleteDynamicTableViewV1Data, DeleteDynamicTableViewV1Error, UpdateDynamicTableViewV1Data, UpdateDynamicTableViewV1Response, UpdateDynamicTableViewV1Error, ViewEwcCodeIndexV1Data, ViewEwcCodeIndexV1Response, ViewEwcCodeIndexV1Error, ViewInvoiceIndexV1Data, ViewInvoiceIndexV1Response, ViewInvoiceIndexV1Error, ViewDraftInvoiceIndexV1Data, ViewDraftInvoiceIndexV1Response, ViewDraftInvoiceIndexV1Error, ApproveDraftInvoiceV1Data, ApproveDraftInvoiceV1Error, RejectDraftInvoiceV1Data, RejectDraftInvoiceV1Error, SubscribeToNewsletterV1Data, SubscribeToNewsletterV1Response, SubscribeToNewsletterV1Error, ViewNewsItemIndexV1Data, ViewNewsItemIndexV1Response, ViewNewsItemIndexV1Error, CreateNewsItemV1Data, CreateNewsItemV1Response, CreateNewsItemV1Error, DeleteNewsItemV1Data, DeleteNewsItemV1Error, ViewNewsItemV1Data, ViewNewsItemV1Response, ViewNewsItemV1Error, UpdateNewsItemV1Data, UpdateNewsItemV1Response, UpdateNewsItemV1Error, ViewDashboardNewsItemIndexV1Data, ViewDashboardNewsItemIndexV1Response, ViewDashboardNewsItemIndexV1Error, ViewDashboardNewsItemV1Data, ViewDashboardNewsItemV1Response, ViewDashboardNewsItemV1Error, CreatePackagingRequestV1Data, CreatePackagingRequestV1Response, CreatePackagingRequestV1Error, ViewPackagingRequestV1Data, ViewPackagingRequestV1Response, ViewPackagingRequestV1Error, UpdatePackagingRequestV1Data, UpdatePackagingRequestV1Response, UpdatePackagingRequestV1Error, SubmitPackagingRequestV1Data, SubmitPackagingRequestV1Response, SubmitPackagingRequestV1Error, BulkDeletePackagingRequestV1Data, BulkDeletePackagingRequestV1Error, CopyPackagingRequestSapV1Data, CopyPackagingRequestSapV1Response, CopyPackagingRequestSapV1Error, ViewPackagingTypeIndexV1Data, ViewPackagingTypeIndexV1Response, ViewPackagingTypeIndexV1Error, ViewPickUpAddressIndexV1Data, ViewPickUpAddressIndexV1Response, ViewPickUpAddressIndexV1Error, ViewSuggestedPickUpAddressesV1Data, ViewSuggestedPickUpAddressesV1Response, ViewSuggestedPickUpAddressesV1Error, GetIsPoNumberAndCostCenterRequiredV1Data, GetIsPoNumberAndCostCenterRequiredV1Response, GetIsPoNumberAndCostCenterRequiredV1Error, ViewPickUpRequestIndexV1Data, ViewPickUpRequestIndexV1Response, ViewPickUpRequestIndexV1Error, CreatePickUpRequestV1Data, CreatePickUpRequestV1Response, CreatePickUpRequestV1Error, ViewPickUpRequestV1Data, ViewPickUpRequestV1Response, ViewPickUpRequestV1Error, UpdatePickUpRequestV1Data, UpdatePickUpRequestV1Response, UpdatePickUpRequestV1Error, ViewWasteProducerIndexV1Data, ViewWasteProducerIndexV1Response, ViewWasteProducerIndexV1Error, ViewSuggestedWasteProducersV1Data, ViewSuggestedWasteProducersV1Response, ViewSuggestedWasteProducersV1Error, SubmitPickUpRequestV1Data, SubmitPickUpRequestV1Response, SubmitPickUpRequestV1Error, BulkDeletePickUpRequestV1Data, BulkDeletePickUpRequestV1Error, ViewPickUpRequestSapV1Data, ViewPickUpRequestSapV1Response, ViewPickUpRequestSapV1Error, UpdatePickUpRequestSapV1Data, UpdatePickUpRequestSapV1Error, CopyPickUpRequestSapV1Data, CopyPickUpRequestSapV1Response, CopyPickUpRequestSapV1Error, SubmitPickUpRequestSapV1Data, SubmitPickUpRequestSapV1Error, ViewTankerTypeIndexV1Data, ViewTankerTypeIndexV1Response, ViewTankerTypeIndexV1Error, ViewTransportTypeIndexV1Data, ViewTransportTypeIndexV1Response, ViewTransportTypeIndexV1Error, ViewUnNumberIndexV1Data, ViewUnNumberIndexV1Response, ViewUnNumberIndexV1Error, ViewUnNumberIndexForPickUpRequestV1Data, ViewUnNumberIndexForPickUpRequestV1Response, ViewUnNumberIndexForPickUpRequestV1Error, ViewWasteInquiryIndexV1Data, ViewWasteInquiryIndexV1Response, ViewWasteInquiryIndexV1Error, CreateWasteInquiryV1Data, CreateWasteInquiryV1Response, CreateWasteInquiryV1Error, ViewWasteInquiryV1Data, ViewWasteInquiryV1Response, ViewWasteInquiryV1Error, UpdateWasteInquiryV1Data, UpdateWasteInquiryV1Response, UpdateWasteInquiryV1Error, SubmitWasteInquiryV1Data, SubmitWasteInquiryV1Response, SubmitWasteInquiryV1Error, ViewWasteInquirySapV1Data, ViewWasteInquirySapV1Response, ViewWasteInquirySapV1Error, AddDocumentToWasteInquirySapV1Data, AddDocumentToWasteInquirySapV1Response, AddDocumentToWasteInquirySapV1Error, BulkDeleteWasteInquiryV1Data, BulkDeleteWasteInquiryV1Error, CopyWasteInquirySapV1Data, CopyWasteInquirySapV1Response, CopyWasteInquirySapV1Error, CreateWeeklyPlanningRequestV1Data, CreateWeeklyPlanningRequestV1Response, CreateWeeklyPlanningRequestV1Error, ViewWeeklyPlanningRequestV1Data, ViewWeeklyPlanningRequestV1Response, ViewWeeklyPlanningRequestV1Error, UpdateWeeklyPlanningRequestV1Data, UpdateWeeklyPlanningRequestV1Response, UpdateWeeklyPlanningRequestV1Error, AddWprPickUpRequestV1Data, AddWprPickUpRequestV1Response, AddWprPickUpRequestV1Error, SubmitWeeklyPlanningRequestV1Data, SubmitWeeklyPlanningRequestV1Response, SubmitWeeklyPlanningRequestV1Error } from './types.gen';
import { client as _heyApiClient } from './client.gen';
import { zViewMeV1Response, zViewUserDetailV1Response, zViewUserIndexV1Response, zViewPermissionIndexV1Response, zViewCustomerIndexV1Response, zViewSuggestedCustomersV1Response, zViewCustomerCountryV1Response, zViewDomainEventLogIndexV1Response, zCreateFileV1Response, zSearchCollectionsV1Response, zViewJobsIndexV1Response, zViewJobDetailV1Response, zGetMyNotificationPreferencesV1Response, zGetNotificationTypesConfigV1Response, zUpdateMyChannelNotificationPreferenceV1Response, zSendTestNotificationV1Response, zGetMyNotificationsV1Response, zViewUnreadNotificationsCountV1Response, zViewUserNotificationDetailV1Response, zMarkAllNotificationAsReadV1Response, zUpdateMyNotificationTypePreferenceV1Response, zMarkNotificationAsReadV1Response, zMarkNotificationAsUnreadV1Response, zUpdateMyNotificationPreferencePresetV1Response, zMigrateNotificationTypesV1Response, zViewRoleIndexV1Response, zUpdateRolesPermissionsV1Response, zCreateRoleV1Response, zClearRolePermissionsCacheV1Response, zDeleteRoleV1Response, zViewRoleDetailV1Response, zGetApiInfoResponse2, zViewUiPreferencesV1Response, zViewAnnouncementIndexV1Response, zCreateAnnouncementV1Response, zViewAnnouncementV1Response, zUpdateAnnouncementV1Response, zViewDashboardAnnouncementIndexV1Response, zViewDashboardAnnouncementV1Response, zViewContactIndexV1Response, zCreateContactV1Response, zDeleteContactV1Response, zUpdateContactV1Response, zViewContainerTypeIndexV1Response, zViewContractLineIndexV1Response, zViewWprContractLineIndexV1Response, zViewPackagingRequestContractLineIndexV1Response, zGenerateContractLinesPdfV1Response, zViewDocumentIndexV1Response, zViewUserSiteIndexV1Response, zViewDynamicTableColumnIndexV1Response, zViewDynamicTableViewIndexV1Response, zCreateDynamicTableViewV1Response, zViewDefaultDynamicTableViewV1Response, zUpdateDynamicTableViewV1Response, zViewEwcCodeIndexV1Response, zViewInvoiceIndexV1Response, zViewDraftInvoiceIndexV1Response, zSubscribeToNewsletterV1Response, zViewNewsItemIndexV1Response, zCreateNewsItemV1Response, zViewNewsItemV1Response, zUpdateNewsItemV1Response, zViewDashboardNewsItemIndexV1Response, zViewDashboardNewsItemV1Response, zCreatePackagingRequestV1Response, zViewPackagingRequestV1Response, zUpdatePackagingRequestV1Response, zSubmitPackagingRequestV1Response, zCopyPackagingRequestSapV1Response, zViewPackagingTypeIndexV1Response, zViewPickUpAddressIndexV1Response, zViewSuggestedPickUpAddressesV1Response, zGetIsPoNumberAndCostCenterRequiredV1Response, zViewPickUpRequestIndexV1Response, zCreatePickUpRequestV1Response, zViewPickUpRequestV1Response, zUpdatePickUpRequestV1Response, zViewWasteProducerIndexV1Response, zViewSuggestedWasteProducersV1Response, zSubmitPickUpRequestV1Response, zViewPickUpRequestSapV1Response, zCopyPickUpRequestSapV1Response, zViewTankerTypeIndexV1Response, zViewTransportTypeIndexV1Response, zViewUnNumberIndexV1Response, zViewUnNumberIndexForPickUpRequestV1Response, zViewWasteInquiryIndexV1Response, zCreateWasteInquiryV1Response, zViewWasteInquiryV1Response, zUpdateWasteInquiryV1Response, zSubmitWasteInquiryV1Response, zViewWasteInquirySapV1Response, zAddDocumentToWasteInquirySapV1Response, zCopyWasteInquirySapV1Response, zCreateWeeklyPlanningRequestV1Response, zViewWeeklyPlanningRequestV1Response, zUpdateWeeklyPlanningRequestV1Response, zAddWprPickUpRequestV1Response, zSubmitWeeklyPlanningRequestV1Response } from './zod.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
};

export const migrateCollectionsV1 = <ThrowOnError extends boolean = true>(options?: Options<MigrateCollectionsV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, MigrateCollectionsV1Error, ThrowOnError>({
        url: '/api/v1/typesense/migrate',
        ...options
    });
};

export const importCollectionsV1 = <ThrowOnError extends boolean = true>(options?: Options<ImportCollectionsV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, ImportCollectionsV1Error, ThrowOnError>({
        url: '/api/v1/typesense/import',
        ...options
    });
};

export const viewCollectionsV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewCollectionsV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, ViewCollectionsV1Error, ThrowOnError>({
        url: '/api/v1/typesense/collections',
        ...options
    });
};

export const viewMeV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewMeV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewMeV1Response, ViewMeV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewMeV1Response.parseAsync(data);
        },
        url: '/api/v1/users/me',
        ...options
    });
};

export const viewUserDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewUserDetailV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewUserDetailV1Response, ViewUserDetailV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUserDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/users/{uuid}',
        ...options
    });
};

export const viewUserIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUserIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewUserIndexV1Response, ViewUserIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUserIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/users',
        ...options
    });
};

export const viewPermissionIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewPermissionIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewPermissionIndexV1Response, ViewPermissionIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPermissionIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/permissions',
        ...options
    });
};

export const viewCustomerIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewCustomerIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewCustomerIndexV1Response, ViewCustomerIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewCustomerIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/customers',
        ...options
    });
};

export const viewSuggestedCustomersV1 = <ThrowOnError extends boolean = true>(options: Options<ViewSuggestedCustomersV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewSuggestedCustomersV1Response, ViewSuggestedCustomersV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewSuggestedCustomersV1Response.parseAsync(data);
        },
        url: '/api/v1/suggested-customers',
        ...options
    });
};

export const viewCustomerCountryV1 = <ThrowOnError extends boolean = true>(options: Options<ViewCustomerCountryV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewCustomerCountryV1Response, ViewCustomerCountryV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewCustomerCountryV1Response.parseAsync(data);
        },
        url: '/api/v1/customers/{id}/country',
        ...options
    });
};

export const viewDomainEventLogIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewDomainEventLogIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewDomainEventLogIndexV1Response, ViewDomainEventLogIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDomainEventLogIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/event-logs',
        ...options
    });
};

export const createFileV1 = <ThrowOnError extends boolean = true>(options: Options<CreateFileV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateFileV1Response, CreateFileV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateFileV1Response.parseAsync(data);
        },
        url: '/api/v1/files',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const confirmFileUploadV1 = <ThrowOnError extends boolean = true>(options: Options<ConfirmFileUploadV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, ConfirmFileUploadV1Error, ThrowOnError>({
        url: '/api/v1/files/{file}/confirm-upload',
        ...options
    });
};

export const downloadFileV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadFileV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, DownloadFileV1Error, ThrowOnError>({
        url: '/api/v1/files/{file}/download',
        ...options
    });
};

export const proxyExternalFileV1 = <ThrowOnError extends boolean = true>(options: Options<ProxyExternalFileV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, ProxyExternalFileV1Error, ThrowOnError>({
        url: '/api/v1/files/proxy-external',
        ...options
    });
};

export const searchCollectionsV1 = <ThrowOnError extends boolean = true>(options: Options<SearchCollectionsV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<SearchCollectionsV1Response, SearchCollectionsV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSearchCollectionsV1Response.parseAsync(data);
        },
        url: '/api/v1/search',
        ...options
    });
};

export const viewJobsIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewJobsIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewJobsIndexV1Response, ViewJobsIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewJobsIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/jobs',
        ...options
    });
};

export const viewJobDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewJobDetailV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewJobDetailV1Response, ViewJobDetailV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewJobDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/jobs/{jobId}',
        ...options
    });
};

export const getMyNotificationPreferencesV1 = <ThrowOnError extends boolean = true>(options?: Options<GetMyNotificationPreferencesV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetMyNotificationPreferencesV1Response, GetMyNotificationPreferencesV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zGetMyNotificationPreferencesV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences',
        ...options
    });
};

export const getNotificationTypesConfigV1 = <ThrowOnError extends boolean = true>(options?: Options<GetNotificationTypesConfigV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetNotificationTypesConfigV1Response, GetNotificationTypesConfigV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zGetNotificationTypesConfigV1Response.parseAsync(data);
        },
        url: '/api/v1/notification-preferences/config',
        ...options
    });
};

export const updateMyChannelNotificationPreferenceV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateMyChannelNotificationPreferenceV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateMyChannelNotificationPreferenceV1Response, UpdateMyChannelNotificationPreferenceV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateMyChannelNotificationPreferenceV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences/channels',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const sendTestNotificationV1 = <ThrowOnError extends boolean = true>(options: Options<SendTestNotificationV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SendTestNotificationV1Response, SendTestNotificationV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSendTestNotificationV1Response.parseAsync(data);
        },
        url: '/api/v1/notifications/test-notification',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getMyNotificationsV1 = <ThrowOnError extends boolean = true>(options?: Options<GetMyNotificationsV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetMyNotificationsV1Response, GetMyNotificationsV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zGetMyNotificationsV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications',
        ...options
    });
};

export const viewUnreadNotificationsCountV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUnreadNotificationsCountV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewUnreadNotificationsCountV1Response, ViewUnreadNotificationsCountV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUnreadNotificationsCountV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/unread-count',
        ...options
    });
};

export const viewUserNotificationDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewUserNotificationDetailV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewUserNotificationDetailV1Response, ViewUserNotificationDetailV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUserNotificationDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/{notificationUuid}',
        ...options
    });
};

export const markAllNotificationAsReadV1 = <ThrowOnError extends boolean = true>(options?: Options<MarkAllNotificationAsReadV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<MarkAllNotificationAsReadV1Response, MarkAllNotificationAsReadV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zMarkAllNotificationAsReadV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/mark-as-read',
        ...options
    });
};

export const updateMyNotificationTypePreferenceV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateMyNotificationTypePreferenceV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateMyNotificationTypePreferenceV1Response, UpdateMyNotificationTypePreferenceV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateMyNotificationTypePreferenceV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences/types',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const markNotificationAsReadV1 = <ThrowOnError extends boolean = true>(options: Options<MarkNotificationAsReadV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<MarkNotificationAsReadV1Response, MarkNotificationAsReadV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zMarkNotificationAsReadV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/{notificationUuid}/mark-as-read',
        ...options
    });
};

export const markNotificationAsUnreadV1 = <ThrowOnError extends boolean = true>(options: Options<MarkNotificationAsUnreadV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<MarkNotificationAsUnreadV1Response, MarkNotificationAsUnreadV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zMarkNotificationAsUnreadV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/{notificationUuid}/mark-as-unread',
        ...options
    });
};

export const updateMyNotificationPreferencePresetV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateMyNotificationPreferencePresetV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateMyNotificationPreferencePresetV1Response, UpdateMyNotificationPreferencePresetV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateMyNotificationPreferencePresetV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences/preset',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const migrateNotificationTypesV1 = <ThrowOnError extends boolean = true>(options: Options<MigrateNotificationTypesV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<MigrateNotificationTypesV1Response, MigrateNotificationTypesV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zMigrateNotificationTypesV1Response.parseAsync(data);
        },
        url: '/api/v1/notifications/migrate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewRoleIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewRoleIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewRoleIndexV1Response, ViewRoleIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewRoleIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/roles',
        ...options
    });
};

export const updateRolesPermissionsV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateRolesPermissionsV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateRolesPermissionsV1Response, UpdateRolesPermissionsV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateRolesPermissionsV1Response.parseAsync(data);
        },
        url: '/api/v1/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const createRoleV1 = <ThrowOnError extends boolean = true>(options: Options<CreateRoleV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateRoleV1Response, CreateRoleV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateRoleV1Response.parseAsync(data);
        },
        url: '/api/v1/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const clearRolePermissionsCacheV1 = <ThrowOnError extends boolean = true>(options: Options<ClearRolePermissionsCacheV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ClearRolePermissionsCacheV1Response, ClearRolePermissionsCacheV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zClearRolePermissionsCacheV1Response.parseAsync(data);
        },
        url: '/api/v1/roles/clear-cache',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteRoleV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteRoleV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteRoleV1Response, DeleteRoleV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zDeleteRoleV1Response.parseAsync(data);
        },
        url: '/api/v1/roles/{role}',
        ...options
    });
};

export const viewRoleDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewRoleDetailV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewRoleDetailV1Response, ViewRoleDetailV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewRoleDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/roles/{role}',
        ...options
    });
};

export const updateRoleV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateRoleV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, UpdateRoleV1Error, ThrowOnError>({
        url: '/api/v1/roles/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiInfo = <ThrowOnError extends boolean = true>(options?: Options<GetApiInfoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiInfoResponse2, GetApiInfoError, ThrowOnError>({
        responseValidator: async (data) => {
            return await zGetApiInfoResponse2.parseAsync(data);
        },
        url: '/api',
        ...options
    });
};

export const swagger = <ThrowOnError extends boolean = true>(options?: Options<SwaggerData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, SwaggerError, ThrowOnError>({
        url: '/api/oauth2-redirect',
        ...options
    });
};

export const viewUiPreferencesV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUiPreferencesV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewUiPreferencesV1Response, ViewUiPreferencesV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUiPreferencesV1Response.parseAsync(data);
        },
        url: '/api/v1/me/ui-preferences',
        ...options
    });
};

export const updateUiPreferencesV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateUiPreferencesV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<unknown, UpdateUiPreferencesV1Error, ThrowOnError>({
        url: '/api/v1/me/ui-preferences',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewAnnouncementIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewAnnouncementIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewAnnouncementIndexV1Response, ViewAnnouncementIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewAnnouncementIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements',
        ...options
    });
};

export const createAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<CreateAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateAnnouncementV1Response, CreateAnnouncementV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, DeleteAnnouncementV1Error, ThrowOnError>({
        url: '/api/v1/announcements/{uuid}',
        ...options
    });
};

export const viewAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<ViewAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewAnnouncementV1Response, ViewAnnouncementV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements/{uuid}',
        ...options
    });
};

export const updateAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateAnnouncementV1Response, UpdateAnnouncementV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewDashboardAnnouncementIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewDashboardAnnouncementIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewDashboardAnnouncementIndexV1Response, ViewDashboardAnnouncementIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDashboardAnnouncementIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-announcements',
        ...options
    });
};

export const viewDashboardAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDashboardAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDashboardAnnouncementV1Response, ViewDashboardAnnouncementV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDashboardAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-announcements/{uuid}',
        ...options
    });
};

export const viewContactIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewContactIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewContactIndexV1Response, ViewContactIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewContactIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts',
        ...options
    });
};

export const createContactV1 = <ThrowOnError extends boolean = true>(options: Options<CreateContactV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateContactV1Response, CreateContactV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateContactV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteContactV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteContactV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteContactV1Response, DeleteContactV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zDeleteContactV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts/{uuid}',
        ...options
    });
};

export const updateContactV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateContactV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateContactV1Response, UpdateContactV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateContactV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewContainerTypeIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewContainerTypeIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewContainerTypeIndexV1Response, ViewContainerTypeIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewContainerTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/container-types',
        ...options
    });
};

export const viewContractLineIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewContractLineIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewContractLineIndexV1Response, ViewContractLineIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewContractLineIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines',
        ...options
    });
};

export const viewWprContractLineIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWprContractLineIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewWprContractLineIndexV1Response, ViewWprContractLineIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewWprContractLineIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines/weekly-planning-requests',
        ...options
    });
};

export const viewPackagingRequestContractLineIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPackagingRequestContractLineIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPackagingRequestContractLineIndexV1Response, ViewPackagingRequestContractLineIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPackagingRequestContractLineIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines/packaging-requests',
        ...options
    });
};

export const generateContractLinesPdfV1 = <ThrowOnError extends boolean = true>(options: Options<GenerateContractLinesPdfV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GenerateContractLinesPdfV1Response, GenerateContractLinesPdfV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zGenerateContractLinesPdfV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines/generate-pdf',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const downloadDocumentV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadDocumentV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, DownloadDocumentV1Error, ThrowOnError>({
        url: '/api/v1/documents/download',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewDocumentIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDocumentIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDocumentIndexV1Response, ViewDocumentIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDocumentIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/documents',
        ...options
    });
};

export const viewUserSiteIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUserSiteIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewUserSiteIndexV1Response, ViewUserSiteIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUserSiteIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/documents/sites',
        ...options
    });
};

export const viewDynamicTableColumnIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDynamicTableColumnIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDynamicTableColumnIndexV1Response, ViewDynamicTableColumnIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDynamicTableColumnIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/columns',
        ...options
    });
};

export const viewDynamicTableViewIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDynamicTableViewIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDynamicTableViewIndexV1Response, ViewDynamicTableViewIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDynamicTableViewIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views',
        ...options
    });
};

export const createDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<CreateDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateDynamicTableViewV1Response, CreateDynamicTableViewV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateDynamicTableViewV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewDefaultDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDefaultDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDefaultDynamicTableViewV1Response, ViewDefaultDynamicTableViewV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDefaultDynamicTableViewV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/default-view',
        ...options
    });
};

export const deleteDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, DeleteDynamicTableViewV1Error, ThrowOnError>({
        url: '/api/v1/dynamic-tables/{name}/views/{uuid}',
        ...options
    });
};

export const updateDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateDynamicTableViewV1Response, UpdateDynamicTableViewV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateDynamicTableViewV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewEwcCodeIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewEwcCodeIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewEwcCodeIndexV1Response, ViewEwcCodeIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewEwcCodeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/ewc-codes',
        ...options
    });
};

export const viewInvoiceIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewInvoiceIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewInvoiceIndexV1Response, ViewInvoiceIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewInvoiceIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/invoices',
        ...options
    });
};

export const viewDraftInvoiceIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDraftInvoiceIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDraftInvoiceIndexV1Response, ViewDraftInvoiceIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDraftInvoiceIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/invoices/draft',
        ...options
    });
};

export const approveDraftInvoiceV1 = <ThrowOnError extends boolean = true>(options: Options<ApproveDraftInvoiceV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, ApproveDraftInvoiceV1Error, ThrowOnError>({
        url: '/api/v1/invoices/draft/{invoiceNumber}/approve',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const rejectDraftInvoiceV1 = <ThrowOnError extends boolean = true>(options: Options<RejectDraftInvoiceV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, RejectDraftInvoiceV1Error, ThrowOnError>({
        url: '/api/v1/invoices/draft/{invoiceNumber}/reject',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const subscribeToNewsletterV1 = <ThrowOnError extends boolean = true>(options: Options<SubscribeToNewsletterV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SubscribeToNewsletterV1Response, SubscribeToNewsletterV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSubscribeToNewsletterV1Response.parseAsync(data);
        },
        url: '/api/v1/newsletters/subscribe',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewNewsItemIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewNewsItemIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewNewsItemIndexV1Response, ViewNewsItemIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewNewsItemIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items',
        ...options
    });
};

export const createNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<CreateNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateNewsItemV1Response, CreateNewsItemV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, DeleteNewsItemV1Error, ThrowOnError>({
        url: '/api/v1/news-items/{uuid}',
        ...options
    });
};

export const viewNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<ViewNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewNewsItemV1Response, ViewNewsItemV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items/{uuid}',
        ...options
    });
};

export const updateNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateNewsItemV1Response, UpdateNewsItemV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewDashboardNewsItemIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewDashboardNewsItemIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewDashboardNewsItemIndexV1Response, ViewDashboardNewsItemIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDashboardNewsItemIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-news-items',
        ...options
    });
};

export const viewDashboardNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDashboardNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewDashboardNewsItemV1Response, ViewDashboardNewsItemV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewDashboardNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-news-items/{uuid}',
        ...options
    });
};

export const createPackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<CreatePackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreatePackagingRequestV1Response, CreatePackagingRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreatePackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewPackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPackagingRequestV1Response, ViewPackagingRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/{uuid}',
        ...options
    });
};

export const updatePackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdatePackagingRequestV1Response, UpdatePackagingRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdatePackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const submitPackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitPackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SubmitPackagingRequestV1Response, SubmitPackagingRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSubmitPackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/{uuid}/submit',
        ...options
    });
};

export const bulkDeletePackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeletePackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, BulkDeletePackagingRequestV1Error, ThrowOnError>({
        url: '/api/v1/packaging-requests/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const copyPackagingRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<CopyPackagingRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CopyPackagingRequestSapV1Response, CopyPackagingRequestSapV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCopyPackagingRequestSapV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/sap/{requestNumber}/copy',
        ...options
    });
};

export const viewPackagingTypeIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPackagingTypeIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPackagingTypeIndexV1Response, ViewPackagingTypeIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPackagingTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-types',
        ...options
    });
};

export const viewPickUpAddressIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpAddressIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPickUpAddressIndexV1Response, ViewPickUpAddressIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPickUpAddressIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-addresses',
        ...options
    });
};

export const viewSuggestedPickUpAddressesV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewSuggestedPickUpAddressesV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewSuggestedPickUpAddressesV1Response, ViewSuggestedPickUpAddressesV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewSuggestedPickUpAddressesV1Response.parseAsync(data);
        },
        url: '/api/v1/suggested-pick-up-addresses',
        ...options
    });
};

/**
 * Check if PO number and cost center are required
 */
export const getIsPoNumberAndCostCenterRequiredV1 = <ThrowOnError extends boolean = true>(options: Options<GetIsPoNumberAndCostCenterRequiredV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetIsPoNumberAndCostCenterRequiredV1Response, GetIsPoNumberAndCostCenterRequiredV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zGetIsPoNumberAndCostCenterRequiredV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/is-po-number-and-cost-center-required',
        ...options
    });
};

export const viewPickUpRequestIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPickUpRequestIndexV1Response, ViewPickUpRequestIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPickUpRequestIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests',
        ...options
    });
};

export const createPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<CreatePickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreatePickUpRequestV1Response, CreatePickUpRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreatePickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPickUpRequestV1Response, ViewPickUpRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/{uuid}',
        ...options
    });
};

export const updatePickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdatePickUpRequestV1Response, UpdatePickUpRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdatePickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewWasteProducerIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteProducerIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewWasteProducerIndexV1Response, ViewWasteProducerIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewWasteProducerIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-producers',
        ...options
    });
};

export const viewSuggestedWasteProducersV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewSuggestedWasteProducersV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewSuggestedWasteProducersV1Response, ViewSuggestedWasteProducersV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewSuggestedWasteProducersV1Response.parseAsync(data);
        },
        url: '/api/v1/suggested-waste-producers',
        ...options
    });
};

export const submitPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SubmitPickUpRequestV1Response, SubmitPickUpRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSubmitPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/{uuid}/submit',
        ...options
    });
};

export const bulkDeletePickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeletePickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, BulkDeletePickUpRequestV1Error, ThrowOnError>({
        url: '/api/v1/pick-up-requests/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewPickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewPickUpRequestSapV1Response, ViewPickUpRequestSapV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewPickUpRequestSapV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}',
        ...options
    });
};

export const updatePickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<unknown, UpdatePickUpRequestSapV1Error, ThrowOnError>({
        url: '/api/v1/pick-up-requests/sap/{requestNumber}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const copyPickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<CopyPickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CopyPickUpRequestSapV1Response, CopyPickUpRequestSapV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCopyPickUpRequestSapV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}/copy',
        ...options
    });
};

export const submitPickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitPickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, SubmitPickUpRequestSapV1Error, ThrowOnError>({
        url: '/api/v1/pick-up-requests/sap/{requestNumber}/submit',
        ...options
    });
};

export const viewTankerTypeIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewTankerTypeIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewTankerTypeIndexV1Response, ViewTankerTypeIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewTankerTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/tanker-types',
        ...options
    });
};

export const viewTransportTypeIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewTransportTypeIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewTransportTypeIndexV1Response, ViewTransportTypeIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewTransportTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/transport-types',
        ...options
    });
};

export const viewUnNumberIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUnNumberIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ViewUnNumberIndexV1Response, ViewUnNumberIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUnNumberIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/un-numbers',
        ...options
    });
};

export const viewUnNumberIndexForPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewUnNumberIndexForPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewUnNumberIndexForPickUpRequestV1Response, ViewUnNumberIndexForPickUpRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewUnNumberIndexForPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/un-numbers-pick-up-request',
        ...options
    });
};

export const viewWasteInquiryIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteInquiryIndexV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewWasteInquiryIndexV1Response, ViewWasteInquiryIndexV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewWasteInquiryIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries',
        ...options
    });
};

export const createWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<CreateWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateWasteInquiryV1Response, CreateWasteInquiryV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewWasteInquiryV1Response, ViewWasteInquiryV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{uuid}',
        ...options
    });
};

export const updateWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateWasteInquiryV1Response, UpdateWasteInquiryV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const submitWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SubmitWasteInquiryV1Response, SubmitWasteInquiryV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSubmitWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{uuid}/submit',
        ...options
    });
};

export const viewWasteInquirySapV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteInquirySapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewWasteInquirySapV1Response, ViewWasteInquirySapV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewWasteInquirySapV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}',
        ...options
    });
};

export const addDocumentToWasteInquirySapV1 = <ThrowOnError extends boolean = true>(options: Options<AddDocumentToWasteInquirySapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AddDocumentToWasteInquirySapV1Response, AddDocumentToWasteInquirySapV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zAddDocumentToWasteInquirySapV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/add-documents',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const bulkDeleteWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeleteWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, BulkDeleteWasteInquiryV1Error, ThrowOnError>({
        url: '/api/v1/waste-inquiries/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const copyWasteInquirySapV1 = <ThrowOnError extends boolean = true>(options: Options<CopyWasteInquirySapV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CopyWasteInquirySapV1Response, CopyWasteInquirySapV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCopyWasteInquirySapV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/copy',
        ...options
    });
};

export const createWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<CreateWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateWeeklyPlanningRequestV1Response, CreateWeeklyPlanningRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zCreateWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const viewWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ViewWeeklyPlanningRequestV1Response, ViewWeeklyPlanningRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zViewWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}',
        ...options
    });
};

export const updateWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateWeeklyPlanningRequestV1Response, UpdateWeeklyPlanningRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zUpdateWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const addWprPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<AddWprPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AddWprPickUpRequestV1Response, AddWprPickUpRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zAddWprPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}/add-pick-up-request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const submitWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SubmitWeeklyPlanningRequestV1Response, SubmitWeeklyPlanningRequestV1Error, ThrowOnError>({
        responseValidator: async (data) => {
            return await zSubmitWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}/submit',
        ...options
    });
};