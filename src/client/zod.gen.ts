// This file is auto-generated by @hey-api/openapi-ts

import { z } from 'zod';

export const zPermission = z.enum([
    'all_permissions',
    'announcement.manage',
    'balanced-scorecard.read',
    'certificate.read',
    'contact.manage',
    'contact.read',
    'contract-line.read',
    'contract-line.manage',
    'document.master-table',
    'document.tfs',
    'document.quotation',
    'document.minutes-and-presentations',
    'document.manual',
    'document.bsc',
    'document.contract',
    'document.transport',
    'dynamic-table-view.manage',
    'ecmr.read',
    'event-log.read',
    'invoice.read',
    'invoice.manage',
    'jobs.read.index',
    'jobs.read.detail',
    'news-item.manage',
    'newsletter.subscribe',
    'pick-up-request.read',
    'pick-up-request.manage',
    'packaging-request.read',
    'packaging-request.manage',
    'power-bi.read',
    'role.read',
    'role.manage',
    'send_push_notification',
    'typesense',
    'user.read',
    'user.manage',
    'user.impersonate',
    'waste-inquiry.read',
    'waste-inquiry.manage',
    'weekly-planning-request.read',
    'weekly-planning-request.manage'
]);

export const zViewRoleDetailResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    permissions: z.array(zPermission),
    isDefault: z.boolean(),
    isSystemAdmin: z.boolean()
});

export const zViewMeResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ]),
    roles: z.array(zViewRoleDetailResponse),
    isInternalUser: z.boolean()
});

export const zViewUserDetailResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ]),
    roles: z.array(zViewRoleDetailResponse)
});

export const zPaginatedOffsetQuery = z.object({
    limit: z.number().gte(1).lte(100),
    offset: z.number().gte(0)
});

export const zUserIndexRoleView = z.object({
    uuid: z.string().uuid(),
    name: z.string()
});

export const zUserIndexView = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ]),
    roles: z.array(zUserIndexRoleView)
});

export const zPaginatedOffsetResponseMeta = z.object({
    total: z.number(),
    offset: z.number(),
    limit: z.number()
});

export const zViewUserIndexResponse = z.object({
    items: z.array(zUserIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewPermissionIndexPermissionResponse = z.object({
    name: z.string(),
    key: zPermission,
    description: z.string()
});

export const zViewPermissionIndexGroupResponse = z.object({
    name: z.string(),
    permissions: z.array(zViewPermissionIndexPermissionResponse)
});

export const zViewPermissionIndexResponse = z.object({
    groups: z.array(zViewPermissionIndexGroupResponse)
});

export const zViewCustomerIndexQueryKey = z.object({
    skipToken: z.string()
});

export const zViewCustomerIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewCustomerIndexQueryKey,
        z.null()
    ]).optional()
});

export const zCoordinatesResponse = z.object({
    latitude: z.number(),
    longitude: z.number()
});

export const zAddressResponse = z.object({
    countryCode: z.union([
        z.string(),
        z.null()
    ]),
    postalCode: z.string(),
    locality: z.string(),
    addressLine1: z.string(),
    addressLine2: z.union([
        z.string(),
        z.null()
    ]),
    coordinates: z.union([
        zCoordinatesResponse,
        z.null()
    ])
});

export const zCustomerResponse = z.object({
    id: z.string(),
    name: z.string(),
    address: z.union([
        z.string(),
        z.unknown(),
        z.null()
    ])
});

export const zViewCustomerIndexResponseMeta = z.object({
    next: z.union([
        zViewCustomerIndexQueryKey,
        z.null()
    ])
});

export const zViewCustomerIndexResponse = z.object({
    items: z.array(zCustomerResponse),
    meta: zViewCustomerIndexResponseMeta
});

export const zRequestType = z.enum([
    'waste',
    'pick-up'
]);

export const zViewSuggestedCustomersFilterQuery = z.object({
    requestType: zRequestType
});

export const zViewSuggestedCustomersResponse = z.object({
    items: z.array(zCustomerResponse)
});

export const zCustomerNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'customer_not_found'
    ])
});

export const zViewCustomerCountryResponse = z.object({
    countryCode: z.string()
});

export const zSubjectType = z.enum([
    'announcement',
    'contact',
    'dynamic-table-view',
    'file',
    'news-item',
    'pick-up-request',
    'packaging-request',
    'role',
    'user',
    'waste-inquiry',
    'weekly-planning-request'
]);

export const zViewDomainEventLogIndexFilterQuery = z.object({
    subjectType: zSubjectType.optional(),
    subjectId: z.string().uuid().optional(),
    userUuid: z.string().uuid().optional()
});

export const zViewDomainEventLogIndexQueryKey = z.object({
    createdAt: z.string(),
    uuid: z.string().uuid()
});

export const zViewDomainEventLogIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewDomainEventLogIndexQueryKey,
        z.null()
    ]).optional()
});

export const zUserCreatedEventContent = z.object({
    userUuid: z.string().uuid()
});

export const zUserCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'user.created'
    ]),
    content: zUserCreatedEventContent
});

export const zRoleCreatedEventContent = z.object({
    roleUuid: z.string().uuid(),
    roleName: z.string()
});

export const zRoleCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.created'
    ]),
    content: zRoleCreatedEventContent
});

export const zRoleDeletedEventContent = z.object({
    roleUuid: z.string().uuid(),
    roleName: z.string()
});

export const zRoleDeletedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.deleted'
    ]),
    content: zRoleDeletedEventContent
});

export const zRoleRenamedEventContent = z.object({
    roleUuid: z.string().uuid(),
    previousName: z.string(),
    newName: z.string()
});

export const zRoleRenamedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.renamed'
    ]),
    content: zRoleRenamedEventContent
});

export const zRolePermissionsUpdatedEventContent = z.object({
    roleUuid: z.string().uuid(),
    newPermissions: z.array(zPermission),
    roleName: z.string()
});

export const zRolePermissionsUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.permissions.updated'
    ]),
    content: zRolePermissionsUpdatedEventContent
});

export const zRolePermissionsCacheClearedEventContent = z.object({
    roleUuids: z.string().uuid()
});

export const zRolePermissionsCacheClearedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.permissions.cache.cleared'
    ]),
    content: zRolePermissionsCacheClearedEventContent
});

export const zNotificationType = z.enum([
    'user.created',
    'test-notification'
]);

export const zNotificationCreatedEventContent = z.object({
    uuid: z.string().uuid(),
    type: zNotificationType
});

export const zNotificationCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.created'
    ]),
    content: zNotificationCreatedEventContent
});

export const zNotificationChannel = z.enum([
    'email',
    'sms',
    'app',
    'push'
]);

export const zUserNotificationCreatedEventContent = z.object({
    notificationUuid: z.string().uuid(),
    channel: zNotificationChannel,
    userUuid: z.string().uuid()
});

export const zUserNotificationCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'user.notification.created'
    ]),
    content: zUserNotificationCreatedEventContent
});

export const zContactCreatedEventContent = z.object({
    contactUuid: z.string().uuid()
});

export const zContactCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'contact.created'
    ]),
    content: zContactCreatedEventContent
});

export const zContactUpdatedEventContent = z.object({
    contactUuid: z.string().uuid()
});

export const zContactUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'contact.updated'
    ]),
    content: zContactUpdatedEventContent
});

export const zContactDeletedEventContent = z.object({
    contactUuid: z.string().uuid()
});

export const zContactDeletedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'contact.deleted'
    ]),
    content: zContactDeletedEventContent
});

export const zFileCreatedEventContent = z.object({
    fileUuid: z.string().uuid(),
    fileName: z.string()
});

export const zFileCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'file.created'
    ]),
    content: zFileCreatedEventContent
});

export const zFileUploadedEventContent = z.object({
    fileUuid: z.string().uuid(),
    fileName: z.string()
});

export const zFileUploadedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'file.uploaded'
    ]),
    content: zFileUploadedEventContent
});

export const zNotificationReadEventContent = z.object({
    notificationUuid: z.string().uuid(),
    userUuid: z.string().uuid()
});

export const zNotificationReadDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.read'
    ]),
    content: zNotificationReadEventContent
});

export const zNotificationUnreadEventContent = z.object({
    notificationUuid: z.string().uuid(),
    userUuid: z.string().uuid()
});

export const zNotificationUnreadDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.unread'
    ]),
    content: zNotificationUnreadEventContent
});

export const zNotificationPreset = z.enum([
    'all',
    'default',
    'custom',
    'none'
]);

export const zNotificationPreferencePresetEventContent = z.object({
    userUuid: z.string().uuid(),
    preset: zNotificationPreset
});

export const zNotificationPreferencePresetUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.preference.preset.updated'
    ]),
    content: zNotificationPreferencePresetEventContent
});

export const zNotificationTypesMigratedEventContent = z.object({
    types: z.array(zNotificationType)
});

export const zNotificationTypesMigratedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.types.migrated'
    ]),
    content: zNotificationTypesMigratedEventContent
});

export const zTestNotificationSentEventContent = z.object({
    message: z.string()
});

export const zTestNotificationSentDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'test-notification.sent'
    ]),
    content: zTestNotificationSentEventContent
});

export const zAllNotificationsMarkedAsReadEventContent = z.object({
    userUuid: z.string().uuid()
});

export const zNotificationReadAllDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.read.all'
    ]),
    content: zAllNotificationsMarkedAsReadEventContent
});

export const zDomainEventLogResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ])
});

export const zViewDomainEventLogIndexResponseMeta = z.object({
    next: z.union([
        zViewDomainEventLogIndexQueryKey,
        z.null()
    ])
});

export const zViewDomainEventLogIndexResponse = z.object({
    items: z.array(z.unknown()),
    meta: zViewDomainEventLogIndexResponseMeta
});

export const zMimeType = z.enum([
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/html',
    'image/jpeg',
    'image/png',
    'image/tiff',
    'image/bmp',
    'image/heic',
    'image/webp',
    'image/gif',
    'text/csv',
    'application/vnd.ms-outlook',
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    'application/rtf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
]);

export const zCreateFileCommand = z.object({
    name: z.string(),
    mimeType: zMimeType
});

export const zCreateFileResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    mimeType: z.union([
        zMimeType,
        z.null()
    ]),
    uploadUrl: z.string()
});

export const zGlobalSearchCollectionName = z.enum([
    'user',
    'contact'
]);

export const zSearchCollectionsFilterQuery = z.object({
    collections: z.array(zGlobalSearchCollectionName).optional()
});

export const zSearchCollectionUserResponse = z.object({
    uuid: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zSearchCollectionContactResponse = z.object({
    uuid: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zSearchCollectionsResponseItem = z.object({
    collection: zGlobalSearchCollectionName,
    entity: z.union([
        zSearchCollectionUserResponse,
        zSearchCollectionContactResponse
    ]),
    text_match: z.number()
});

export const zSearchCollectionsResponse = z.object({
    items: z.array(zSearchCollectionsResponseItem)
});

export const zViewJobsIndexSortQueryKey = z.enum([
    'createdAt'
]);

export const zSortDirection = z.enum([
    'asc',
    'desc'
]);

export const zViewJobsIndexSortQuery = z.object({
    key: zViewJobsIndexSortQueryKey,
    order: zSortDirection
});

export const zQueueName = z.enum([
    'system'
]);

export const zViewJobsIndexFilterQuery = z.object({
    queueNames: z.array(zQueueName).optional(),
    archived: z.boolean().optional().default(false)
});

export const zViewJobsIndexQueryKey = z.object({
    createdAt: z.string().datetime().optional(),
    id: z.string()
});

export const zViewJobsIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewJobsIndexQueryKey,
        z.null()
    ]).optional()
});

export const zJobStatus = z.enum([
    'created',
    'active',
    'completed',
    'retry',
    'failed',
    'cancelled'
]);

export const zViewJobsIndexItemResponse = z.object({
    queueName: zQueueName,
    id: z.string().uuid(),
    name: z.string(),
    status: zJobStatus,
    createdAt: z.string().datetime(),
    completedAt: z.union([
        z.string().datetime(),
        z.null()
    ])
});

export const zViewJobsIndexResponseMeta = z.object({
    next: z.union([
        zViewJobsIndexQueryKey,
        z.null()
    ])
});

export const zViewJobsIndexResponse = z.object({
    items: z.array(zViewJobsIndexItemResponse),
    meta: zViewJobsIndexResponseMeta
});

export const zViewJobDetailResponse = z.object({
    id: z.string().uuid(),
    queueName: zQueueName,
    priority: z.number(),
    name: z.string(),
    data: z.object({}),
    status: zJobStatus,
    retryLimit: z.number(),
    retryCount: z.number(),
    retryDelay: z.number(),
    retryBackoff: z.boolean(),
    startAfter: z.string().datetime(),
    startedAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    singletonKey: z.union([
        z.string(),
        z.null()
    ]),
    singletonOn: z.union([
        z.string().datetime(),
        z.null()
    ]),
    expireIn: z.object({}),
    createdAt: z.string().datetime(),
    completedAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    keepUntil: z.string().datetime(),
    output: z.union([
        z.object({}),
        z.null()
    ]),
    deadLetter: z.union([
        z.string(),
        z.null()
    ]),
    policy: z.union([
        z.string(),
        z.null()
    ])
});

export const zPreferenceTypes = z.object({
    email: z.array(zNotificationType),
    sms: z.array(zNotificationType),
    app: z.array(zNotificationType),
    push: z.array(zNotificationType)
});

export const zGetMyNotificationPreferencesResponse = z.object({
    preset: zNotificationPreset,
    emailEnabled: z.boolean(),
    smsEnabled: z.boolean(),
    appEnabled: z.boolean(),
    pushEnabled: z.boolean(),
    preferences: zPreferenceTypes
});

export const zNotificationTypeChannelConfig = z.object({
    channel: zNotificationChannel,
    defaultValue: z.boolean(),
    isSupported: z.boolean()
});

export const zNotificationTypeConfig = z.object({
    type: zNotificationType,
    channelConfigs: z.array(zNotificationTypeChannelConfig)
});

export const zGetNotificationTypesConfigResponse = z.object({
    items: z.array(zNotificationTypeConfig)
});

export const zUpdateMyChannelNotificationPreferenceCommand = z.object({
    channel: zNotificationChannel,
    isEnabled: z.boolean()
});

export const zSendTestNotificationCommand = z.object({
    message: z.string()
});

export const zGetMyNotificationsFilterQuery = z.object({
    onlyUnread: z.string().optional()
});

export const zGetMyNotificationsQueryKey = z.object({
    createdAt: z.string(),
    notificationUuid: z.string().uuid()
});

export const zGetMyNotificationsPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: zGetMyNotificationsQueryKey.optional()
});

export const zCreatedByUserResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string()
});

export const zTestNotificationContent = z.object({
    message: z.string()
});

export const zTestNotificationNotification = z.object({
    createdAt: z.string().datetime(),
    readAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    notificationUuid: z.string().uuid(),
    createdByUser: z.union([
        zCreatedByUserResponse,
        z.null()
    ]),
    message: z.string(),
    type: z.enum([
        'test-notification'
    ]),
    meta: zTestNotificationContent
});

export const zGetMyNotificationsResponseMeta = z.object({
    next: z.union([
        zGetMyNotificationsQueryKey,
        z.null()
    ])
});

export const zGetMyNotificationsResponse = z.object({
    items: z.array(zTestNotificationNotification),
    meta: zGetMyNotificationsResponseMeta
});

export const zViewUnreadNotificationsCountResponse = z.object({
    amount: z.number().gte(0),
    exceedsLimit: z.boolean()
});

export const zUpdateMyNotificationTypePreferenceCommand = z.object({
    channel: zNotificationChannel,
    isEnabled: z.boolean(),
    types: z.array(zNotificationType)
});

export const zUserNotificationNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'user_notification_not_found'
    ])
});

export const zUpdateMyNotificationPreferencePresetCommand = z.object({
    preset: zNotificationPreset
});

export const zErrorSource = z.object({
    pointer: z.string()
});

export const zMigrationAlreadyPerformedErrorMeta = z.object({
    type: z.array(zNotificationType)
});

export const zMigrationAlreadyPerformedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'migration_already_performed'
    ]),
    meta: zMigrationAlreadyPerformedErrorMeta
});

export const zMigrateNotificationTypesCommand = z.object({
    types: z.array(zNotificationType)
});

export const zCreateRoleCommand = z.object({
    name: z.string()
});

export const zCreateRoleResponse = z.object({
    uuid: z.string().uuid()
});

export const zClearRolePermissionsCacheCommand = z.object({
    roleUuids: z.union([
        z.array(z.string().uuid()),
        z.null()
    ]).optional()
});

export const zUpdateRoleCommand = z.object({
    name: z.string()
});

export const zRoleResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    permissions: z.array(zPermission),
    isDefault: z.boolean(),
    isSystemAdmin: z.boolean()
});

export const zViewRoleIndexResponse = z.object({
    items: z.array(zRoleResponse)
});

export const zRoleNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'role_not_found'
    ])
});

export const zUpdateRolesPermissionsCommandItem = z.object({
    roleUuid: z.string().uuid(),
    permissions: z.array(zPermission)
});

export const zUpdateRolesPermissionsCommand = z.object({
    roles: z.array(zUpdateRolesPermissionsCommandItem)
});

export const zGetApiInfoResponse = z.object({
    environment: z.string(),
    commit: z.string(),
    version: z.string(),
    timestamp: z.string().datetime()
});

export const zUiTheme = z.enum([
    'light',
    'dark',
    'system'
]);

export const zLocale = z.enum([
    'en-GB',
    'nl-BE',
    'fr-FR',
    'es-ES',
    'de-DE'
]);

export const zFontSize = z.enum([
    'smaller',
    'small',
    'default',
    'large',
    'larger'
]);

export const zUpdateUiPreferencesCommand = z.object({
    theme: zUiTheme.optional(),
    language: zLocale.optional(),
    fontSize: zFontSize.optional(),
    showShortcuts: z.boolean().optional(),
    reduceMotion: z.boolean().optional(),
    highContrast: z.boolean().optional()
});

export const zViewUiPreferencesResponse = z.object({
    theme: zUiTheme,
    language: zLocale,
    fontSize: zFontSize,
    showShortcuts: z.boolean(),
    reduceMotion: z.boolean(),
    highContrast: z.boolean()
});

export const zAnnouncementType = z.enum([
    'informational',
    'urgent'
]);

export const zCreateAnnouncementTranslationCommand = z.object({
    title: z.string(),
    content: z.object({}),
    language: zLocale
});

export const zCreateAnnouncementCommand = z.object({
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    translations: z.array(zCreateAnnouncementTranslationCommand)
});

export const zCreateAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zMissingRequiredFieldError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'missing_required_field'
    ])
});

export const zDateMustBeAfterError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'date_must_be_after'
    ])
});

export const zFieldMustBeNullError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'field_must_be_null'
    ])
});

export const zNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'not_found'
    ])
});

export const zUpdateAnnouncementTranslationCommand = z.object({
    title: z.string().optional(),
    content: z.object({}).optional(),
    language: zLocale
});

export const zUpdateAnnouncementCommand = z.object({
    type: zAnnouncementType.optional(),
    startDate: z.string().datetime().optional(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    translations: z.array(zUpdateAnnouncementTranslationCommand).optional()
});

export const zUpdateAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zViewAnnouncementTranslationResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    title: z.string(),
    content: z.object({}),
    language: zLocale
});

export const zDashboardAnnouncementIndexView = z.object({
    uuid: z.string().uuid(),
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewAnnouncementTranslationResponse
});

export const zViewDashboardAnnouncementIndexResponse = z.object({
    items: z.array(zDashboardAnnouncementIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewDashboardAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewAnnouncementTranslationResponse
});

export const zPublishStatus = z.enum([
    'scheduled',
    'published',
    'archived'
]);

export const zViewNewsItemTranslationResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    title: z.union([
        z.string(),
        z.null()
    ]),
    content: z.union([
        z.object({}),
        z.null()
    ]),
    language: zLocale
});

export const zViewNewsItemAuthorResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewNewsItemTranslationResponse),
    author: zViewNewsItemAuthorResponse
});

export const zViewAnnouncementTranslationIndexResponse = z.object({
    uuid: z.string().uuid(),
    title: z.string(),
    language: zLocale
});

export const zViewAnnouncementAuthorResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ])
});

export const zAnnouncementIndexView = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewAnnouncementTranslationIndexResponse),
    author: zViewAnnouncementAuthorResponse
});

export const zViewAnnouncementIndexResponse = z.object({
    items: z.array(zAnnouncementIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zContactResponse = z.object({
    uuid: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zViewContactIndexResponse = z.object({
    items: z.array(zContactResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zCreateContactCommand = z.object({
    firstName: z.string().max(40),
    lastName: z.string().max(40),
    email: z.string().email().max(241)
});

export const zCreateContactResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zUpdateContactCommand = z.object({
    firstName: z.string().max(40).optional(),
    lastName: z.string().max(40).optional(),
    email: z.string().email().max(241).optional()
});

export const zViewContainerTypeIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zContainerTypeResponse = z.object({
    name: z.string()
});

export const zViewContainerTypeIndexResponse = z.object({
    items: z.array(zContainerTypeResponse)
});

export const zViewContractLineIndexFilterQuery = z.object({
    customerId: z.string().optional(),
    wasteProducerId: z.string().optional(),
    pickUpAddressIds: z.array(z.string()).optional()
});

export const zViewContractLineIndexQueryKey = z.object({
    skipToken: z.string()
});

export const zViewContractLineIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewContractLineIndexQueryKey,
        z.null()
    ]).optional()
});

export const zContractLineResponse = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        z.string(),
        z.null()
    ]),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    materialType: z.union([
        z.string(),
        z.null()
    ]),
    packagingIndicator: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewContractLineIndexResponseMeta = z.object({
    next: z.union([
        zViewContractLineIndexQueryKey,
        z.null()
    ])
});

export const zViewContractLineIndexResponse = z.object({
    items: z.array(zContractLineResponse),
    meta: zViewContractLineIndexResponseMeta
});

export const zViewWprContractLineIndexFilterQuery = z.object({
    customerId: z.string(),
    wasteProducerId: z.string().optional(),
    pickUpAddressIds: z.array(z.string()).optional()
});

export const zWprContractLineResponse = z.object({
    pickUpRequestUuid: z.string(),
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        z.string(),
        z.null()
    ]),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    materialType: z.union([
        z.string(),
        z.null()
    ]),
    packagingIndicator: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewWprContractLineIndexResponse = z.object({
    items: z.array(zWprContractLineResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewPackagingRequestContractLineIndexFilterQuery = z.object({
    customerId: z.string(),
    wasteProducerId: z.string(),
    deliveryAddressIds: z.array(z.string()).optional()
});

export const zViewPackagingRequestContractLineIndexQueryKey = z.object({
    skipToken: z.string()
});

export const zViewPackagingRequestContractLineIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewPackagingRequestContractLineIndexQueryKey,
        z.null()
    ]).optional()
});

export const zPackagingRequestContractLineResponse = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    imageUrl: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewPackagingRequestContractLineIndexResponseMeta = z.object({
    next: z.union([
        zViewContractLineIndexQueryKey,
        z.null()
    ])
});

export const zViewPackagingRequestContractLineIndexResponse = z.object({
    items: z.array(zPackagingRequestContractLineResponse),
    meta: zViewPackagingRequestContractLineIndexResponseMeta
});

export const zContractLineNotAccessibleErrorMeta = z.object({
    contractNumber: z.string(),
    contractLineNumber: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zContractLineNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'contract_line_not_accessible'
    ]),
    meta: zContractLineNotAccessibleErrorMeta
});

export const zContractLinesSelection = z.object({
    contractNumber: z.string(),
    contractItem: z.string()
});

export const zGenerateContractLinesPdfCommand = z.object({
    selection: z.array(zContractLinesSelection).min(1)
});

export const zGenerateContractLinesPdfResponse = z.object({
    pageCount: z.number(),
    size: z.number(),
    name: z.string(),
    mimeType: z.string(),
    content: z.string()
});

export const zDocumentNotFoundErrorMeta = z.object({
    customerUuid: z.string().uuid(),
    documentId: z.string()
});

export const zDocumentNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'document_not_found'
    ]),
    meta: zDocumentNotFoundErrorMeta
});

export const zDownloadDocumentCommand = z.object({
    documentId: z.string(),
    customerUuid: z.string().uuid()
});

export const zForbiddenError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '403'
    ]),
    code: z.enum([
        'forbidden'
    ])
});

export const zViewDocumentIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        z.string(),
        z.null()
    ]).optional()
});

export const zSharepointDocumentViewName = z.enum([
    'mastertable',
    'tfs',
    'quotation',
    'meetings',
    'manual',
    'bsc',
    'contract',
    'transport'
]);

export const zSharepointDocumentStatus = z.enum([
    'Active',
    'Archived'
]);

export const zViewDocumentIndexFilterQuery = z.object({
    viewName: zSharepointDocumentViewName,
    customerUuid: z.string().uuid(),
    wasteProducerIds: z.array(z.string()),
    status: zSharepointDocumentStatus.optional(),
    year: z.string().optional()
});

export const zSharepointDocumentType = z.unknown();

export const zViewDocumentIndexItemResponse = z.object({
    id: z.string(),
    name: z.string(),
    actionAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    applicableFrom: z.union([
        z.string().datetime(),
        z.null()
    ]),
    applicableTill: z.union([
        z.string().datetime(),
        z.null()
    ]),
    tfsType: z.union([
        zSharepointDocumentType,
        z.null()
    ]),
    wasteProducer: z.string(),
    status: z.union([
        zSharepointDocumentStatus,
        z.null()
    ])
});

export const zViewDocumentIndexResponseMeta = z.object({
    next: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewDocumentIndexResponse = z.object({
    items: z.array(zViewDocumentIndexItemResponse),
    meta: zViewDocumentIndexResponseMeta
});

export const zViewUserSiteIndexWasteProducerResponse = z.object({
    id: z.number(),
    name: z.string()
});

export const zViewUserSiteIndexResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    wasteProducers: z.array(zViewUserSiteIndexWasteProducerResponse)
});

export const zDynamicColumnNames = z.enum([
    'contractNumber',
    'contractItem',
    'customerReference',
    'wasteMaterial',
    'materialNumber',
    'treatmentCenterName',
    'installationName',
    'customerId',
    'customerName',
    'wasteProducerId',
    'wasteProducerName',
    'pickUpAddressId',
    'pickUpAddressName',
    'asn',
    'tfs',
    'isHazardous',
    'packaged',
    'tcNumber',
    'materialAnalysis',
    'ewcCode',
    'endTreatmentCenterId',
    'endTreatmentCenterName',
    'remarks',
    'processCode',
    'esnNumber',
    'deliveryInfo',
    'materialType',
    'packagingIndicator',
    'invoiceNumber',
    'status',
    'payerId',
    'payerName',
    'issuedOn',
    'firstReminderMailStatus',
    'firstReminderOn',
    'secondReminderMailStatus',
    'secondReminderOn',
    'thirdReminderStatus',
    'autoApprovedOn',
    'netAmount',
    'vatAmount',
    'currency',
    'estimatedWeightOrVolumeValue',
    'estimatedWeightOrVolumeUnit',
    'packagingType',
    'quantityPackages',
    'quantityLabels',
    'quantityPallets',
    'unNumber',
    'packingGroup',
    'dangerLabel1',
    'dangerLabel2',
    'dangerLabel3',
    'costCenter',
    'poNumber',
    'containerType',
    'containerVolumeSize',
    'containerNumber',
    'containerTransportType',
    'isContainerCovered',
    'tankerType',
    'totalQuantityPallets',
    'isReturnPackaging',
    'packagingRemark',
    'reconciliationNumber',
    'hazardInducers',
    'quantityContainers',
    'tfsNumber',
    'serialNumber',
    'dueOn',
    'type',
    'accountDocumentNumber',
    'accountManagerName',
    'companyName',
    'requestNumber',
    'transportMode',
    'dateOfRequest',
    'accountManager',
    'isTransportByIndaver',
    'requestedStartDate',
    'requestedEndDate',
    'confirmedTransportDate',
    'salesOrder',
    'nameOfApplicant',
    'orderNumber',
    'nameInstallation',
    'disposalCertificateNumber',
    'ewc',
    'inquiryNumber',
    'wasteStreamName',
    'date',
    'contractId',
    'salesOrganisationId',
    'salesOrganisationName',
    'requestorName'
]);

export const zDynamicTableColumnIndexView = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: zDynamicColumnNames,
    isHidable: z.boolean(),
    applicableFields: z.array(z.string()),
    filterableField: z.union([
        z.string(),
        z.null()
    ]),
    sortableFields: z.array(z.string()),
    searchableFields: z.array(z.string())
});

export const zDynamicTableIndexColumnResponse = z.object({
    items: z.array(zDynamicTableColumnIndexView)
});

export const zColumnNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'column_not_found'
    ])
});

export const zColumnNotFilterableError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'column_not_filterable'
    ])
});

export const zColumnNotSortableError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'column_not_sortable'
    ])
});

export const zDuplicateColumnError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'duplicate_column'
    ])
});

export const zDynamicTableViewFilterCommand = z.object({
    columnUuid: z.string().uuid(),
    value: z.union([
        z.string(),
        z.array(z.string())
    ])
});

export const zDynamicTableViewSortCommand = z.object({
    columnUuid: z.string().uuid(),
    direction: zSortDirection
});

export const zDynamicTableViewVisibleColumnsCommand = z.object({
    columnUuid: z.string().uuid()
});

export const zCreateDynamicTableViewCommand = z.object({
    viewName: z.string(),
    isGlobal: z.boolean().optional(),
    isGlobalDefault: z.boolean().optional(),
    isDefault: z.boolean().optional(),
    filters: z.array(zDynamicTableViewFilterCommand),
    sorts: z.array(zDynamicTableViewSortCommand),
    visibleColumns: z.array(zDynamicTableViewVisibleColumnsCommand)
});

export const zCreateDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zVisibilityConfigurationResponse = z.object({
    uuid: z.string().uuid(),
    order: z.number()
});

export const zSortConfigurationResponse = z.object({
    uuid: z.string().uuid(),
    order: z.number(),
    direction: zSortDirection
});

export const zFilterConfigurationResponse = z.object({
    uuid: z.string().uuid(),
    value: z.union([
        z.string(),
        z.array(z.string())
    ])
});

export const zViewDefaultDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    isGlobal: z.boolean(),
    isDefaultGlobal: z.boolean(),
    isUserDefault: z.boolean(),
    visibleColumns: z.array(zVisibilityConfigurationResponse),
    sorts: z.array(zSortConfigurationResponse),
    filters: z.array(zFilterConfigurationResponse)
});

export const zUpdateDynamicTableViewCommand = z.object({
    viewName: z.string().optional(),
    isGlobal: z.boolean().optional(),
    isGlobalDefault: z.boolean().optional(),
    isDefault: z.boolean().optional(),
    filters: z.array(zDynamicTableViewFilterCommand).optional(),
    sorts: z.array(zDynamicTableViewSortCommand).optional(),
    visibleColumns: z.array(zDynamicTableViewVisibleColumnsCommand).optional()
});

export const zUpdateDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    isGlobal: z.boolean(),
    isDefaultGlobal: z.boolean(),
    isUserDefault: z.boolean(),
    visibleColumns: z.array(zVisibilityConfigurationResponse),
    sorts: z.array(zSortConfigurationResponse),
    filters: z.array(zFilterConfigurationResponse)
});

export const zDynamicTableViewIndexResponse = z.object({
    items: z.array(zDynamicTableViewResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zGlobalDefaultViewNotDeletable = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'global_default_view_not_deletable'
    ])
});

export const zLastGlobalViewNotDeletable = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'last_global_view_not_deletable'
    ])
});

export const zEwcCodeResponse = z.object({
    code: z.string(),
    description: z.string()
});

export const zViewEwcCodeIndexResponse = z.object({
    items: z.array(zEwcCodeResponse)
});

export const zInvoiceStatus = z.enum([
    'overdue',
    'outstanding',
    'cleared'
]);

export const zViewInvoiceIndexFilterQuery = z.object({
    statuses: z.array(zInvoiceStatus)
});

export const zViewInvoiceIndexPaginationQueryKey = z.object({
    skipToken: z.string()
});

export const zViewInvoiceIndexQueryKey = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewInvoiceIndexPaginationQueryKey,
        z.null()
    ]).optional()
});

export const zInvoiceType = z.enum([
    'credit_note',
    'invoice',
    'request_for_invoice',
    'cancellation',
    'unknown'
]);

export const zInvoiceResponse = z.object({
    invoiceNumber: z.string(),
    status: zInvoiceStatus,
    issuedOn: z.string().date(),
    dueOn: z.union([
        z.string().date(),
        z.null()
    ]),
    customerName: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    type: zInvoiceType,
    payerId: z.string(),
    payerName: z.string(),
    netAmount: z.string(),
    vatAmount: z.string(),
    currency: z.string(),
    accountDocumentNumber: z.union([
        z.string(),
        z.null()
    ]),
    accountManagerName: z.union([
        z.string(),
        z.null()
    ]),
    companyName: z.string()
});

export const zViewInvoiceIndexResponseMeta = z.object({
    next: z.union([
        zViewInvoiceIndexPaginationQueryKey,
        z.null()
    ])
});

export const zViewInvoiceIndexResponse = z.object({
    items: z.array(zInvoiceResponse),
    meta: zViewInvoiceIndexResponseMeta
});

export const zDraftInvoiceStatus = z.enum([
    'approved_by_customer',
    'auto_approved',
    'internal_approved',
    'rejected_by_customer',
    'rejected_by_indaver',
    'to_be_approved_by_customer',
    'to_be_approved_by_indaver'
]);

export const zViewDraftInvoiceIndexFilterQuery = z.object({
    statuses: z.array(zDraftInvoiceStatus).min(1)
});

export const zViewDraftInvoiceIndexPaginationQueryKey = z.object({
    skipToken: z.string()
});

export const zViewDraftInvoiceIndexQueryKey = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewDraftInvoiceIndexPaginationQueryKey,
        z.null()
    ]).optional()
});

export const zMailStatus = z.enum([
    'sent',
    'not_sent'
]);

export const zDraftInvoiceResponse = z.object({
    invoiceNumber: z.string(),
    status: zDraftInvoiceStatus,
    payerId: z.string(),
    payerName: z.string(),
    issuedOn: z.string().date(),
    firstReminderMailStatus: zMailStatus,
    firstReminderOn: z.union([
        z.string().date(),
        z.null()
    ]),
    secondReminderMailStatus: zMailStatus,
    secondReminderOn: z.union([
        z.string().date(),
        z.null()
    ]),
    thirdReminderStatus: zMailStatus,
    autoApprovedOn: z.union([
        z.string().date(),
        z.null()
    ]),
    netAmount: z.string(),
    vatAmount: z.string(),
    currency: z.string()
});

export const zViewDraftInvoiceIndexResponse = z.object({
    items: z.array(zDraftInvoiceResponse),
    meta: zViewInvoiceIndexResponseMeta
});

export const zNonApproveOrRejectableDraftInvoiceError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'non_approve_or_rejectable_draft_invoice'
    ])
});

export const zInvoiceNotFoundErrorMeta = z.object({
    invoiceNumber: z.string()
});

export const zInvoiceNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'invoice_not_found'
    ]),
    meta: zInvoiceNotFoundErrorMeta
});

export const zApproveDraftInvoiceCommand = z.object({
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    remark: z.union([
        z.string(),
        z.null()
    ])
});

export const zRejectDraftInvoiceCommand = z.object({
    remark: z.string()
});

export const zAlreadySubscribedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'already_subscribed'
    ])
});

export const zOptInAlreadyRequestedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'opt_in_already_requested'
    ])
});

export const zSubscribeToNewsletterCommand = z.object({
    email: z.string()
});

export const zCreateNewsItemTranslationCommand = z.object({
    title: z.string(),
    content: z.object({}),
    language: zLocale
});

export const zCreateNewsItemCommand = z.object({
    startDate: z.string().date(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    imageUuid: z.string().uuid(),
    newsItemTranslations: z.array(zCreateNewsItemTranslationCommand),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ]).optional()
});

export const zCreateNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zNewsItemTranslationExistsError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'news-item-translation-exists'
    ])
});

export const zNoStartDateOrEndDateExpectedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no-start-date-or-end-date-expected'
    ])
});

export const zUpdateNewsItemTranslationCommand = z.object({
    title: z.string().optional(),
    content: z.object({}).optional(),
    language: zLocale
});

export const zUpdateNewsItemCommand = z.object({
    startDate: z.string().date().optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    imageUuid: z.string().uuid().optional(),
    newsItemTranslations: z.array(zUpdateNewsItemTranslationCommand).optional(),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ]).optional()
});

export const zUpdateNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zFileResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    mimeType: z.union([
        zMimeType,
        z.null()
    ]),
    url: z.string()
});

export const zViewNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewNewsItemTranslationResponse),
    image: zFileResponse,
    videoIFrame: z.union([
        z.string(),
        z.null()
    ]),
    author: zViewNewsItemAuthorResponse
});

export const zViewNewsItemTranslationIndexResponse = z.object({
    uuid: z.string().uuid(),
    title: z.union([
        z.string(),
        z.null()
    ]),
    language: zLocale
});

export const zNewsItemIndexView = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewNewsItemTranslationIndexResponse),
    author: zViewNewsItemAuthorResponse
});

export const zViewNewsIndexResponse = z.object({
    items: z.array(zNewsItemIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewDashboardNewItemFilterQuery = z.object({
    excludeNewsItemUuids: z.array(z.string()).optional()
});

export const zDashboardNewsItemIndexView = z.object({
    uuid: z.string().uuid(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    startDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewNewsItemTranslationResponse,
    image: z.union([
        zFileResponse,
        z.null()
    ]),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewDashboardNewsIndexResponse = z.object({
    items: z.array(zDashboardNewsItemIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewDashboardNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    startDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewNewsItemTranslationResponse,
    image: z.union([
        zFileResponse,
        z.null()
    ]),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ])
});

export const zCreatePackagingRequestCommand = z.object({});

export const zCreatePackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteProducerResponse = z.object({
    id: z.string(),
    name: z.string(),
    address: z.union([
        z.string(),
        z.unknown(),
        z.null()
    ])
});

export const zPickUpAddressResponse = z.object({
    id: z.string(),
    name: z.string(),
    address: z.union([
        z.string(),
        z.unknown(),
        z.null()
    ])
});

export const zPackagingRequestMaterialResponse = z.object({
    contractLineId: z.string(),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    contractNumber: z.string(),
    contractItem: z.string(),
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    costCenter: z.union([
        z.string(),
        z.null()
    ]),
    quantity: z.number(),
    contractLineAccessible: z.boolean().optional()
});

export const zContactTypeResponse = z.object({
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zViewPackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    deliveryAddress: z.union([
        zPickUpAddressResponse,
        z.null()
    ]),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse)
});

export const zCustomerNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'customer_not_accessible'
    ])
});

export const zWasteProducerNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'waste_producer_not_accessible'
    ])
});

export const zPickUpAddressNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'pick_up_address_not_accessible'
    ])
});

export const zPackagingRequestMaterialCommand = z.object({
    contractLineId: z.string(),
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]),
    poNumber: z.union([
        z.string().max(35),
        z.null()
    ]),
    contractNumber: z.string(),
    contractItem: z.string(),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.string(),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    quantity: z.number().gte(0)
});

export const zContact = z.object({
    email: z.string().email().max(241),
    firstName: z.string().max(40),
    lastName: z.string().max(40)
});

export const zUpdatePackagingRequestCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    deliveryAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string().max(200),
        z.null()
    ]).optional(),
    sendCopyToContacts: z.array(zContact).optional()
});

export const zUpdatePackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zContractLineNotOfCustomerErrorMeta = z.object({
    customerId: z.string(),
    contractNumber: z.string(),
    contractLineNumber: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zContractLineNotOfCustomerError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'contract_line_not_of_customer'
    ]),
    meta: zContractLineNotOfCustomerErrorMeta
});

export const zContractLineNotOfPickUpAddressesErrorMeta = z.object({
    pickUpAddressIds: z.array(z.string()),
    contractNumber: z.string(),
    contractLineNumber: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zContractLineNotOfPickUpAddressesError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'contract_line_not_of_pick_up_addresses'
    ]),
    meta: zContractLineNotOfPickUpAddressesErrorMeta
});

export const zUpdateOnlyPackagingRequestError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'update_only_packaging_request'
    ])
});

export const zPackagingRequestAlreadySubmitted = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'packaging_request_already_submitted'
    ])
});

export const zSubmitPackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    requestNumber: z.string()
});

export const zBulkDeletePackagingRequestCommand = z.object({
    packagingRequestUuids: z.array(z.string())
});

export const zPickUpRequestNotFoundErrorMeta = z.object({
    uuid: z.string().optional(),
    requestNumber: z.string().optional()
});

export const zPickUpRequestNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'pick_up_request_not_found'
    ]),
    meta: zPickUpRequestNotFoundErrorMeta
});

export const zCopyNonSubmittedPickUpRequestError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'copy_non_submitted_pick_up_request'
    ])
});

export const zCopyPackagingRequestSapResponse = z.object({
    uuid: z.string().uuid()
});

export const zViewPackagingTypeIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zPackagingTypeResponse = z.object({
    name: z.string()
});

export const zViewPackagingTypeIndexResponse = z.object({
    items: z.array(zPackagingTypeResponse)
});

export const zViewPickUpAddressIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zViewPickUpAddressIndexQueryKey = z.object({
    skipToken: z.string()
});

export const zViewPickUpAddressIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewPickUpAddressIndexQueryKey,
        z.null()
    ]).optional()
});

export const zViewPickUpAddressIndexResponseMeta = z.object({
    next: z.union([
        zViewPickUpAddressIndexQueryKey,
        z.null()
    ])
});

export const zViewPickUpAddressIndexResponse = z.object({
    items: z.array(zPickUpAddressResponse),
    meta: zViewPickUpAddressIndexResponseMeta
});

export const zViewSuggestedPickUpAddressesFilterQuery = z.object({
    customerId: z.string().uuid(),
    requestType: zRequestType
});

export const zViewSuggestedPickUpAddressesResponse = z.object({
    items: z.array(zPickUpAddressResponse)
});

export const zGetIsPoNumberAndCostCenterRequiredFilterQuery = z.object({
    customerId: z.string()
});

export const zGetIsPoNumberAndCostCenterRequiredResponse = z.object({
    isPoNumberRequired: z.boolean(),
    isCostCenterRequired: z.boolean()
});

export const zViewPickUpRequestIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        z.unknown(),
        z.null()
    ]).optional()
});

export const zPickUpRequestStatus = z.enum([
    'draft',
    'pending',
    'confirmed',
    'cancelled',
    'indascan_draft'
]);

export const zViewPickUpRequestIndexFilterQuery = z.object({
    statuses: z.array(zPickUpRequestStatus)
});

export const zTransportMode = z.enum([
    'packaging-request-order',
    'packaged-curtain-sider-truck',
    'bulk-skips-container',
    'bulk-vacuum-tankers-road-tankers',
    'bulk-iso-tank'
]);

export const zPickUpRequestResponse = z.object({
    uuid: z.union([
        z.string(),
        z.null()
    ]),
    requestNumber: z.union([
        z.string(),
        z.null()
    ]),
    status: zPickUpRequestStatus,
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    contractNumber: z.union([
        z.string(),
        z.null()
    ]),
    contractItem: z.union([
        z.string(),
        z.null()
    ]),
    transportMode: z.union([
        zTransportMode,
        z.null()
    ]),
    dateOfRequest: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    accountManager: z.union([
        z.string(),
        z.null()
    ]),
    costCenter: z.union([
        z.string(),
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    requestedStartDate: z.union([
        z.string().date(),
        z.null()
    ]),
    requestedEndDate: z.union([
        z.string().date(),
        z.null()
    ]),
    confirmedTransportDate: z.union([
        z.string().date(),
        z.null()
    ]),
    salesOrder: z.union([
        z.string(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    nameOfApplicant: z.union([
        z.string(),
        z.null()
    ]),
    orderNumber: z.union([
        z.string(),
        z.null()
    ]),
    containerNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    nameInstallation: z.union([
        z.string(),
        z.null()
    ]),
    disposalCertificateNumber: z.union([
        z.string(),
        z.null()
    ]),
    ewc: z.union([
        z.string(),
        z.null()
    ]),
    tfsNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewPickUpRequestIndexResponseMeta = z.object({
    next: z.union([
        z.unknown(),
        z.null()
    ])
});

export const zViewPickUpRequestIndexResponse = z.object({
    items: z.array(zPickUpRequestResponse),
    meta: zViewPickUpRequestIndexResponseMeta
});

export const zCreatePickUpRequestCommand = z.object({});

export const zCreatePickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zPickUpTransportMode = z.enum([
    'packaged-curtain-sider-truck',
    'bulk-skips-container',
    'bulk-vacuum-tankers-road-tankers',
    'bulk-iso-tank'
]);

export const zWasteMeasurementUnit = z.enum([
    'kg',
    'm3',
    'pc',
    'to',
    'yd3'
]);

export const zMaterialResponse = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    materialType: z.union([
        z.string(),
        z.null()
    ]),
    packagingIndicator: z.union([
        z.string(),
        z.null()
    ]),
    estimatedWeightOrVolumeValue: z.union([
        z.number(),
        z.null()
    ]),
    estimatedWeightOrVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    costCenter: z.union([
        z.string(),
        z.null()
    ]),
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    unNumber: z.union([
        z.string(),
        z.null()
    ]),
    unNumberDescription: z.union([
        z.string(),
        z.null()
    ]),
    adrClass: z.union([
        z.string(),
        z.null()
    ]),
    packingGroup: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel1: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel2: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel3: z.union([
        z.string(),
        z.null()
    ]),
    packagingType: z.union([
        z.string(),
        z.null()
    ]),
    quantityPackages: z.union([
        z.number(),
        z.null()
    ]),
    quantityLabels: z.union([
        z.number(),
        z.null()
    ]),
    quantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    containerType: z.union([
        z.string(),
        z.null()
    ]),
    containerVolumeSize: z.union([
        z.string(),
        z.null()
    ]),
    containerNumber: z.union([
        z.string(),
        z.null()
    ]),
    containerTransportType: z.union([
        z.string(),
        z.null()
    ]),
    isContainerCovered: z.union([
        z.boolean(),
        z.null()
    ]),
    tankerType: z.union([
        z.string(),
        z.null()
    ]),
    position: z.union([
        z.string(),
        z.null()
    ]),
    contractLineAccessible: z.union([
        z.boolean(),
        z.null()
    ]),
    unNumberHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    reconciliationNumber: z.union([
        z.string(),
        z.null()
    ]),
    hazardInducers: z.union([
        z.string(),
        z.null()
    ]),
    quantityContainers: z.union([
        z.number(),
        z.null()
    ]),
    tfsNumber: z.union([
        z.string(),
        z.null()
    ]),
    serialNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zFileLinkResponse = z.object({
    uuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    name: z.string(),
    mimeType: z.union([
        zMimeType,
        z.null()
    ]),
    url: z.union([
        z.string(),
        z.null()
    ]),
    order: z.union([
        z.number(),
        z.null()
    ])
});

export const zViewPickUpRequestResponse = z.object({
    status: zPickUpRequestStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    totalQuantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    packagingRemark: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    materials: z.array(zMaterialResponse),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    additionalFiles: z.array(zFileLinkResponse),
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zViewWasteProducerIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zViewWasteProducerIndexQueryKey = z.object({
    skipToken: z.string()
});

export const zViewWasteProducerIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewWasteProducerIndexQueryKey,
        z.null()
    ]).optional()
});

export const zViewWasteProducerIndexResponseMeta = z.object({
    next: z.union([
        zViewWasteProducerIndexQueryKey,
        z.null()
    ])
});

export const zViewWasteProducerIndexResponse = z.object({
    items: z.array(zWasteProducerResponse),
    meta: zViewWasteProducerIndexResponseMeta
});

export const zViewSuggestedWasteProducersFilterQuery = z.object({
    customerId: z.string().uuid(),
    requestType: zRequestType
});

export const zViewSuggestedWasteProducersResponse = z.object({
    items: z.array(zWasteProducerResponse)
});

export const zMissingEwcLevelsError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'missing_ewc_levels'
    ])
});

export const zCreateFileLinkCommand = z.object({
    fileUuid: z.string().uuid(),
    order: z.number().int()
});

export const zPackingGroup = z.enum([
    'not-applicable',
    'one',
    'two',
    'three'
]);

export const zPickUpRequestMaterialCommand = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]).optional(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]).optional(),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]).optional(),
    asn: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    unNumberHazardous: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    estimatedWeightOrVolumeValue: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    estimatedWeightOrVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    poNumber: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    unNumber: z.union([
        z.string(),
        z.null()
    ]).optional(),
    unNumberDescription: z.union([
        z.string(),
        z.null()
    ]).optional(),
    adrClass: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packingGroup: z.union([
        zPackingGroup,
        z.null()
    ]).optional(),
    dangerLabel1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    dangerLabel2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    dangerLabel3: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packagingType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    quantityPackages: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    quantityLabels: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    quantityPallets: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    containerType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    containerVolumeSize: z.union([
        z.string().max(13),
        z.null()
    ]).optional(),
    containerNumber: z.union([
        z.string().max(40),
        z.null()
    ]).optional(),
    containerTransportType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tankerType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isContainerCovered: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    reconciliationNumber: z.union([
        z.string().max(6),
        z.null()
    ]).optional(),
    hazardInducers: z.union([
        z.string().max(100),
        z.null()
    ]).optional(),
    quantityContainers: z.union([
        z.number().gte(0).lte(9),
        z.null()
    ]).optional(),
    tfsNumber: z.union([
        z.string().max(15),
        z.null()
    ]).optional(),
    serialNumber: z.union([
        z.string().max(4),
        z.null()
    ]).optional()
});

export const zUpdatePickUpRequestCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressIds: z.array(z.string()).optional(),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]).optional(),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    totalQuantityPallets: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    startTime: z.union([
        z.string().time(),
        z.null()
    ]).optional(),
    packagingRemark: z.union([
        z.string().max(200),
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional(),
    sendCopyToContacts: z.array(zContact).optional(),
    materials: z.array(zPickUpRequestMaterialCommand).optional(),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional()
});

export const zUpdatePickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zPickUpRequestAlreadySubmitted = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'pick_up_request_already_submitted'
    ])
});

export const zSubmitPickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    requestNumber: z.string()
});

export const zBulkDeletePickUpRequestCommand = z.object({
    pickUpRequestUuids: z.array(z.string())
});

export const zViewPickUpRequestSapResponse = z.object({
    status: zPickUpRequestStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    totalQuantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    packagingRemark: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    materials: z.array(zMaterialResponse),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    additionalFiles: z.array(zFileLinkResponse),
    requestNumber: z.union([
        z.string(),
        z.null()
    ]),
    createdBy: z.union([
        z.string(),
        z.null()
    ]),
    confirmedDate: z.union([
        z.string().date(),
        z.null()
    ])
});

export const zInvalidPickUpRequestCopyErrorMeta = z.object({
    requestNumber: z.string()
});

export const zInvalidPickUpRequestCopyError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'invalid_pick_up_request_copy'
    ]),
    meta: zInvalidPickUpRequestCopyErrorMeta
});

export const zCopyPickUpRequestSapResponse = z.object({
    uuid: z.string().uuid()
});

export const zInvalidUpdateSapPickUpRequestError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'invalid_update_sap_pick_up_request'
    ])
});

export const zPickUpRequestContractLineNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'pick_up_request_contract_line_not_found'
    ])
});

export const zUpdatePickUpRequestMaterialSapCommand = z.object({
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    poNumber: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    position: z.string().max(6)
});

export const zUpdatePickUpRequestPackagingMaterialSapCommand = z.object({
    contractLineId: z.string(),
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]),
    poNumber: z.union([
        z.string().max(35),
        z.null()
    ]),
    contractNumber: z.string(),
    contractItem: z.string(),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.string(),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    quantity: z.number().gte(0),
    position: z.union([
        z.string().max(6),
        z.null()
    ])
});

export const zUpdatePickUpRequestPackagingSapCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    deliveryAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packagingRequestMaterials: z.array(zUpdatePickUpRequestPackagingMaterialSapCommand).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional()
});

export const zUpdatePickUpRequestSapCommand = z.object({
    contacts: z.array(zContact).optional(),
    materials: z.array(zUpdatePickUpRequestMaterialSapCommand).optional(),
    packagingRequest: zUpdatePickUpRequestPackagingSapCommand.optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional()
});

export const zInvalidIndascanSubmitStatusError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'invalid_indascan_submit_status'
    ])
});

export const zTankerTypeResponse = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewTankerTypeIndexResponse = z.object({
    items: z.array(zTankerTypeResponse)
});

export const zTransportTypeResponse = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewTransportTypeIndexResponse = z.object({
    items: z.array(zTransportTypeResponse)
});

export const zSalesOrganization = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewSalesOrganizationIndexResponse = z.object({
    items: z.array(zSalesOrganization)
});

export const zViewUnNumberIndexQueryKey = z.object({
    number: z.string()
});

export const zViewUnNumberIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewUnNumberIndexQueryKey,
        z.null()
    ]).optional()
});

export const zUnNumberResponse = z.object({
    number: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]),
    packingGroup: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel1: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel2: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel3: z.union([
        z.string(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ])
});

export const zViewUnNumberIndexResponseMeta = z.object({
    next: z.union([
        zViewUnNumberIndexQueryKey,
        z.null()
    ])
});

export const zViewUnNumberIndexResponse = z.object({
    items: z.array(zUnNumberResponse),
    meta: zViewUnNumberIndexResponseMeta
});

export const zViewUnNumberIndexForPickUpRequestFilterQuery = z.object({
    contractNumber: z.string(),
    contractItem: z.string(),
    tcNumber: z.string().optional()
});

export const zViewUnNumberIndexForPickUpRequestResponse = z.object({
    items: z.array(zUnNumberResponse)
});

export const zCreateWasteInquiryCommand = z.object({});

export const zCreateWasteInquiryResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteInquiryStatus = z.enum([
    'draft',
    'new',
    'in_progress',
    'conformity_confirmed',
    'solution_defined',
    'offer_sent',
    'offer_approved',
    'completed',
    'rejected'
]);

export const zStateOfMatter = z.enum([
    'gaseous',
    'powder',
    'sludgy',
    'solid',
    'liquid',
    'viscous',
    'liquid-with-solids',
    'no-data-available'
]);

export const zWastePackagingType = z.enum([
    'bulk',
    'packaged'
]);

export const zWasteFlashpointOption = z.enum([
    '< 23°',
    '23° - 60°',
    '> 60°'
]);

export const zWastePhOption = z.enum([
    '< 2',
    '2 - 4',
    '4 - 10',
    '> 10'
]);

export const zStableTemperatureType = z.enum([
    'ambient',
    'other'
]);

export const zWasteCompositionResponse = z.object({
    name: z.union([
        z.string(),
        z.null()
    ]),
    minWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ]),
    maxWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ])
});

export const zWasteLegislationOption = z.enum([
    'none',
    'radioactive',
    'cwc',
    'controlled-drugs',
    'drug-precursor',
    'hg-containing',
    'ozon-depleting-substance',
    'animal-byproduct',
    'infectious-waste',
    'svhc'
]);

export const zSvhcExtraOption = z.enum([
    'other',
    '< 1 mg/kg',
    '> 1 mg/kg'
]);

export const zWastePropertyOption = z.enum([
    'none',
    'explosive',
    'gaseous',
    'peroxide',
    'polymerisation-sensitive',
    'pyrophoric',
    'strong-oxidizing',
    'reactive-with-t-gas',
    'reactive-with-f-gas',
    'high-acute-toxic',
    'thermal-unstable'
]);

export const zWasteDischargeFrequency = z.enum([
    'once-off-stream',
    'regular-stream',
    'once-off-campaign',
    'regular-campaign'
]);

export const zRegulatedTransportOption = z.enum([
    'yes',
    'no',
    'unknown'
]);

export const zWasteInquiryUnNumberResponse = z.object({
    unNumber: z.union([
        z.string(),
        z.null()
    ]),
    packingGroup: z.union([
        zPackingGroup,
        z.null()
    ])
});

export const zWastePackagingOption = z.enum([
    'asf',
    'asp',
    'big-bag',
    'cardboard-box',
    'ibc',
    'metal-drum',
    'oversized-drum',
    'plastic-drum',
    'other'
]);

export const zWeightUnit = z.enum([
    'kg'
]);

export const zWastePackagingResponse = z.object({
    type: z.union([
        zWastePackagingOption,
        z.null()
    ]),
    size: z.union([
        z.string(),
        z.null()
    ]),
    weightPerPieceValue: z.union([
        z.number(),
        z.null()
    ]),
    weightPerPieceUnit: z.union([
        zWeightUnit,
        z.null()
    ]),
    hasInnerPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ])
});

export const zWasteTransportType = z.enum([
    'container',
    'skip',
    'tipper-truck',
    'rel-truck',
    'other'
]);

export const zContainerLoadingType = z.enum([
    'hook',
    'chain'
]);

export const zWasteLoadingType = z.enum([
    'on-waste-collection',
    'before-waste-collection'
]);

export const zWasteTransportInOption = z.enum([
    'tank-trailer',
    'tank-container',
    'no-preference',
    'other'
]);

export const zWasteLoadingMethod = z.enum([
    'gravitational',
    'pump-from-customer',
    'pump-from-haulier'
]);

export const zWasteStoredInOption = z.enum([
    'storage-tank',
    'tank-container',
    'ibcs',
    'drums',
    'other'
]);

export const zCollectionRequirementOption = z.enum([
    'tractor',
    'tractor-trailer',
    'tractor-trailer-tank'
]);

export const zViewWasteInquiryResponse = z.object({
    status: zWasteInquiryStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddress: z.union([
        zPickUpAddressResponse,
        z.null()
    ]),
    wasteStreamName: z.union([
        z.string(),
        z.null()
    ]),
    wasteStreamDescription: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel1Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel2Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel3Name: z.union([
        z.string(),
        z.null()
    ]),
    stateOfMatter: z.union([
        zStateOfMatter,
        z.null()
    ]),
    packagingType: z.union([
        zWastePackagingType,
        z.null()
    ]),
    flashpoint: z.union([
        zWasteFlashpointOption,
        z.null()
    ]),
    ph: z.union([
        zWastePhOption,
        z.null()
    ]),
    specificGravity: z.union([
        z.number(),
        z.null()
    ]),
    stableTemperatureType: z.union([
        zStableTemperatureType,
        z.null()
    ]),
    minStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    maxStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    averageStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    sdsFiles: z.array(zFileLinkResponse),
    noSds: z.boolean(),
    analysisReportFiles: z.array(zFileLinkResponse),
    noAnalysisReport: z.boolean(),
    composition: z.array(zWasteCompositionResponse),
    isSampleAvailable: z.union([
        z.boolean(),
        z.null()
    ]),
    selectedLegislationOptions: z.array(zWasteLegislationOption),
    svhcExtra: z.union([
        zSvhcExtraOption,
        z.null()
    ]),
    legislationRemarks: z.union([
        z.string(),
        z.null()
    ]),
    selectedPropertyOptions: z.array(zWastePropertyOption),
    propertyRemarks: z.union([
        z.string(),
        z.null()
    ]),
    expectedYearlyVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    expectedYearlyVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    expectedPerCollectionQuantity: z.union([
        z.number(),
        z.null()
    ]),
    expectedPerCollectionUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    dischargeFrequency: z.union([
        zWasteDischargeFrequency,
        z.null()
    ]),
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]),
    expectedEndDate: z.union([
        z.string().date(),
        z.null()
    ]),
    collectionRemarks: z.union([
        z.string(),
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isLoadingByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isRegulatedTransport: z.union([
        zRegulatedTransportOption,
        z.null()
    ]),
    unNumbers: z.array(zWasteInquiryUnNumberResponse),
    packaging: z.array(zWastePackagingResponse),
    transportType: z.union([
        zWasteTransportType,
        z.null()
    ]),
    containerLoadingType: z.union([
        zContainerLoadingType,
        z.null()
    ]),
    loadingType: z.union([
        zWasteLoadingType,
        z.null()
    ]),
    transportIn: z.union([
        zWasteTransportInOption,
        z.null()
    ]),
    loadingMethod: z.union([
        zWasteLoadingMethod,
        z.null()
    ]),
    storedIn: z.union([
        zWasteStoredInOption,
        z.null()
    ]),
    transportVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    transportVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    isTankOwnedByCustomer: z.union([
        z.boolean(),
        z.null()
    ]),
    collectionRequirements: z.union([
        zCollectionRequirementOption,
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    additionalFiles: z.array(zFileLinkResponse),
    submittedOn: z.union([
        z.string().datetime(),
        z.null()
    ]),
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zEwcCodeNotFound = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'ewc_code_not_found'
    ])
});

export const zInvalidStableTemperatureError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'invalid_stable_temperature'
    ])
});

export const zFileNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'file_not_accessible'
    ])
});

export const zNoSdsFilesExpected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_sds_files_expected'
    ])
});

export const zNoAnalysisReportFilesExpected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_analysis_report_files_expected'
    ])
});

export const zNoOptionExpectedWhenNoneSelected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_option_expected_when_none_selected'
    ])
});

export const zNoSvhcExtraExpected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_svhc_extra_expected'
    ])
});

export const zWasteComposition = z.object({
    name: z.union([
        z.string().max(132),
        z.null()
    ]),
    minWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ]),
    maxWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ])
});

export const zUnNumber = z.object({
    unNumber: z.union([
        z.string().max(20),
        z.null()
    ]),
    packingGroup: z.union([
        zPackingGroup,
        z.null()
    ])
});

export const zWastePackaging = z.object({
    type: z.union([
        zWastePackagingOption,
        z.null()
    ]),
    size: z.union([
        z.string().max(10),
        z.null()
    ]),
    weightPerPieceValue: z.union([
        z.number().gte(0).lte(99999999999.99),
        z.null()
    ]),
    weightPerPieceUnit: z.union([
        zWeightUnit,
        z.null()
    ]),
    hasInnerPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    remarks: z.union([
        z.string().max(1333),
        z.null()
    ])
});

export const zUpdateWasteInquiryCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isUnknownWasteProducer: z.boolean().optional(),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isUnknownPickUpAddress: z.boolean().optional(),
    wasteStreamName: z.union([
        z.string().max(40),
        z.null()
    ]).optional(),
    wasteStreamDescription: z.union([
        z.string().max(1333),
        z.null()
    ]).optional(),
    ewcLevel1Name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    ewcLevel2Name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    ewcLevel3Name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    stateOfMatter: z.union([
        zStateOfMatter,
        z.null()
    ]).optional(),
    packagingType: z.union([
        zWastePackagingType,
        z.null()
    ]).optional(),
    flashpoint: z.union([
        zWasteFlashpointOption,
        z.null()
    ]).optional(),
    ph: z.union([
        zWastePhOption,
        z.null()
    ]).optional(),
    specificGravity: z.union([
        z.number().gte(-9999999.9).lte(9999999.9),
        z.null()
    ]).optional(),
    stableTemperatureType: z.union([
        zStableTemperatureType,
        z.null()
    ]).optional(),
    minStableTemperature: z.union([
        z.number().gte(-99999).lte(99999),
        z.null()
    ]).optional(),
    maxStableTemperature: z.union([
        z.number().gte(-99999).lte(99999),
        z.null()
    ]).optional(),
    averageStableTemperature: z.union([
        z.number().gte(-99999).lte(99999),
        z.null()
    ]).optional(),
    sdsFiles: z.array(zCreateFileLinkCommand).optional(),
    noSds: z.boolean().optional(),
    analysisReportFiles: z.array(zCreateFileLinkCommand).optional(),
    noAnalysisReport: z.boolean().optional(),
    composition: z.array(zWasteComposition).optional(),
    isSampleAvailable: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    selectedLegislationOptions: z.array(zWasteLegislationOption).optional(),
    svhcExtra: z.union([
        zSvhcExtraOption,
        z.null()
    ]).optional(),
    legislationRemarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    selectedPropertyOptions: z.array(zWastePropertyOption).optional(),
    propertyRemarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    expectedYearlyVolumeAmount: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    expectedYearlyVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    expectedPerCollectionQuantity: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    expectedPerCollectionUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    dischargeFrequency: z.union([
        zWasteDischargeFrequency,
        z.null()
    ]).optional(),
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    expectedEndDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    collectionRemarks: z.union([
        z.string().max(1333),
        z.null()
    ]).optional(),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    isLoadingByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    isRegulatedTransport: z.union([
        zRegulatedTransportOption,
        z.null()
    ]).optional(),
    unNumbers: z.array(zUnNumber).optional(),
    packaging: z.array(zWastePackaging).optional(),
    transportType: z.union([
        zWasteTransportType,
        z.null()
    ]).optional(),
    loadingType: z.union([
        zWasteLoadingType,
        z.null()
    ]).optional(),
    transportIn: z.union([
        zWasteTransportInOption,
        z.null()
    ]).optional(),
    loadingMethod: z.union([
        zWasteLoadingMethod,
        z.null()
    ]).optional(),
    storedIn: z.union([
        zWasteStoredInOption,
        z.null()
    ]).optional(),
    containerLoadingType: z.union([
        zContainerLoadingType,
        z.null()
    ]).optional(),
    transportVolumeAmount: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    transportVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    isTankOwnedByCustomer: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    collectionRequirements: z.union([
        zCollectionRequirementOption,
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string().max(1333),
        z.null()
    ]).optional(),
    sendCopyToContacts: z.array(zContact).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional()
});

export const zUpdateWasteInquiryResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteInquiryAlreadySubmitted = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'waste_inquiry_already_submitted'
    ])
});

export const zSubmitWasteInquiryResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    inquiryNumber: z.string()
});

export const zViewWasteInquiryIndexFilterQuery = z.object({
    statuses: z.array(zWasteInquiryStatus)
});

export const zViewWasteInquiryIndexSapQueryKey = z.object({
    skipToken: z.string()
});

export const zViewWasteInquiryIndexDbQueryKey = z.object({
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]),
    uuid: z.string().uuid()
});

export const zViewWasteInquiryIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        z.unknown(),
        z.null()
    ]).optional()
});

export const zWasteInquiryResponse = z.object({
    uuid: z.union([
        z.string(),
        z.null()
    ]),
    inquiryNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteStreamName: z.union([
        z.string(),
        z.null()
    ]),
    date: z.union([
        z.string().date(),
        z.null()
    ]),
    contractId: z.union([
        z.string(),
        z.null()
    ]),
    contractItem: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    salesOrganisationId: z.union([
        z.string(),
        z.null()
    ]),
    salesOrganisationName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    requestorName: z.union([
        z.string(),
        z.null()
    ]),
    status: zWasteInquiryStatus,
    ewcLevel1: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel2: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel3: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewWasteInquiryIndexResponseMeta = z.object({
    next: z.union([
        z.unknown(),
        z.null()
    ])
});

export const zViewWasteInquiryIndexResponse = z.object({
    items: z.array(zWasteInquiryResponse),
    meta: zViewWasteInquiryIndexResponseMeta
});

export const zViewWasteInquirySapResponse = z.object({
    status: zWasteInquiryStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddress: z.union([
        zPickUpAddressResponse,
        z.null()
    ]),
    wasteStreamName: z.union([
        z.string(),
        z.null()
    ]),
    wasteStreamDescription: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel1Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel2Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel3Name: z.union([
        z.string(),
        z.null()
    ]),
    stateOfMatter: z.union([
        zStateOfMatter,
        z.null()
    ]),
    packagingType: z.union([
        zWastePackagingType,
        z.null()
    ]),
    flashpoint: z.union([
        zWasteFlashpointOption,
        z.null()
    ]),
    ph: z.union([
        zWastePhOption,
        z.null()
    ]),
    specificGravity: z.union([
        z.number(),
        z.null()
    ]),
    stableTemperatureType: z.union([
        zStableTemperatureType,
        z.null()
    ]),
    minStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    maxStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    averageStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    sdsFiles: z.array(zFileLinkResponse),
    noSds: z.boolean(),
    analysisReportFiles: z.array(zFileLinkResponse),
    noAnalysisReport: z.boolean(),
    composition: z.array(zWasteCompositionResponse),
    isSampleAvailable: z.union([
        z.boolean(),
        z.null()
    ]),
    selectedLegislationOptions: z.array(zWasteLegislationOption),
    svhcExtra: z.union([
        zSvhcExtraOption,
        z.null()
    ]),
    legislationRemarks: z.union([
        z.string(),
        z.null()
    ]),
    selectedPropertyOptions: z.array(zWastePropertyOption),
    propertyRemarks: z.union([
        z.string(),
        z.null()
    ]),
    expectedYearlyVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    expectedYearlyVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    expectedPerCollectionQuantity: z.union([
        z.number(),
        z.null()
    ]),
    expectedPerCollectionUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    dischargeFrequency: z.union([
        zWasteDischargeFrequency,
        z.null()
    ]),
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]),
    expectedEndDate: z.union([
        z.string().date(),
        z.null()
    ]),
    collectionRemarks: z.union([
        z.string(),
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isLoadingByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isRegulatedTransport: z.union([
        zRegulatedTransportOption,
        z.null()
    ]),
    unNumbers: z.array(zWasteInquiryUnNumberResponse),
    packaging: z.array(zWastePackagingResponse),
    transportType: z.union([
        zWasteTransportType,
        z.null()
    ]),
    containerLoadingType: z.union([
        zContainerLoadingType,
        z.null()
    ]),
    loadingType: z.union([
        zWasteLoadingType,
        z.null()
    ]),
    transportIn: z.union([
        zWasteTransportInOption,
        z.null()
    ]),
    loadingMethod: z.union([
        zWasteLoadingMethod,
        z.null()
    ]),
    storedIn: z.union([
        zWasteStoredInOption,
        z.null()
    ]),
    transportVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    transportVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    isTankOwnedByCustomer: z.union([
        z.boolean(),
        z.null()
    ]),
    collectionRequirements: z.union([
        zCollectionRequirementOption,
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    additionalFiles: z.array(zFileLinkResponse),
    submittedOn: z.union([
        z.string().datetime(),
        z.null()
    ]),
    inquiryNumber: z.union([
        z.string(),
        z.null()
    ]),
    createdBy: z.union([
        z.string(),
        z.null()
    ]),
    contractNumber: z.union([
        z.string(),
        z.null()
    ]),
    contractItem: z.union([
        z.string(),
        z.null()
    ])
});

export const zCreateFileSapCommand = z.object({
    fileUuid: z.string().uuid()
});

export const zAddDocumentToWasteInquirySapCommand = z.object({
    sdsFiles: z.array(zCreateFileSapCommand).optional(),
    analysisReportFiles: z.array(zCreateFileSapCommand).optional(),
    additionalFiles: z.array(zCreateFileSapCommand).optional()
});

export const zBulkDeleteWasteInquiryCommand = z.object({
    wasteInquiryUuids: z.array(z.string())
});

export const zCopyWasteInquirySapResponse = z.object({
    uuid: z.string().uuid()
});

export const zCreateWeeklyPlanningRequestCommand = z.object({});

export const zCreateWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zCustomerNotProvidedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'customer_not_provided'
    ])
});

export const zUpdateWeeklyPlanningRequestCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressIds: z.array(z.string()).optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional(),
    sendCopyToContacts: z.array(zContact).optional()
});

export const zUpdateWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zAddWprPickUpRequestCommand = z.object({
    pickUpRequestUuid: z.string().uuid()
});

export const zAddWprPickUpRequestResponse = z.object({
    uuid: z.string().uuid()
});

export const zViewWprPickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        z.string(),
        z.null()
    ]),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    materialType: z.union([
        z.string(),
        z.null()
    ]),
    packagingIndicator: z.union([
        z.string(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    startTime: z.union([
        z.string().time(),
        z.null()
    ])
});

export const zViewWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    additionalFiles: z.array(zFileLinkResponse),
    pickUpRequests: z.array(zViewWprPickUpRequestResponse)
});

export const zWeeklyPlanningRequestAlreadySubmittedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'weekly_planning_request_already_submitted_error'
    ])
});

export const zSubmitWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    inquiryNumber: z.string()
});

export const zInternalServerApiError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '500'
    ]),
    code: z.enum([
        'internal_server_error'
    ])
});

export const zViewMeV1Response = zViewMeResponse;

export const zViewUserDetailV1Response = zViewUserDetailResponse;

export const zViewUserIndexV1Response = zViewUserIndexResponse;

export const zViewPermissionIndexV1Response = zViewPermissionIndexResponse;

export const zViewCustomerIndexV1Response = zViewCustomerIndexResponse;

export const zViewSuggestedCustomersV1Response = zViewSuggestedCustomersResponse;

export const zViewCustomerCountryV1Response = zViewCustomerCountryResponse;

export const zViewDomainEventLogIndexV1Response = zViewDomainEventLogIndexResponse;

export const zCreateFileV1Response = zCreateFileResponse;

export const zSearchCollectionsV1Response = zSearchCollectionsResponse;

export const zViewJobsIndexV1Response = zViewJobsIndexResponse;

export const zViewJobDetailV1Response = zViewJobDetailResponse;

export const zGetMyNotificationPreferencesV1Response = zGetMyNotificationPreferencesResponse;

export const zGetNotificationTypesConfigV1Response = zGetNotificationTypesConfigResponse;

export const zUpdateMyChannelNotificationPreferenceV1Response = z.void();

export const zSendTestNotificationV1Response = z.void();

export const zGetMyNotificationsV1Response = zGetMyNotificationsResponse;

export const zViewUnreadNotificationsCountV1Response = zViewUnreadNotificationsCountResponse;

export const zViewUserNotificationDetailV1Response = zTestNotificationNotification;

export const zMarkAllNotificationAsReadV1Response = z.void();

export const zUpdateMyNotificationTypePreferenceV1Response = z.void();

export const zMarkNotificationAsReadV1Response = z.void();

export const zMarkNotificationAsUnreadV1Response = z.void();

export const zUpdateMyNotificationPreferencePresetV1Response = z.void();

export const zMigrateNotificationTypesV1Response = z.void();

export const zViewRoleIndexV1Response = zViewRoleIndexResponse;

export const zUpdateRolesPermissionsV1Response = z.void();

export const zCreateRoleV1Response = zCreateRoleResponse;

export const zClearRolePermissionsCacheV1Response = z.void();

export const zDeleteRoleV1Response = z.void();

export const zViewRoleDetailV1Response = zViewRoleDetailResponse;

export const zGetApiInfoResponse2 = zGetApiInfoResponse;

export const zViewUiPreferencesV1Response = zViewUiPreferencesResponse;

export const zViewAnnouncementIndexV1Response = zViewAnnouncementIndexResponse;

export const zCreateAnnouncementV1Response = zCreateAnnouncementResponse;

export const zViewAnnouncementV1Response = zViewAnnouncementResponse;

export const zUpdateAnnouncementV1Response = zUpdateAnnouncementResponse;

export const zViewDashboardAnnouncementIndexV1Response = zViewDashboardAnnouncementIndexResponse;

export const zViewDashboardAnnouncementV1Response = zViewDashboardAnnouncementResponse;

export const zViewContactIndexV1Response = zViewContactIndexResponse;

export const zCreateContactV1Response = zCreateContactResponse;

export const zDeleteContactV1Response = z.void();

export const zUpdateContactV1Response = z.void();

export const zViewContainerTypeIndexV1Response = zViewContainerTypeIndexResponse;

export const zViewContractLineIndexV1Response = zViewContractLineIndexResponse;

export const zViewWprContractLineIndexV1Response = zViewWprContractLineIndexResponse;

export const zViewPackagingRequestContractLineIndexV1Response = zViewPackagingRequestContractLineIndexResponse;

export const zGenerateContractLinesPdfV1Response = zGenerateContractLinesPdfResponse;

export const zViewDocumentIndexV1Response = zViewDocumentIndexResponse;

export const zViewUserSiteIndexV1Response = z.array(zViewUserSiteIndexResponse);

export const zViewDynamicTableColumnIndexV1Response = zDynamicTableIndexColumnResponse;

export const zViewDynamicTableViewIndexV1Response = zDynamicTableViewIndexResponse;

export const zCreateDynamicTableViewV1Response = zCreateDynamicTableViewResponse;

export const zViewDefaultDynamicTableViewV1Response = zViewDefaultDynamicTableViewResponse;

export const zUpdateDynamicTableViewV1Response = zUpdateDynamicTableViewResponse;

export const zViewEwcCodeIndexV1Response = zViewEwcCodeIndexResponse;

export const zViewInvoiceIndexV1Response = zViewInvoiceIndexResponse;

export const zViewDraftInvoiceIndexV1Response = zViewDraftInvoiceIndexResponse;

export const zSubscribeToNewsletterV1Response = z.void();

export const zViewNewsItemIndexV1Response = zViewNewsIndexResponse;

export const zCreateNewsItemV1Response = zCreateNewsItemResponse;

export const zViewNewsItemV1Response = zViewNewsItemResponse;

export const zUpdateNewsItemV1Response = zUpdateNewsItemResponse;

export const zViewDashboardNewsItemIndexV1Response = zViewDashboardNewsIndexResponse;

export const zViewDashboardNewsItemV1Response = zViewDashboardNewsItemResponse;

export const zCreatePackagingRequestV1Response = zCreatePackagingRequestResponse;

export const zViewPackagingRequestV1Response = zViewPackagingRequestResponse;

export const zUpdatePackagingRequestV1Response = zUpdatePackagingRequestResponse;

export const zSubmitPackagingRequestV1Response = zSubmitPackagingRequestResponse;

export const zCopyPackagingRequestSapV1Response = zCopyPackagingRequestSapResponse;

export const zViewPackagingTypeIndexV1Response = zViewPackagingTypeIndexResponse;

export const zViewPickUpAddressIndexV1Response = zViewPickUpAddressIndexResponse;

export const zViewSuggestedPickUpAddressesV1Response = zViewSuggestedPickUpAddressesResponse;

export const zGetIsPoNumberAndCostCenterRequiredV1Response = zGetIsPoNumberAndCostCenterRequiredResponse;

export const zViewPickUpRequestIndexV1Response = zViewPickUpRequestIndexResponse;

export const zCreatePickUpRequestV1Response = zCreatePickUpRequestResponse;

export const zViewPickUpRequestV1Response = zViewPickUpRequestResponse;

export const zUpdatePickUpRequestV1Response = zUpdatePickUpRequestResponse;

export const zViewWasteProducerIndexV1Response = zViewWasteProducerIndexResponse;

export const zViewSuggestedWasteProducersV1Response = zViewSuggestedWasteProducersResponse;

export const zSubmitPickUpRequestV1Response = zSubmitPickUpRequestResponse;

export const zViewPickUpRequestSapV1Response = zViewPickUpRequestSapResponse;

export const zCopyPickUpRequestSapV1Response = zCopyPickUpRequestSapResponse;

export const zViewTankerTypeIndexV1Response = zViewTankerTypeIndexResponse;

export const zViewTransportTypeIndexV1Response = zViewTransportTypeIndexResponse;

export const zViewSalesOrganizationIndexV1Response = zViewSalesOrganizationIndexResponse;

export const zViewUnNumberIndexV1Response = zViewUnNumberIndexResponse;

export const zViewUnNumberIndexForPickUpRequestV1Response = zViewUnNumberIndexForPickUpRequestResponse;

export const zViewWasteInquiryIndexV1Response = zViewWasteInquiryIndexResponse;

export const zCreateWasteInquiryV1Response = zCreateWasteInquiryResponse;

export const zViewWasteInquiryV1Response = zViewWasteInquiryResponse;

export const zUpdateWasteInquiryV1Response = zUpdateWasteInquiryResponse;

export const zSubmitWasteInquiryV1Response = zSubmitWasteInquiryResponse;

export const zViewWasteInquirySapV1Response = zViewWasteInquirySapResponse;

export const zAddDocumentToWasteInquirySapV1Response = z.void();

export const zCopyWasteInquirySapV1Response = zCopyWasteInquirySapResponse;

export const zCreateWeeklyPlanningRequestV1Response = zCreateWeeklyPlanningRequestResponse;

export const zViewWeeklyPlanningRequestV1Response = zViewWeeklyPlanningRequestResponse;

export const zUpdateWeeklyPlanningRequestV1Response = zUpdateWeeklyPlanningRequestResponse;

export const zAddWprPickUpRequestV1Response = zAddWprPickUpRequestResponse;

export const zSubmitWeeklyPlanningRequestV1Response = zSubmitWeeklyPlanningRequestResponse;