// This file is auto-generated by @hey-api/openapi-ts

export enum Permission {
    ALL_PERMISSIONS = 'all_permissions',
    ANNOUNCEMENT_MANAGE = 'announcement.manage',
    BALANCED_SCORECARD_READ = 'balanced-scorecard.read',
    CERTIFICATE_READ = 'certificate.read',
    CONTACT_MANAGE = 'contact.manage',
    CONTACT_READ = 'contact.read',
    CONTRACT_LINE_READ = 'contract-line.read',
    CONTRACT_LINE_MANAGE = 'contract-line.manage',
    DOCUMENT_MASTER_TABLE = 'document.master-table',
    DOCUMENT_TFS = 'document.tfs',
    DOCUMENT_QUOTATION = 'document.quotation',
    DOCUMENT_MINUTES_AND_PRESENTATIONS = 'document.minutes-and-presentations',
    DOCUMENT_MANUAL = 'document.manual',
    DOCUMENT_BSC = 'document.bsc',
    DOCUMENT_CONTRACT = 'document.contract',
    DOCUMENT_TRANSPORT = 'document.transport',
    DYNAMIC_TABLE_VIEW_MANAGE = 'dynamic-table-view.manage',
    ECMR_READ = 'ecmr.read',
    EVENT_LOG_READ = 'event-log.read',
    INVOICE_READ = 'invoice.read',
    INVOICE_MANAGE = 'invoice.manage',
    JOBS_READ_INDEX = 'jobs.read.index',
    JOBS_READ_DETAIL = 'jobs.read.detail',
    NEWS_ITEM_MANAGE = 'news-item.manage',
    NEWSLETTER_SUBSCRIBE = 'newsletter.subscribe',
    PICK_UP_REQUEST_READ = 'pick-up-request.read',
    PICK_UP_REQUEST_MANAGE = 'pick-up-request.manage',
    PACKAGING_REQUEST_READ = 'packaging-request.read',
    PACKAGING_REQUEST_MANAGE = 'packaging-request.manage',
    POWER_BI_READ = 'power-bi.read',
    ROLE_READ = 'role.read',
    ROLE_MANAGE = 'role.manage',
    SEND_PUSH_NOTIFICATION = 'send_push_notification',
    TYPESENSE = 'typesense',
    USER_READ = 'user.read',
    USER_MANAGE = 'user.manage',
    USER_IMPERSONATE = 'user.impersonate',
    WASTE_INQUIRY_READ = 'waste-inquiry.read',
    WASTE_INQUIRY_MANAGE = 'waste-inquiry.manage',
    WEEKLY_PLANNING_REQUEST_READ = 'weekly-planning-request.read',
    WEEKLY_PLANNING_REQUEST_MANAGE = 'weekly-planning-request.manage'
}

export type ViewRoleDetailResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    permissions: Array<Permission>;
    isDefault: boolean;
    isSystemAdmin: boolean;
};

export type ViewMeResponse = {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    roles: Array<ViewRoleDetailResponse>;
    isInternalUser: boolean;
};

export type ViewUserDetailResponse = {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    roles: Array<ViewRoleDetailResponse>;
};

export type PaginatedOffsetQuery = {
    limit: number;
    offset: number;
};

export type UserIndexRoleView = {
    uuid: string;
    name: string;
};

export type UserIndexView = {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    roles: Array<UserIndexRoleView>;
};

export type PaginatedOffsetResponseMeta = {
    /**
     * the total amount of items that exist
     */
    total: number;
    /**
     * the amount of items skipped
     */
    offset: number;
    /**
     * the amount of items per response
     */
    limit: number;
};

export type ViewUserIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<UserIndexView>;
    meta: PaginatedOffsetResponseMeta;
};

export type ViewPermissionIndexPermissionResponse = {
    name: string;
    key: Permission;
    description: string;
};

export type ViewPermissionIndexGroupResponse = {
    name: string;
    permissions: Array<ViewPermissionIndexPermissionResponse>;
};

export type ViewPermissionIndexResponse = {
    groups: Array<ViewPermissionIndexGroupResponse>;
};

export type ViewCustomerIndexQueryKey = {
    skipToken: string;
};

export type ViewCustomerIndexPaginationQuery = {
    limit: number;
    key?: ViewCustomerIndexQueryKey | null;
};

export type CoordinatesResponse = {
    latitude: number;
    longitude: number;
};

export type AddressResponse = {
    /**
     * ISO 3166-1 alpha-2
     */
    countryCode: string | null;
    postalCode: string;
    /**
     * City, town or village
     */
    locality: string;
    /**
     * Generalized street address
     */
    addressLine1: string;
    /**
     * Additional address details (apartment, floor, etc.)
     */
    addressLine2: string | null;
    coordinates: CoordinatesResponse | null;
};

export type CustomerResponse = {
    id: string;
    name: string;
    address: string | unknown | null;
};

export type ViewCustomerIndexResponseMeta = {
    next: ViewCustomerIndexQueryKey | null;
};

export type ViewCustomerIndexResponse = {
    items: Array<CustomerResponse>;
    meta: ViewCustomerIndexResponseMeta;
};

export enum RequestType {
    WASTE = 'waste',
    PICK_UP = 'pick-up'
}

export type ViewSuggestedCustomersFilterQuery = {
    requestType: RequestType;
};

export type ViewSuggestedCustomersResponse = {
    items: Array<CustomerResponse>;
};

export type CustomerNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'customer_not_found';
};

export type ViewCustomerCountryResponse = {
    countryCode: string;
};

export enum SubjectType {
    ANNOUNCEMENT = 'announcement',
    CONTACT = 'contact',
    DYNAMIC_TABLE_VIEW = 'dynamic-table-view',
    FILE = 'file',
    NEWS_ITEM = 'news-item',
    PICK_UP_REQUEST = 'pick-up-request',
    PACKAGING_REQUEST = 'packaging-request',
    ROLE = 'role',
    USER = 'user',
    WASTE_INQUIRY = 'waste-inquiry',
    WEEKLY_PLANNING_REQUEST = 'weekly-planning-request'
}

export type ViewDomainEventLogIndexFilterQuery = {
    subjectType?: SubjectType;
    subjectId?: string;
    userUuid?: string;
};

export type ViewDomainEventLogIndexQueryKey = {
    createdAt: string;
    uuid: string;
};

export type ViewDomainEventLogIndexPaginationQuery = {
    limit: number;
    key?: ViewDomainEventLogIndexQueryKey | null;
};

export type UserCreatedEventContent = {
    userUuid: string;
};

export type UserCreatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'user.created';
    content: UserCreatedEventContent;
};

export type RoleCreatedEventContent = {
    roleUuid: string;
    roleName: string;
};

export type RoleCreatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'role.created';
    content: RoleCreatedEventContent;
};

export type RoleDeletedEventContent = {
    roleUuid: string;
    roleName: string;
};

export type RoleDeletedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'role.deleted';
    content: RoleDeletedEventContent;
};

export type RoleRenamedEventContent = {
    roleUuid: string;
    previousName: string;
    newName: string;
};

export type RoleRenamedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'role.renamed';
    content: RoleRenamedEventContent;
};

export type RolePermissionsUpdatedEventContent = {
    roleUuid: string;
    newPermissions: Array<Permission>;
    roleName: string;
};

export type RolePermissionsUpdatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'role.permissions.updated';
    content: RolePermissionsUpdatedEventContent;
};

export type RolePermissionsCacheClearedEventContent = {
    roleUuids: string;
};

export type RolePermissionsCacheClearedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'role.permissions.cache.cleared';
    content: RolePermissionsCacheClearedEventContent;
};

export enum NotificationType {
    USER_CREATED = 'user.created',
    TEST_NOTIFICATION = 'test-notification'
}

export type NotificationCreatedEventContent = {
    uuid: string;
    type: NotificationType;
};

export type NotificationCreatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'notification.created';
    content: NotificationCreatedEventContent;
};

export enum NotificationChannel {
    EMAIL = 'email',
    SMS = 'sms',
    APP = 'app',
    PUSH = 'push'
}

export type UserNotificationCreatedEventContent = {
    notificationUuid: string;
    channel: NotificationChannel;
    userUuid: string;
};

export type UserNotificationCreatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'user.notification.created';
    content: UserNotificationCreatedEventContent;
};

export type ContactCreatedEventContent = {
    contactUuid: string;
};

export type ContactCreatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'contact.created';
    content: ContactCreatedEventContent;
};

export type ContactUpdatedEventContent = {
    contactUuid: string;
};

export type ContactUpdatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'contact.updated';
    content: ContactUpdatedEventContent;
};

export type ContactDeletedEventContent = {
    contactUuid: string;
};

export type ContactDeletedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'contact.deleted';
    content: ContactDeletedEventContent;
};

export type FileCreatedEventContent = {
    fileUuid: string;
    fileName: string;
};

export type FileCreatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'file.created';
    content: FileCreatedEventContent;
};

export type FileUploadedEventContent = {
    fileUuid: string;
    fileName: string;
};

export type FileUploadedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'file.uploaded';
    content: FileUploadedEventContent;
};

export type NotificationReadEventContent = {
    notificationUuid: string;
    userUuid: string;
};

export type NotificationReadDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'notification.read';
    content: NotificationReadEventContent;
};

export type NotificationUnreadEventContent = {
    notificationUuid: string;
    userUuid: string;
};

export type NotificationUnreadDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'notification.unread';
    content: NotificationUnreadEventContent;
};

export enum NotificationPreset {
    ALL = 'all',
    DEFAULT = 'default',
    CUSTOM = 'custom',
    NONE = 'none'
}

export type NotificationPreferencePresetEventContent = {
    userUuid: string;
    preset: NotificationPreset;
};

export type NotificationPreferencePresetUpdatedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'notification.preference.preset.updated';
    content: NotificationPreferencePresetEventContent;
};

export type NotificationTypesMigratedEventContent = {
    types: Array<NotificationType>;
};

export type NotificationTypesMigratedDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'notification.types.migrated';
    content: NotificationTypesMigratedEventContent;
};

export type TestNotificationSentEventContent = {
    message: string;
};

export type TestNotificationSentDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'test-notification.sent';
    content: TestNotificationSentEventContent;
};

export type AllNotificationsMarkedAsReadEventContent = {
    userUuid: string;
};

export type NotificationReadAllDomainEventLog = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
    type: 'notification.read.all';
    content: AllNotificationsMarkedAsReadEventContent;
};

export type DomainEventLogResponse = {
    uuid: string;
    createdAt: string;
    version: number;
    source: string;
    userUuid: string | null;
    message: string;
    subjectType: SubjectType | null;
    subjectId: string | null;
};

export type ViewDomainEventLogIndexResponseMeta = {
    next: ViewDomainEventLogIndexQueryKey | null;
};

export type ViewDomainEventLogIndexResponse = {
    items: Array<UserCreatedDomainEventLog | RoleCreatedDomainEventLog | RoleDeletedDomainEventLog | RoleRenamedDomainEventLog | RolePermissionsUpdatedDomainEventLog | RolePermissionsCacheClearedDomainEventLog | NotificationCreatedDomainEventLog | UserNotificationCreatedDomainEventLog | ContactCreatedDomainEventLog | ContactUpdatedDomainEventLog | ContactDeletedDomainEventLog | FileCreatedDomainEventLog | FileUploadedDomainEventLog | NotificationReadDomainEventLog | NotificationUnreadDomainEventLog | NotificationPreferencePresetUpdatedDomainEventLog | NotificationTypesMigratedDomainEventLog | TestNotificationSentDomainEventLog | NotificationReadAllDomainEventLog>;
    meta: ViewDomainEventLogIndexResponseMeta;
};

export enum MimeType {
    APPLICATION_PDF = 'application/pdf',
    APPLICATION_MSWORD = 'application/msword',
    APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_WORDPROCESSINGML_DOCUMENT = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    APPLICATION_VND_MS_POWERPOINT = 'application/vnd.ms-powerpoint',
    APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_PRESENTATIONML_PRESENTATION = 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    TEXT_PLAIN = 'text/plain',
    TEXT_HTML = 'text/html',
    IMAGE_JPEG = 'image/jpeg',
    IMAGE_PNG = 'image/png',
    IMAGE_TIFF = 'image/tiff',
    IMAGE_BMP = 'image/bmp',
    IMAGE_HEIC = 'image/heic',
    IMAGE_WEBP = 'image/webp',
    IMAGE_GIF = 'image/gif',
    TEXT_CSV = 'text/csv',
    APPLICATION_VND_MS_OUTLOOK = 'application/vnd.ms-outlook',
    APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_PRESENTATIONML_SLIDESHOW = 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    APPLICATION_RTF = 'application/rtf',
    APPLICATION_VND_MS_EXCEL = 'application/vnd.ms-excel',
    APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_SPREADSHEETML_SHEET = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}

export type CreateFileCommand = {
    name: string;
    mimeType: MimeType;
};

export type CreateFileResponse = {
    uuid: string;
    name: string;
    mimeType: MimeType | null;
    uploadUrl: string;
};

export enum GlobalSearchCollectionName {
    USER = 'user',
    CONTACT = 'contact'
}

export type SearchCollectionsFilterQuery = {
    collections?: Array<GlobalSearchCollectionName>;
};

export type SearchCollectionUserResponse = {
    uuid: string;
    firstName: string;
    lastName: string;
    email: string;
};

export type SearchCollectionContactResponse = {
    uuid: string;
    firstName: string;
    lastName: string;
    email: string;
};

export type SearchCollectionsResponseItem = {
    collection: GlobalSearchCollectionName;
    entity: SearchCollectionUserResponse | SearchCollectionContactResponse;
    text_match: number;
};

export type SearchCollectionsResponse = {
    items: Array<SearchCollectionsResponseItem>;
};

export enum ViewJobsIndexSortQueryKey {
    CREATED_AT = 'createdAt'
}

export enum SortDirection {
    ASC = 'asc',
    DESC = 'desc'
}

export type ViewJobsIndexSortQuery = {
    key: ViewJobsIndexSortQueryKey;
    order: SortDirection;
};

export enum QueueName {
    SYSTEM = 'system'
}

export type ViewJobsIndexFilterQuery = {
    queueNames?: Array<QueueName>;
    archived?: boolean;
};

export type ViewJobsIndexQueryKey = {
    createdAt?: string;
    id: string;
};

export type ViewJobsIndexPaginationQuery = {
    limit: number;
    key?: ViewJobsIndexQueryKey | null;
};

export enum JobStatus {
    CREATED = 'created',
    ACTIVE = 'active',
    COMPLETED = 'completed',
    RETRY = 'retry',
    FAILED = 'failed',
    CANCELLED = 'cancelled'
}

export type ViewJobsIndexItemResponse = {
    queueName: QueueName;
    id: string;
    name: string;
    status: JobStatus;
    createdAt: string;
    completedAt: string | null;
};

export type ViewJobsIndexResponseMeta = {
    next: ViewJobsIndexQueryKey | null;
};

export type ViewJobsIndexResponse = {
    items: Array<ViewJobsIndexItemResponse>;
    meta: ViewJobsIndexResponseMeta;
};

export type ViewJobDetailResponse = {
    id: string;
    queueName: QueueName;
    priority: number;
    name: string;
    data: {
        [key: string]: unknown;
    };
    status: JobStatus;
    retryLimit: number;
    retryCount: number;
    retryDelay: number;
    retryBackoff: boolean;
    startAfter: string;
    startedAt: string | null;
    singletonKey: string | null;
    singletonOn: string | null;
    expireIn: {
        [key: string]: unknown;
    };
    createdAt: string;
    completedAt: string | null;
    keepUntil: string;
    output: {
        [key: string]: unknown;
    } | null;
    deadLetter: string | null;
    policy: string | null;
};

export type PreferenceTypes = {
    email: Array<NotificationType>;
    sms: Array<NotificationType>;
    app: Array<NotificationType>;
    push: Array<NotificationType>;
};

export type GetMyNotificationPreferencesResponse = {
    preset: NotificationPreset;
    emailEnabled: boolean;
    smsEnabled: boolean;
    appEnabled: boolean;
    pushEnabled: boolean;
    preferences: PreferenceTypes;
};

export type NotificationTypeChannelConfig = {
    channel: NotificationChannel;
    defaultValue: boolean;
    isSupported: boolean;
};

export type NotificationTypeConfig = {
    type: NotificationType;
    channelConfigs: Array<NotificationTypeChannelConfig>;
};

export type GetNotificationTypesConfigResponse = {
    items: Array<NotificationTypeConfig>;
};

export type UpdateMyChannelNotificationPreferenceCommand = {
    channel: NotificationChannel;
    isEnabled: boolean;
};

export type SendTestNotificationCommand = {
    message: string;
};

export type GetMyNotificationsFilterQuery = {
    onlyUnread?: string;
};

export type GetMyNotificationsQueryKey = {
    createdAt: string;
    notificationUuid: string;
};

export type GetMyNotificationsPaginationQuery = {
    limit: number;
    key?: GetMyNotificationsQueryKey;
};

export type CreatedByUserResponse = {
    uuid: string;
    name: string;
};

export type TestNotificationContent = {
    message: string;
};

export type TestNotificationNotification = {
    createdAt: string;
    readAt: string | null;
    notificationUuid: string;
    createdByUser: CreatedByUserResponse | null;
    message: string;
    type: 'test-notification';
    meta: TestNotificationContent;
};

export type GetMyNotificationsResponseMeta = {
    next: GetMyNotificationsQueryKey | null;
};

export type GetMyNotificationsResponse = {
    items: Array<TestNotificationNotification>;
    meta: GetMyNotificationsResponseMeta;
};

export type ViewUnreadNotificationsCountResponse = {
    amount: number;
    exceedsLimit: boolean;
};

export type UpdateMyNotificationTypePreferenceCommand = {
    channel: NotificationChannel;
    isEnabled: boolean;
    types: Array<NotificationType>;
};

export type UserNotificationNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'user_notification_not_found';
};

export type UpdateMyNotificationPreferencePresetCommand = {
    preset: NotificationPreset;
};

export type ErrorSource = {
    pointer: string;
};

export type MigrationAlreadyPerformedErrorMeta = {
    type: Array<NotificationType>;
};

export type MigrationAlreadyPerformedError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'migration_already_performed';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: MigrationAlreadyPerformedErrorMeta;
};

export type MigrateNotificationTypesCommand = {
    types: Array<NotificationType>;
};

export type CreateRoleCommand = {
    /**
     * The name of the role
     */
    name: string;
};

export type CreateRoleResponse = {
    uuid: string;
};

export type ClearRolePermissionsCacheCommand = {
    /**
     * clears the cache for all roles when omitted or null
     */
    roleUuids?: Array<string> | null;
};

export type UpdateRoleCommand = {
    /**
     * The name of the role
     */
    name: string;
};

export type RoleResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    permissions: Array<Permission>;
    isDefault: boolean;
    isSystemAdmin: boolean;
};

export type ViewRoleIndexResponse = {
    items: Array<RoleResponse>;
};

export type RoleNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'role_not_found';
};

export type UpdateRolesPermissionsCommandItem = {
    roleUuid: string;
    permissions: Array<Permission>;
};

export type UpdateRolesPermissionsCommand = {
    roles: Array<UpdateRolesPermissionsCommandItem>;
};

export type GetApiInfoResponse = {
    environment: string;
    /**
     * Commit SHA of the current build
     */
    commit: string;
    /**
     * Version of the current build
     */
    version: string;
    /**
     * Timestamp of the current build
     */
    timestamp: string;
};

export enum UiTheme {
    LIGHT = 'light',
    DARK = 'dark',
    SYSTEM = 'system'
}

export enum Locale {
    EN_GB = 'en-GB',
    NL_BE = 'nl-BE',
    FR_FR = 'fr-FR',
    ES_ES = 'es-ES',
    DE_DE = 'de-DE'
}

export enum FontSize {
    SMALLER = 'smaller',
    SMALL = 'small',
    DEFAULT = 'default',
    LARGE = 'large',
    LARGER = 'larger'
}

export type UpdateUiPreferencesCommand = {
    theme?: UiTheme;
    language?: Locale;
    fontSize?: FontSize;
    showShortcuts?: boolean;
    reduceMotion?: boolean;
    highContrast?: boolean;
};

export type ViewUiPreferencesResponse = {
    theme: UiTheme;
    language: Locale;
    fontSize: FontSize;
    showShortcuts: boolean;
    reduceMotion: boolean;
    highContrast: boolean;
};

export enum AnnouncementType {
    INFORMATIONAL = 'informational',
    URGENT = 'urgent'
}

export type CreateAnnouncementTranslationCommand = {
    title: string;
    content: {
        [key: string]: unknown;
    };
    language: Locale;
};

export type CreateAnnouncementCommand = {
    type: AnnouncementType;
    startDate: string;
    endDate?: string | null;
    translations: Array<CreateAnnouncementTranslationCommand>;
    salesOrganizations?: Array<SalesOrganization>;
};

export type CreateAnnouncementResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type MissingRequiredFieldError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'missing_required_field';
};

export type DateMustBeAfterError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'date_must_be_after';
};

export type FieldMustBeNullError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'field_must_be_null';
};

export type NotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'not_found';
};

export type UpdateAnnouncementTranslationCommand = {
    title?: string;
    content?: {
        [key: string]: unknown;
    };
    language: Locale;
};

export type SalesOrganization = {
    id: string;
    name: string;
};

export type UpdateAnnouncementCommand = {
    type?: AnnouncementType;
    startDate?: string;
    endDate?: string | null;
    translations?: Array<UpdateAnnouncementTranslationCommand>;
    salesOrganizations?: Array<SalesOrganization>;
};

export type UpdateAnnouncementResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type ViewAnnouncementTranslationResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    title: string;
    content: {
        [key: string]: unknown;
    };
    language: Locale;
};

export type DashboardAnnouncementIndexView = {
    uuid: string;
    type: AnnouncementType;
    startDate: string;
    endDate: string | null;
    translation: ViewAnnouncementTranslationResponse;
};

export type ViewDashboardAnnouncementIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<DashboardAnnouncementIndexView>;
    meta: PaginatedOffsetResponseMeta;
};

export type ViewDashboardAnnouncementResponse = {
    uuid: string;
    type: AnnouncementType;
    startDate: string;
    endDate: string | null;
    translation: ViewAnnouncementTranslationResponse;
};

export enum PublishStatus {
    SCHEDULED = 'scheduled',
    PUBLISHED = 'published',
    ARCHIVED = 'archived'
}

export type ViewNewsItemTranslationResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    title: string | null;
    content: {
        [key: string]: unknown;
    } | null;
    language: Locale;
};

export type ViewNewsItemAuthorResponse = {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
};

export type ViewAnnouncementResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    publishStatus: PublishStatus;
    type: AnnouncementType;
    startDate: string;
    endDate: string | null;
    translations: Array<ViewNewsItemTranslationResponse>;
    author: ViewNewsItemAuthorResponse;
    salesOrganizations: Array<SalesOrganization>;
};

export type ViewAnnouncementTranslationIndexResponse = {
    uuid: string;
    title: string;
    language: Locale;
};

export type ViewAnnouncementAuthorResponse = {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
};

export type AnnouncementIndexView = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    publishStatus: PublishStatus;
    type: AnnouncementType;
    startDate: string;
    endDate: string | null;
    translations: Array<ViewAnnouncementTranslationIndexResponse>;
    author: ViewAnnouncementAuthorResponse;
};

export type ViewAnnouncementIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<AnnouncementIndexView>;
    meta: PaginatedOffsetResponseMeta;
};

export type ContactResponse = {
    uuid: string;
    firstName: string;
    lastName: string;
    email: string;
};

export type ViewContactIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<ContactResponse>;
    meta: PaginatedOffsetResponseMeta;
};

export type CreateContactCommand = {
    firstName: string;
    lastName: string;
    email: string;
};

export type CreateContactResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type UpdateContactCommand = {
    firstName?: string;
    lastName?: string;
    email?: string;
};

export type ViewContainerTypeIndexFilterQuery = {
    customerId: string;
};

export type ContainerTypeResponse = {
    name: string;
};

export type ViewContainerTypeIndexResponse = {
    items: Array<ContainerTypeResponse>;
};

export type ViewContractLineIndexFilterQuery = {
    customerId?: string;
    wasteProducerId?: string;
    pickUpAddressIds?: Array<string>;
};

export type ViewContractLineIndexQueryKey = {
    skipToken: string;
};

export type ViewContractLineIndexPaginationQuery = {
    limit: number;
    key?: ViewContractLineIndexQueryKey | null;
};

export type ContractLineResponse = {
    contractLineId: string;
    contractNumber: string;
    contractItem: string;
    customerReference: string | null;
    wasteMaterial: string | null;
    materialNumber: string | null;
    treatmentCenterName: string | null;
    installationName: string | null;
    customerId: string | null;
    customerName: string | null;
    wasteProducerId: string | null;
    wasteProducerName: string | null;
    pickUpAddressId: string | null;
    pickUpAddressName: string | null;
    asn: string | null;
    tfs: boolean | null;
    isHazardous: boolean | null;
    packaged: string | null;
    tcNumber: string | null;
    materialAnalysis: string | null;
    ewcCode: string | null;
    endTreatmentCenterId: string | null;
    endTreatmentCenterName: string | null;
    remarks: string | null;
    processCode: string | null;
    esnNumber: string | null;
    deliveryInfo: string | null;
    materialType: string | null;
    packagingIndicator: string | null;
};

export type ViewContractLineIndexResponseMeta = {
    next: ViewContractLineIndexQueryKey | null;
};

export type ViewContractLineIndexResponse = {
    items: Array<ContractLineResponse>;
    meta: ViewContractLineIndexResponseMeta;
};

export type ViewWprContractLineIndexFilterQuery = {
    customerId: string;
    wasteProducerId?: string;
    pickUpAddressIds?: Array<string>;
};

export type WprContractLineResponse = {
    pickUpRequestUuid: string;
    contractLineId: string;
    contractNumber: string;
    contractItem: string;
    customerReference: string | null;
    wasteMaterial: string | null;
    materialNumber: string | null;
    treatmentCenterName: string | null;
    installationName: string | null;
    customerId: string | null;
    customerName: string | null;
    wasteProducerId: string | null;
    wasteProducerName: string | null;
    pickUpAddressId: string | null;
    pickUpAddressName: string | null;
    asn: string | null;
    tfs: boolean | null;
    isHazardous: boolean | null;
    packaged: string | null;
    tcNumber: string | null;
    materialAnalysis: string | null;
    ewcCode: string | null;
    endTreatmentCenterId: string | null;
    endTreatmentCenterName: string | null;
    remarks: string | null;
    processCode: string | null;
    esnNumber: string | null;
    deliveryInfo: string | null;
    materialType: string | null;
    packagingIndicator: string | null;
};

export type ViewWprContractLineIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<WprContractLineResponse>;
    meta: PaginatedOffsetResponseMeta;
};

export type ViewPackagingRequestContractLineIndexFilterQuery = {
    customerId: string;
    wasteProducerId: string;
    deliveryAddressIds?: Array<string>;
};

export type ViewPackagingRequestContractLineIndexQueryKey = {
    skipToken: string;
};

export type ViewPackagingRequestContractLineIndexPaginationQuery = {
    limit: number;
    key?: ViewPackagingRequestContractLineIndexQueryKey | null;
};

export type PackagingRequestContractLineResponse = {
    contractLineId: string;
    contractNumber: string;
    contractItem: string;
    materialNumber: string | null;
    wasteMaterial: string | null;
    isSales: boolean | null;
    imageUrl: string | null;
};

export type ViewPackagingRequestContractLineIndexResponseMeta = {
    next: ViewContractLineIndexQueryKey | null;
};

export type ViewPackagingRequestContractLineIndexResponse = {
    items: Array<PackagingRequestContractLineResponse>;
    meta: ViewPackagingRequestContractLineIndexResponseMeta;
};

export type ContractLineNotAccessibleErrorMeta = {
    contractNumber: string;
    contractLineNumber: string;
    tcNumber: string | null;
};

export type ContractLineNotAccessibleError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'contract_line_not_accessible';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: ContractLineNotAccessibleErrorMeta;
};

export type ContractLinesSelection = {
    contractNumber: string;
    contractItem: string;
};

export type GenerateContractLinesPdfCommand = {
    selection: Array<ContractLinesSelection>;
};

export type GenerateContractLinesPdfResponse = {
    /**
     * Number of pages in the PDF
     */
    pageCount: number;
    /**
     * File size in bytes
     */
    size: number;
    /**
     * File name
     */
    name: string;
    /**
     * File mime type
     */
    mimeType: string;
    /**
     * Base64 encoded PDF content
     */
    content: string;
};

export type DocumentNotFoundErrorMeta = {
    customerUuid: string;
    documentId: string;
};

export type DocumentNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'document_not_found';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: DocumentNotFoundErrorMeta;
};

export type DownloadDocumentCommand = {
    documentId: string;
    customerUuid: string;
};

export type ForbiddenError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '403';
    code: 'forbidden';
};

export type ViewDocumentIndexPaginationQuery = {
    limit: number;
    key?: string | null;
};

export enum SharepointDocumentViewName {
    MASTERTABLE = 'mastertable',
    TFS = 'tfs',
    QUOTATION = 'quotation',
    MEETINGS = 'meetings',
    MANUAL = 'manual',
    BSC = 'bsc',
    CONTRACT = 'contract',
    TRANSPORT = 'transport'
}

export enum SharepointDocumentStatus {
    ACTIVE = 'Active',
    ARCHIVED = 'Archived'
}

export type ViewDocumentIndexFilterQuery = {
    viewName: SharepointDocumentViewName;
    customerUuid: string;
    wasteProducerIds: Array<string>;
    status?: SharepointDocumentStatus;
    year?: string;
};

export enum SharepointDocumentType {
}

export type ViewDocumentIndexItemResponse = {
    id: string;
    name: string;
    actionAt: string | null;
    applicableFrom: string | null;
    applicableTill: string | null;
    tfsType: SharepointDocumentType | null;
    wasteProducer: string;
    status: SharepointDocumentStatus | null;
};

export type ViewDocumentIndexResponseMeta = {
    next: string | null;
};

export type ViewDocumentIndexResponse = {
    items: Array<ViewDocumentIndexItemResponse>;
    meta: ViewDocumentIndexResponseMeta;
};

export type ViewUserSiteIndexWasteProducerResponse = {
    id: number;
    name: string;
};

export type ViewUserSiteIndexResponse = {
    uuid: string;
    name: string;
    wasteProducers: Array<ViewUserSiteIndexWasteProducerResponse>;
};

export enum DynamicColumnNames {
    CONTRACT_NUMBER = 'contractNumber',
    CONTRACT_ITEM = 'contractItem',
    CUSTOMER_REFERENCE = 'customerReference',
    WASTE_MATERIAL = 'wasteMaterial',
    MATERIAL_NUMBER = 'materialNumber',
    TREATMENT_CENTER_NAME = 'treatmentCenterName',
    INSTALLATION_NAME = 'installationName',
    CUSTOMER_ID = 'customerId',
    CUSTOMER_NAME = 'customerName',
    WASTE_PRODUCER_ID = 'wasteProducerId',
    WASTE_PRODUCER_NAME = 'wasteProducerName',
    PICK_UP_ADDRESS_ID = 'pickUpAddressId',
    PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
    ASN = 'asn',
    TFS = 'tfs',
    IS_HAZARDOUS = 'isHazardous',
    PACKAGED = 'packaged',
    TC_NUMBER = 'tcNumber',
    MATERIAL_ANALYSIS = 'materialAnalysis',
    EWC_CODE = 'ewcCode',
    END_TREATMENT_CENTER_ID = 'endTreatmentCenterId',
    END_TREATMENT_CENTER_NAME = 'endTreatmentCenterName',
    REMARKS = 'remarks',
    PROCESS_CODE = 'processCode',
    ESN_NUMBER = 'esnNumber',
    DELIVERY_INFO = 'deliveryInfo',
    MATERIAL_TYPE = 'materialType',
    PACKAGING_INDICATOR = 'packagingIndicator',
    INVOICE_NUMBER = 'invoiceNumber',
    STATUS = 'status',
    PAYER_ID = 'payerId',
    PAYER_NAME = 'payerName',
    ISSUED_ON = 'issuedOn',
    FIRST_REMINDER_MAIL_STATUS = 'firstReminderMailStatus',
    FIRST_REMINDER_ON = 'firstReminderOn',
    SECOND_REMINDER_MAIL_STATUS = 'secondReminderMailStatus',
    SECOND_REMINDER_ON = 'secondReminderOn',
    THIRD_REMINDER_STATUS = 'thirdReminderStatus',
    AUTO_APPROVED_ON = 'autoApprovedOn',
    NET_AMOUNT = 'netAmount',
    VAT_AMOUNT = 'vatAmount',
    CURRENCY = 'currency',
    ESTIMATED_WEIGHT_OR_VOLUME_VALUE = 'estimatedWeightOrVolumeValue',
    ESTIMATED_WEIGHT_OR_VOLUME_UNIT = 'estimatedWeightOrVolumeUnit',
    PACKAGING_TYPE = 'packagingType',
    QUANTITY_PACKAGES = 'quantityPackages',
    QUANTITY_LABELS = 'quantityLabels',
    QUANTITY_PALLETS = 'quantityPallets',
    UN_NUMBER = 'unNumber',
    PACKING_GROUP = 'packingGroup',
    DANGER_LABEL1 = 'dangerLabel1',
    DANGER_LABEL2 = 'dangerLabel2',
    DANGER_LABEL3 = 'dangerLabel3',
    COST_CENTER = 'costCenter',
    PO_NUMBER = 'poNumber',
    CONTAINER_TYPE = 'containerType',
    CONTAINER_VOLUME_SIZE = 'containerVolumeSize',
    CONTAINER_NUMBER = 'containerNumber',
    CONTAINER_TRANSPORT_TYPE = 'containerTransportType',
    IS_CONTAINER_COVERED = 'isContainerCovered',
    TANKER_TYPE = 'tankerType',
    TOTAL_QUANTITY_PALLETS = 'totalQuantityPallets',
    IS_RETURN_PACKAGING = 'isReturnPackaging',
    PACKAGING_REMARK = 'packagingRemark',
    RECONCILIATION_NUMBER = 'reconciliationNumber',
    HAZARD_INDUCERS = 'hazardInducers',
    QUANTITY_CONTAINERS = 'quantityContainers',
    TFS_NUMBER = 'tfsNumber',
    SERIAL_NUMBER = 'serialNumber',
    DUE_ON = 'dueOn',
    TYPE = 'type',
    ACCOUNT_DOCUMENT_NUMBER = 'accountDocumentNumber',
    ACCOUNT_MANAGER_NAME = 'accountManagerName',
    COMPANY_NAME = 'companyName',
    REQUEST_NUMBER = 'requestNumber',
    TRANSPORT_MODE = 'transportMode',
    DATE_OF_REQUEST = 'dateOfRequest',
    ACCOUNT_MANAGER = 'accountManager',
    IS_TRANSPORT_BY_INDAVER = 'isTransportByIndaver',
    REQUESTED_START_DATE = 'requestedStartDate',
    REQUESTED_END_DATE = 'requestedEndDate',
    CONFIRMED_TRANSPORT_DATE = 'confirmedTransportDate',
    SALES_ORDER = 'salesOrder',
    NAME_OF_APPLICANT = 'nameOfApplicant',
    ORDER_NUMBER = 'orderNumber',
    NAME_INSTALLATION = 'nameInstallation',
    DISPOSAL_CERTIFICATE_NUMBER = 'disposalCertificateNumber',
    EWC = 'ewc',
    INQUIRY_NUMBER = 'inquiryNumber',
    WASTE_STREAM_NAME = 'wasteStreamName',
    DATE = 'date',
    CONTRACT_ID = 'contractId',
    SALES_ORGANISATION_ID = 'salesOrganisationId',
    SALES_ORGANISATION_NAME = 'salesOrganisationName',
    REQUESTOR_NAME = 'requestorName'
}

export type DynamicTableColumnIndexView = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    name: DynamicColumnNames;
    isHidable: boolean;
    applicableFields: Array<string>;
    filterableField: string | null;
    sortableFields: Array<string>;
    searchableFields: Array<string>;
};

export type DynamicTableIndexColumnResponse = {
    items: Array<DynamicTableColumnIndexView>;
};

export type ColumnNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'column_not_found';
};

export type ColumnNotFilterableError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'column_not_filterable';
};

export type ColumnNotSortableError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'column_not_sortable';
};

export type DuplicateColumnError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'duplicate_column';
};

export type DynamicTableViewFilterCommand = {
    columnUuid: string;
    value: string | Array<string>;
};

export type DynamicTableViewSortCommand = {
    columnUuid: string;
    direction: SortDirection;
};

export type DynamicTableViewVisibleColumnsCommand = {
    columnUuid: string;
};

export type CreateDynamicTableViewCommand = {
    viewName: string;
    /**
     * Field only allowed for admins
     */
    isGlobal?: boolean;
    /**
     * Field only allowed for admins
     */
    isGlobalDefault?: boolean;
    isDefault?: boolean;
    filters: Array<DynamicTableViewFilterCommand>;
    sorts: Array<DynamicTableViewSortCommand>;
    visibleColumns: Array<DynamicTableViewVisibleColumnsCommand>;
};

export type CreateDynamicTableViewResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type VisibilityConfigurationResponse = {
    uuid: string;
    order: number;
};

export type SortConfigurationResponse = {
    uuid: string;
    order: number;
    direction: SortDirection;
};

export type FilterConfigurationResponse = {
    uuid: string;
    value: string | Array<string>;
};

export type ViewDefaultDynamicTableViewResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    isGlobal: boolean;
    isDefaultGlobal: boolean;
    isUserDefault: boolean;
    visibleColumns: Array<VisibilityConfigurationResponse>;
    sorts: Array<SortConfigurationResponse>;
    filters: Array<FilterConfigurationResponse>;
};

export type UpdateDynamicTableViewCommand = {
    viewName?: string;
    isGlobal?: boolean;
    isGlobalDefault?: boolean;
    isDefault?: boolean;
    filters?: Array<DynamicTableViewFilterCommand>;
    sorts?: Array<DynamicTableViewSortCommand>;
    visibleColumns?: Array<DynamicTableViewVisibleColumnsCommand>;
};

export type UpdateDynamicTableViewResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type DynamicTableViewResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    isGlobal: boolean;
    isDefaultGlobal: boolean;
    isUserDefault: boolean;
    visibleColumns: Array<VisibilityConfigurationResponse>;
    sorts: Array<SortConfigurationResponse>;
    filters: Array<FilterConfigurationResponse>;
};

export type DynamicTableViewIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<DynamicTableViewResponse>;
    meta: PaginatedOffsetResponseMeta;
};

export type GlobalDefaultViewNotDeletable = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'global_default_view_not_deletable';
};

export type LastGlobalViewNotDeletable = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'last_global_view_not_deletable';
};

export type EwcCodeResponse = {
    /**
     * Code existing out of 1, 2 or 3 levels
     */
    code: string;
    description: string;
};

export type ViewEwcCodeIndexResponse = {
    items: Array<EwcCodeResponse>;
};

export enum InvoiceStatus {
    OVERDUE = 'overdue',
    OUTSTANDING = 'outstanding',
    CLEARED = 'cleared'
}

export type ViewInvoiceIndexFilterQuery = {
    statuses: Array<InvoiceStatus>;
};

export type ViewInvoiceIndexPaginationQueryKey = {
    skipToken: string;
};

export type ViewInvoiceIndexQueryKey = {
    limit: number;
    key?: ViewInvoiceIndexPaginationQueryKey | null;
};

export enum InvoiceType {
    CREDIT_NOTE = 'credit_note',
    INVOICE = 'invoice',
    REQUEST_FOR_INVOICE = 'request_for_invoice',
    CANCELLATION = 'cancellation',
    UNKNOWN = 'unknown'
}

export type InvoiceResponse = {
    invoiceNumber: string;
    status: InvoiceStatus;
    issuedOn: string;
    dueOn: string | null;
    customerName: string;
    customerReference: string | null;
    type: InvoiceType;
    payerId: string;
    payerName: string;
    netAmount: string;
    vatAmount: string;
    currency: string;
    accountDocumentNumber: string | null;
    accountManagerName: string | null;
    companyName: string;
};

export type ViewInvoiceIndexResponseMeta = {
    next: ViewInvoiceIndexPaginationQueryKey | null;
};

export type ViewInvoiceIndexResponse = {
    items: Array<InvoiceResponse>;
    meta: ViewInvoiceIndexResponseMeta;
};

export enum DraftInvoiceStatus {
    APPROVED_BY_CUSTOMER = 'approved_by_customer',
    AUTO_APPROVED = 'auto_approved',
    INTERNAL_APPROVED = 'internal_approved',
    REJECTED_BY_CUSTOMER = 'rejected_by_customer',
    REJECTED_BY_INDAVER = 'rejected_by_indaver',
    TO_BE_APPROVED_BY_CUSTOMER = 'to_be_approved_by_customer',
    TO_BE_APPROVED_BY_INDAVER = 'to_be_approved_by_indaver'
}

export type ViewDraftInvoiceIndexFilterQuery = {
    statuses: Array<DraftInvoiceStatus>;
};

export type ViewDraftInvoiceIndexPaginationQueryKey = {
    skipToken: string;
};

export type ViewDraftInvoiceIndexQueryKey = {
    limit: number;
    key?: ViewDraftInvoiceIndexPaginationQueryKey | null;
};

export enum MailStatus {
    SENT = 'sent',
    NOT_SENT = 'not_sent'
}

export type DraftInvoiceResponse = {
    invoiceNumber: string;
    status: DraftInvoiceStatus;
    payerId: string;
    payerName: string;
    issuedOn: string;
    firstReminderMailStatus: MailStatus;
    firstReminderOn: string | null;
    secondReminderMailStatus: MailStatus;
    secondReminderOn: string | null;
    thirdReminderStatus: MailStatus;
    autoApprovedOn: string | null;
    netAmount: string;
    vatAmount: string;
    currency: string;
};

export type ViewDraftInvoiceIndexResponse = {
    items: Array<DraftInvoiceResponse>;
    meta: ViewInvoiceIndexResponseMeta;
};

export type NonApproveOrRejectableDraftInvoiceError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'non_approve_or_rejectable_draft_invoice';
};

export type InvoiceNotFoundErrorMeta = {
    invoiceNumber: string;
};

export type InvoiceNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'invoice_not_found';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: InvoiceNotFoundErrorMeta;
};

export type ApproveDraftInvoiceCommand = {
    poNumber: string | null;
    remark: string | null;
};

export type RejectDraftInvoiceCommand = {
    remark: string;
};

export type AlreadySubscribedError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'already_subscribed';
};

export type OptInAlreadyRequestedError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'opt_in_already_requested';
};

export type SubscribeToNewsletterCommand = {
    email: string;
};

export type CreateNewsItemTranslationCommand = {
    title: string;
    content: {
        [key: string]: unknown;
    };
    language: Locale;
};

export type CreateNewsItemCommand = {
    startDate: string;
    endDate?: string | null;
    imageUuid: string;
    newsItemTranslations: Array<CreateNewsItemTranslationCommand>;
    videoIFrame?: string | null;
    salesOrganizations?: Array<SalesOrganization>;
};

export type CreateNewsItemResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type NewsItemTranslationExistsError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'news-item-translation-exists';
};

export type NoStartDateOrEndDateExpectedError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'no-start-date-or-end-date-expected';
};

export type UpdateNewsItemTranslationCommand = {
    title?: string;
    content?: {
        [key: string]: unknown;
    };
    language: Locale;
};

export type UpdateNewsItemCommand = {
    startDate?: string;
    endDate?: string | null;
    imageUuid?: string;
    newsItemTranslations?: Array<UpdateNewsItemTranslationCommand>;
    videoIFrame?: string | null;
    salesOrganizations?: Array<SalesOrganization>;
};

export type UpdateNewsItemResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type FileResponse = {
    uuid: string;
    name: string;
    mimeType: MimeType | null;
    url: string;
};

export type ViewNewsItemResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    publishStatus: PublishStatus;
    startDate: string;
    endDate: string | null;
    translations: Array<ViewNewsItemTranslationResponse>;
    image: FileResponse;
    videoIFrame: string | null;
    author: ViewNewsItemAuthorResponse;
    salesOrganizations: Array<SalesOrganization>;
};

export type ViewNewsItemTranslationIndexResponse = {
    uuid: string;
    title: string | null;
    language: Locale;
};

export type NewsItemIndexView = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    publishStatus: PublishStatus;
    startDate: string;
    endDate: string | null;
    translations: Array<ViewNewsItemTranslationIndexResponse>;
    author: ViewNewsItemAuthorResponse;
};

export type ViewNewsIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<NewsItemIndexView>;
    meta: PaginatedOffsetResponseMeta;
};

export type ViewDashboardNewItemFilterQuery = {
    excludeNewsItemUuids?: Array<string>;
};

export type DashboardNewsItemIndexView = {
    uuid: string;
    endDate: string | null;
    startDate: string | null;
    translation: ViewNewsItemTranslationResponse;
    image: FileResponse | null;
    videoIFrame: string | null;
};

export type ViewDashboardNewsIndexResponse = {
    /**
     * The items for the current page
     */
    items: Array<DashboardNewsItemIndexView>;
    meta: PaginatedOffsetResponseMeta;
};

export type ViewDashboardNewsItemResponse = {
    uuid: string;
    endDate: string | null;
    startDate: string | null;
    translation: ViewNewsItemTranslationResponse;
    image: FileResponse | null;
    videoIFrame: string | null;
};

export type CreatePackagingRequestCommand = {
    [key: string]: unknown;
};

export type CreatePackagingRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type WasteProducerResponse = {
    id: string;
    name: string;
    address: string | unknown | null;
};

export type PickUpAddressResponse = {
    id: string;
    name: string;
    address: string | unknown | null;
};

export type PackagingRequestMaterialResponse = {
    contractLineId: string;
    materialNumber: string | null;
    wasteMaterial: string | null;
    isSales: boolean | null;
    contractNumber: string;
    contractItem: string;
    poNumber: string | null;
    costCenter: string | null;
    quantity: number;
    /**
     * When false, the contract line could not be retrieved from SAP. This occurs when the contract line is expired or rejected.
     */
    contractLineAccessible?: boolean;
};

export type ContactTypeResponse = {
    firstName: string;
    lastName: string;
    email: string;
};

export type ViewPackagingRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    customer: CustomerResponse | null;
    wasteProducer: WasteProducerResponse | null;
    deliveryAddress: PickUpAddressResponse | null;
    packagingRequestMaterials: Array<PackagingRequestMaterialResponse>;
    startDate: string | null;
    endDate: string | null;
    remarks: string | null;
    sendCopyToContacts: Array<ContactTypeResponse>;
};

export type CustomerNotAccessibleError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'customer_not_accessible';
};

export type WasteProducerNotAccessibleError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'waste_producer_not_accessible';
};

export type PickUpAddressNotAccessibleError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'pick_up_address_not_accessible';
};

export type PackagingRequestMaterialCommand = {
    contractLineId: string;
    costCenter: string | null;
    poNumber: string | null;
    contractNumber: string;
    contractItem: string;
    wasteMaterial: string | null;
    materialNumber: string;
    isSales: boolean | null;
    quantity: number;
};

export type Contact = {
    email: string;
    firstName: string;
    lastName: string;
};

export type UpdatePackagingRequestCommand = {
    customerId?: string | null;
    wasteProducerId?: string | null;
    deliveryAddressId?: string | null;
    packagingRequestMaterials?: Array<PackagingRequestMaterialCommand>;
    startDate?: string | null;
    endDate?: string | null;
    remarks?: string | null;
    sendCopyToContacts?: Array<Contact>;
};

export type UpdatePackagingRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type ContractLineNotOfCustomerErrorMeta = {
    customerId: string;
    contractNumber: string;
    contractLineNumber: string;
    tcNumber: string | null;
};

export type ContractLineNotOfCustomerError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'contract_line_not_of_customer';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: ContractLineNotOfCustomerErrorMeta;
};

export type ContractLineNotOfPickUpAddressesErrorMeta = {
    pickUpAddressIds: Array<string>;
    contractNumber: string;
    contractLineNumber: string;
    tcNumber: string | null;
};

export type ContractLineNotOfPickUpAddressesError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'contract_line_not_of_pick_up_addresses';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: ContractLineNotOfPickUpAddressesErrorMeta;
};

export type UpdateOnlyPackagingRequestError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'update_only_packaging_request';
};

export type PackagingRequestAlreadySubmitted = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'packaging_request_already_submitted';
};

export type SubmitPackagingRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    submittedOn: string;
    requestNumber: string;
};

export type BulkDeletePackagingRequestCommand = {
    packagingRequestUuids: Array<string>;
};

export type PickUpRequestNotFoundErrorMeta = {
    uuid?: string;
    requestNumber?: string;
};

export type PickUpRequestNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'pick_up_request_not_found';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: PickUpRequestNotFoundErrorMeta;
};

export type CopyNonSubmittedPickUpRequestError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'copy_non_submitted_pick_up_request';
};

export type CopyPackagingRequestSapResponse = {
    uuid: string;
};

export type ViewPackagingTypeIndexFilterQuery = {
    customerId: string;
};

export type PackagingTypeResponse = {
    name: string;
};

export type ViewPackagingTypeIndexResponse = {
    items: Array<PackagingTypeResponse>;
};

export type ViewPickUpAddressIndexFilterQuery = {
    customerId: string;
};

export type ViewPickUpAddressIndexQueryKey = {
    skipToken: string;
};

export type ViewPickUpAddressIndexPaginationQuery = {
    limit: number;
    key?: ViewPickUpAddressIndexQueryKey | null;
};

export type ViewPickUpAddressIndexResponseMeta = {
    next: ViewPickUpAddressIndexQueryKey | null;
};

export type ViewPickUpAddressIndexResponse = {
    items: Array<PickUpAddressResponse>;
    meta: ViewPickUpAddressIndexResponseMeta;
};

export type ViewSuggestedPickUpAddressesFilterQuery = {
    customerId: string;
    requestType: RequestType;
};

export type ViewSuggestedPickUpAddressesResponse = {
    items: Array<PickUpAddressResponse>;
};

export type GetIsPoNumberAndCostCenterRequiredFilterQuery = {
    customerId: string;
};

export type GetIsPoNumberAndCostCenterRequiredResponse = {
    isPoNumberRequired: boolean;
    isCostCenterRequired: boolean;
};

export type ViewPickUpRequestIndexPaginationQuery = {
    limit: number;
    key?: unknown | null;
};

export enum PickUpRequestStatus {
    DRAFT = 'draft',
    PENDING = 'pending',
    CONFIRMED = 'confirmed',
    CANCELLED = 'cancelled',
    INDASCAN_DRAFT = 'indascan_draft'
}

export type ViewPickUpRequestIndexFilterQuery = {
    statuses: Array<PickUpRequestStatus>;
};

export enum TransportMode {
    PACKAGING_REQUEST_ORDER = 'packaging-request-order',
    PACKAGED_CURTAIN_SIDER_TRUCK = 'packaged-curtain-sider-truck',
    BULK_SKIPS_CONTAINER = 'bulk-skips-container',
    BULK_VACUUM_TANKERS_ROAD_TANKERS = 'bulk-vacuum-tankers-road-tankers',
    BULK_ISO_TANK = 'bulk-iso-tank'
}

export type PickUpRequestResponse = {
    /**
     * UUID is null when retrieved from SAP
     */
    uuid: string | null;
    requestNumber: string | null;
    status: PickUpRequestStatus;
    wasteMaterial: string | null;
    customerId: string | null;
    customerName: string | null;
    wasteProducerId: string | null;
    wasteProducerName: string | null;
    pickUpAddressId: string | null;
    pickUpAddressName: string | null;
    customerReference: string | null;
    contractNumber: string | null;
    contractItem: string | null;
    transportMode: TransportMode | null;
    dateOfRequest: string | null;
    treatmentCenterName: string | null;
    accountManager: string | null;
    costCenter: string | null;
    isTransportByIndaver: boolean | null;
    requestedStartDate: string | null;
    requestedEndDate: string | null;
    confirmedTransportDate: string | null;
    salesOrder: string | null;
    isHazardous: boolean | null;
    nameOfApplicant: string | null;
    orderNumber: string | null;
    containerNumber: string | null;
    materialAnalysis: string | null;
    deliveryInfo: string | null;
    nameInstallation: string | null;
    disposalCertificateNumber: string | null;
    ewc: string | null;
    tfsNumber: string | null;
};

export type ViewPickUpRequestIndexResponseMeta = {
    next: unknown | null;
};

export type ViewPickUpRequestIndexResponse = {
    items: Array<PickUpRequestResponse>;
    meta: ViewPickUpRequestIndexResponseMeta;
};

export type CreatePickUpRequestCommand = {
    [key: string]: unknown;
};

export type CreatePickUpRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export enum PickUpTransportMode {
    PACKAGED_CURTAIN_SIDER_TRUCK = 'packaged-curtain-sider-truck',
    BULK_SKIPS_CONTAINER = 'bulk-skips-container',
    BULK_VACUUM_TANKERS_ROAD_TANKERS = 'bulk-vacuum-tankers-road-tankers',
    BULK_ISO_TANK = 'bulk-iso-tank'
}

export enum WasteMeasurementUnit {
    KG = 'kg',
    M3 = 'm3',
    PC = 'pc',
    TO = 'to',
    YD3 = 'yd3'
}

export type MaterialResponse = {
    contractLineId: string;
    contractNumber: string;
    contractItem: string;
    tcNumber: string | null;
    customerReference: string | null;
    wasteMaterial: string | null;
    materialNumber: string | null;
    treatmentCenterName: string | null;
    installationName: string | null;
    customerId: string | null;
    customerName: string | null;
    wasteProducerId: string | null;
    wasteProducerName: string | null;
    pickUpAddressId: string | null;
    pickUpAddressName: string | null;
    asn: string | null;
    tfs: boolean | null;
    isHazardous: boolean | null;
    packaged: string | null;
    materialAnalysis: string | null;
    ewcCode: string | null;
    endTreatmentCenterId: string | null;
    endTreatmentCenterName: string | null;
    remarks: string | null;
    processCode: string | null;
    esnNumber: string | null;
    deliveryInfo: string | null;
    materialType: string | null;
    packagingIndicator: string | null;
    estimatedWeightOrVolumeValue: number | null;
    estimatedWeightOrVolumeUnit: WasteMeasurementUnit | null;
    costCenter: string | null;
    poNumber: string | null;
    unNumber: string | null;
    unNumberDescription: string | null;
    adrClass: string | null;
    packingGroup: string | null;
    dangerLabel1: string | null;
    dangerLabel2: string | null;
    dangerLabel3: string | null;
    packagingType: string | null;
    quantityPackages: number | null;
    quantityLabels: number | null;
    quantityPallets: number | null;
    containerType: string | null;
    containerVolumeSize: string | null;
    containerNumber: string | null;
    containerTransportType: string | null;
    isContainerCovered: boolean | null;
    tankerType: string | null;
    position: string | null;
    /**
     * When false, the contract line could not be retrieved from SAP. This occurs when the contract line is expired or rejected.
     */
    contractLineAccessible: boolean | null;
    unNumberHazardous: boolean | null;
    reconciliationNumber: string | null;
    hazardInducers: string | null;
    quantityContainers: number | null;
    tfsNumber: string | null;
    serialNumber: string | null;
};

export type FileLinkResponse = {
    /**
     * Null when file from external resource
     */
    uuid: string | null;
    name: string;
    mimeType: MimeType | null;
    url: string | null;
    order: number | null;
};

export type ViewPickUpRequestResponse = {
    status: PickUpRequestStatus;
    customer: CustomerResponse | null;
    wasteProducer: WasteProducerResponse | null;
    pickUpAddresses: Array<PickUpAddressResponse>;
    transportMode: PickUpTransportMode | null;
    isTransportByIndaver: boolean | null;
    startDate: string | null;
    endDate: string | null;
    remarks: string | null;
    totalQuantityPallets: number | null;
    isReturnPackaging: boolean | null;
    packagingRemark: string | null;
    sendCopyToContacts: Array<ContactTypeResponse>;
    materials: Array<MaterialResponse>;
    packagingRequestMaterials: Array<PackagingRequestMaterialResponse>;
    additionalFiles: Array<FileLinkResponse>;
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type ViewWasteProducerIndexFilterQuery = {
    customerId: string;
};

export type ViewWasteProducerIndexQueryKey = {
    skipToken: string;
};

export type ViewWasteProducerIndexPaginationQuery = {
    limit: number;
    key?: ViewWasteProducerIndexQueryKey | null;
};

export type ViewWasteProducerIndexResponseMeta = {
    next: ViewWasteProducerIndexQueryKey | null;
};

export type ViewWasteProducerIndexResponse = {
    items: Array<WasteProducerResponse>;
    meta: ViewWasteProducerIndexResponseMeta;
};

export type ViewSuggestedWasteProducersFilterQuery = {
    customerId: string;
    requestType: RequestType;
};

export type ViewSuggestedWasteProducersResponse = {
    items: Array<WasteProducerResponse>;
};

export type MissingEwcLevelsError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'missing_ewc_levels';
};

export type CreateFileLinkCommand = {
    fileUuid: string;
    order: number;
};

export enum PackingGroup {
    NOT_APPLICABLE = 'not-applicable',
    ONE = 'one',
    TWO = 'two',
    THREE = 'three'
}

export type PickUpRequestMaterialCommand = {
    contractLineId: string;
    contractNumber: string;
    contractItem: string;
    tcNumber?: string | null;
    pickUpAddressId?: string | null;
    materialNumber?: string | null;
    customerReference?: string | null;
    isHazardous?: boolean | null;
    wasteMaterial?: string | null;
    ewcCode?: string | null;
    asn?: string | null;
    tfs?: boolean | null;
    unNumberHazardous?: boolean | null;
    estimatedWeightOrVolumeValue?: number | null;
    estimatedWeightOrVolumeUnit?: WasteMeasurementUnit | null;
    costCenter?: string | null;
    poNumber?: string | null;
    unNumber?: string | null;
    unNumberDescription?: string | null;
    adrClass?: string | null;
    packingGroup?: PackingGroup | null;
    dangerLabel1?: string | null;
    dangerLabel2?: string | null;
    dangerLabel3?: string | null;
    packagingType?: string | null;
    quantityPackages?: number | null;
    quantityLabels?: number | null;
    quantityPallets?: number | null;
    containerType?: string | null;
    containerVolumeSize?: string | null;
    containerNumber?: string | null;
    /**
     * Transport type id
     */
    containerTransportType?: string | null;
    /**
     * Tanker type id
     */
    tankerType?: string | null;
    isContainerCovered?: boolean | null;
    reconciliationNumber?: string | null;
    hazardInducers?: string | null;
    quantityContainers?: number | null;
    tfsNumber?: string | null;
    serialNumber?: string | null;
};

export type UpdatePickUpRequestCommand = {
    customerId?: string | null;
    wasteProducerId?: string | null;
    pickUpAddressIds?: Array<string>;
    transportMode?: PickUpTransportMode | null;
    isTransportByIndaver?: boolean | null;
    totalQuantityPallets?: number | null;
    isReturnPackaging?: boolean | null;
    startDate?: string | null;
    endDate?: string | null;
    startTime?: string | null;
    packagingRemark?: string | null;
    remarks?: string | null;
    additionalFiles?: Array<CreateFileLinkCommand>;
    sendCopyToContacts?: Array<Contact>;
    materials?: Array<PickUpRequestMaterialCommand>;
    packagingRequestMaterials?: Array<PackagingRequestMaterialCommand>;
};

export type UpdatePickUpRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type PickUpRequestAlreadySubmitted = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'pick_up_request_already_submitted';
};

export type SubmitPickUpRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    submittedOn: string;
    requestNumber: string;
};

export type BulkDeletePickUpRequestCommand = {
    pickUpRequestUuids: Array<string>;
};

export type ViewPickUpRequestSapResponse = {
    status: PickUpRequestStatus;
    customer: CustomerResponse | null;
    wasteProducer: WasteProducerResponse | null;
    pickUpAddresses: Array<PickUpAddressResponse>;
    transportMode: PickUpTransportMode | null;
    isTransportByIndaver: boolean | null;
    startDate: string | null;
    endDate: string | null;
    remarks: string | null;
    totalQuantityPallets: number | null;
    isReturnPackaging: boolean | null;
    packagingRemark: string | null;
    sendCopyToContacts: Array<ContactTypeResponse>;
    materials: Array<MaterialResponse>;
    packagingRequestMaterials: Array<PackagingRequestMaterialResponse>;
    additionalFiles: Array<FileLinkResponse>;
    requestNumber: string | null;
    createdBy: string | null;
    confirmedDate: string | null;
};

export type InvalidPickUpRequestCopyErrorMeta = {
    requestNumber: string;
};

export type InvalidPickUpRequestCopyError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'invalid_pick_up_request_copy';
    /**
     * a meta object containing non-standard meta-information about the error
     */
    meta: InvalidPickUpRequestCopyErrorMeta;
};

export type CopyPickUpRequestSapResponse = {
    uuid: string;
};

export type InvalidUpdateSapPickUpRequestError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'invalid_update_sap_pick_up_request';
};

export type PickUpRequestContractLineNotFoundError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '404';
    code: 'pick_up_request_contract_line_not_found';
};

export type UpdatePickUpRequestMaterialSapCommand = {
    costCenter?: string | null;
    poNumber?: string | null;
    position: string;
};

export type UpdatePickUpRequestPackagingMaterialSapCommand = {
    contractLineId: string;
    costCenter: string | null;
    poNumber: string | null;
    contractNumber: string;
    contractItem: string;
    wasteMaterial: string | null;
    materialNumber: string;
    isSales: boolean | null;
    quantity: number;
    position: string | null;
};

export type UpdatePickUpRequestPackagingSapCommand = {
    customerId?: string | null;
    wasteProducerId?: string | null;
    deliveryAddressId?: string | null;
    packagingRequestMaterials?: Array<UpdatePickUpRequestPackagingMaterialSapCommand>;
    startDate?: string | null;
    endDate?: string | null;
};

export type UpdatePickUpRequestSapCommand = {
    contacts?: Array<Contact>;
    materials?: Array<UpdatePickUpRequestMaterialSapCommand>;
    packagingRequest?: UpdatePickUpRequestPackagingSapCommand;
    remarks?: string | null;
    additionalFiles?: Array<CreateFileLinkCommand>;
};

export type InvalidIndascanSubmitStatusError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'invalid_indascan_submit_status';
};

export type TankerTypeResponse = {
    id: string;
    name: string;
};

export type ViewTankerTypeIndexResponse = {
    items: Array<TankerTypeResponse>;
};

export type TransportTypeResponse = {
    id: string;
    name: string;
};

export type ViewTransportTypeIndexResponse = {
    items: Array<TransportTypeResponse>;
};

export type ViewUnNumberIndexQueryKey = {
    number: string;
};

export type ViewUnNumberIndexPaginationQuery = {
    limit: number;
    key?: ViewUnNumberIndexQueryKey | null;
};

export type UnNumberResponse = {
    number: string;
    description: string | null;
    packingGroup: string | null;
    dangerLabel1: string | null;
    dangerLabel2: string | null;
    dangerLabel3: string | null;
    isHazardous: boolean | null;
};

export type ViewUnNumberIndexResponseMeta = {
    next: ViewUnNumberIndexQueryKey | null;
};

export type ViewUnNumberIndexResponse = {
    items: Array<UnNumberResponse>;
    meta: ViewUnNumberIndexResponseMeta;
};

export type ViewUnNumberIndexForPickUpRequestFilterQuery = {
    contractNumber: string;
    contractItem: string;
    tcNumber?: string;
};

export type ViewUnNumberIndexForPickUpRequestResponse = {
    items: Array<UnNumberResponse>;
};

export type CreateWasteInquiryCommand = {
    [key: string]: unknown;
};

export type CreateWasteInquiryResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export enum WasteInquiryStatus {
    DRAFT = 'draft',
    NEW = 'new',
    IN_PROGRESS = 'in_progress',
    CONFORMITY_CONFIRMED = 'conformity_confirmed',
    SOLUTION_DEFINED = 'solution_defined',
    OFFER_SENT = 'offer_sent',
    OFFER_APPROVED = 'offer_approved',
    COMPLETED = 'completed',
    REJECTED = 'rejected'
}

export enum StateOfMatter {
    GASEOUS = 'gaseous',
    POWDER = 'powder',
    SLUDGY = 'sludgy',
    SOLID = 'solid',
    LIQUID = 'liquid',
    VISCOUS = 'viscous',
    LIQUID_WITH_SOLIDS = 'liquid-with-solids',
    NO_DATA_AVAILABLE = 'no-data-available'
}

export enum WastePackagingType {
    BULK = 'bulk',
    PACKAGED = 'packaged'
}

export enum WasteFlashpointOption {
    '<_23°' = '< 23°',
    '23°_60°' = '23° - 60°',
    '>_60°' = '> 60°'
}

export enum WastePhOption {
    '<_2' = '< 2',
    '2_4' = '2 - 4',
    '4_10' = '4 - 10',
    '>_10' = '> 10'
}

export enum StableTemperatureType {
    AMBIENT = 'ambient',
    OTHER = 'other'
}

export type WasteCompositionResponse = {
    name: string | null;
    minWeight: number | null;
    maxWeight: number | null;
};

export enum WasteLegislationOption {
    NONE = 'none',
    RADIOACTIVE = 'radioactive',
    CWC = 'cwc',
    CONTROLLED_DRUGS = 'controlled-drugs',
    DRUG_PRECURSOR = 'drug-precursor',
    HG_CONTAINING = 'hg-containing',
    OZON_DEPLETING_SUBSTANCE = 'ozon-depleting-substance',
    ANIMAL_BYPRODUCT = 'animal-byproduct',
    INFECTIOUS_WASTE = 'infectious-waste',
    SVHC = 'svhc'
}

export enum SvhcExtraOption {
    OTHER = 'other',
    '<_1_MG_KG' = '< 1 mg/kg',
    '>_1_MG_KG' = '> 1 mg/kg'
}

export enum WastePropertyOption {
    NONE = 'none',
    EXPLOSIVE = 'explosive',
    GASEOUS = 'gaseous',
    PEROXIDE = 'peroxide',
    POLYMERISATION_SENSITIVE = 'polymerisation-sensitive',
    PYROPHORIC = 'pyrophoric',
    STRONG_OXIDIZING = 'strong-oxidizing',
    REACTIVE_WITH_T_GAS = 'reactive-with-t-gas',
    REACTIVE_WITH_F_GAS = 'reactive-with-f-gas',
    HIGH_ACUTE_TOXIC = 'high-acute-toxic',
    THERMAL_UNSTABLE = 'thermal-unstable'
}

export enum WasteDischargeFrequency {
    ONCE_OFF_STREAM = 'once-off-stream',
    REGULAR_STREAM = 'regular-stream',
    ONCE_OFF_CAMPAIGN = 'once-off-campaign',
    REGULAR_CAMPAIGN = 'regular-campaign'
}

export enum RegulatedTransportOption {
    YES = 'yes',
    NO = 'no',
    UNKNOWN = 'unknown'
}

export type WasteInquiryUnNumberResponse = {
    unNumber: string | null;
    packingGroup: PackingGroup | null;
};

export enum WastePackagingOption {
    ASF = 'asf',
    ASP = 'asp',
    BIG_BAG = 'big-bag',
    CARDBOARD_BOX = 'cardboard-box',
    IBC = 'ibc',
    METAL_DRUM = 'metal-drum',
    OVERSIZED_DRUM = 'oversized-drum',
    PLASTIC_DRUM = 'plastic-drum',
    OTHER = 'other'
}

export enum WeightUnit {
    KG = 'kg'
}

export type WastePackagingResponse = {
    type: WastePackagingOption | null;
    size: string | null;
    weightPerPieceValue: number | null;
    weightPerPieceUnit: WeightUnit | null;
    hasInnerPackaging: boolean | null;
    remarks: string | null;
};

export enum WasteTransportType {
    CONTAINER = 'container',
    SKIP = 'skip',
    TIPPER_TRUCK = 'tipper-truck',
    REL_TRUCK = 'rel-truck',
    OTHER = 'other'
}

export enum ContainerLoadingType {
    HOOK = 'hook',
    CHAIN = 'chain'
}

export enum WasteLoadingType {
    ON_WASTE_COLLECTION = 'on-waste-collection',
    BEFORE_WASTE_COLLECTION = 'before-waste-collection'
}

export enum WasteTransportInOption {
    TANK_TRAILER = 'tank-trailer',
    TANK_CONTAINER = 'tank-container',
    NO_PREFERENCE = 'no-preference',
    OTHER = 'other'
}

export enum WasteLoadingMethod {
    GRAVITATIONAL = 'gravitational',
    PUMP_FROM_CUSTOMER = 'pump-from-customer',
    PUMP_FROM_HAULIER = 'pump-from-haulier'
}

export enum WasteStoredInOption {
    STORAGE_TANK = 'storage-tank',
    TANK_CONTAINER = 'tank-container',
    IBCS = 'ibcs',
    DRUMS = 'drums',
    OTHER = 'other'
}

export enum CollectionRequirementOption {
    TRACTOR = 'tractor',
    TRACTOR_TRAILER = 'tractor-trailer',
    TRACTOR_TRAILER_TANK = 'tractor-trailer-tank'
}

export type ViewWasteInquiryResponse = {
    status: WasteInquiryStatus;
    customer: CustomerResponse | null;
    wasteProducer: WasteProducerResponse | null;
    pickUpAddress: PickUpAddressResponse | null;
    wasteStreamName: string | null;
    wasteStreamDescription: string | null;
    ewcLevel1Name: string | null;
    ewcLevel2Name: string | null;
    ewcLevel3Name: string | null;
    stateOfMatter: StateOfMatter | null;
    packagingType: WastePackagingType | null;
    flashpoint: WasteFlashpointOption | null;
    ph: WastePhOption | null;
    specificGravity: number | null;
    stableTemperatureType: StableTemperatureType | null;
    minStableTemperature: number | null;
    maxStableTemperature: number | null;
    averageStableTemperature: number | null;
    sdsFiles: Array<FileLinkResponse>;
    noSds: boolean;
    analysisReportFiles: Array<FileLinkResponse>;
    noAnalysisReport: boolean;
    composition: Array<WasteCompositionResponse>;
    isSampleAvailable: boolean | null;
    selectedLegislationOptions: Array<WasteLegislationOption>;
    svhcExtra: SvhcExtraOption | null;
    legislationRemarks: string | null;
    selectedPropertyOptions: Array<WastePropertyOption>;
    propertyRemarks: string | null;
    expectedYearlyVolumeAmount: number | null;
    expectedYearlyVolumeUnit: WasteMeasurementUnit | null;
    expectedPerCollectionQuantity: number | null;
    expectedPerCollectionUnit: WasteMeasurementUnit | null;
    dischargeFrequency: WasteDischargeFrequency | null;
    firstCollectionDate: string | null;
    expectedEndDate: string | null;
    collectionRemarks: string | null;
    isTransportByIndaver: boolean | null;
    isLoadingByIndaver: boolean | null;
    isRegulatedTransport: RegulatedTransportOption | null;
    unNumbers: Array<WasteInquiryUnNumberResponse>;
    packaging: Array<WastePackagingResponse>;
    transportType: WasteTransportType | null;
    containerLoadingType: ContainerLoadingType | null;
    loadingType: WasteLoadingType | null;
    transportIn: WasteTransportInOption | null;
    loadingMethod: WasteLoadingMethod | null;
    storedIn: WasteStoredInOption | null;
    transportVolumeAmount: number | null;
    transportVolumeUnit: WasteMeasurementUnit | null;
    isTankOwnedByCustomer: boolean | null;
    collectionRequirements: CollectionRequirementOption | null;
    remarks: string | null;
    sendCopyToContacts: Array<ContactTypeResponse>;
    additionalFiles: Array<FileLinkResponse>;
    submittedOn: string | null;
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type EwcCodeNotFound = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'ewc_code_not_found';
};

export type InvalidStableTemperatureError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'invalid_stable_temperature';
};

export type FileNotAccessibleError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'file_not_accessible';
};

export type NoSdsFilesExpected = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'no_sds_files_expected';
};

export type NoAnalysisReportFilesExpected = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'no_analysis_report_files_expected';
};

export type NoOptionExpectedWhenNoneSelected = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'no_option_expected_when_none_selected';
};

export type NoSvhcExtraExpected = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'no_svhc_extra_expected';
};

export type WasteComposition = {
    name: string | null;
    minWeight: number | null;
    maxWeight: number | null;
};

export type UnNumber = {
    unNumber: string | null;
    packingGroup: PackingGroup | null;
};

export type WastePackaging = {
    type: WastePackagingOption | null;
    size: string | null;
    weightPerPieceValue: number | null;
    weightPerPieceUnit: WeightUnit | null;
    hasInnerPackaging: boolean | null;
    remarks: string | null;
};

export type UpdateWasteInquiryCommand = {
    customerId?: string | null;
    wasteProducerId?: string | null;
    isUnknownWasteProducer?: boolean;
    pickUpAddressId?: string | null;
    isUnknownPickUpAddress?: boolean;
    wasteStreamName?: string | null;
    wasteStreamDescription?: string | null;
    ewcLevel1Name?: string | null;
    ewcLevel2Name?: string | null;
    ewcLevel3Name?: string | null;
    stateOfMatter?: StateOfMatter | null;
    packagingType?: WastePackagingType | null;
    flashpoint?: WasteFlashpointOption | null;
    ph?: WastePhOption | null;
    specificGravity?: number | null;
    stableTemperatureType?: StableTemperatureType | null;
    minStableTemperature?: number | null;
    maxStableTemperature?: number | null;
    averageStableTemperature?: number | null;
    sdsFiles?: Array<CreateFileLinkCommand>;
    noSds?: boolean;
    analysisReportFiles?: Array<CreateFileLinkCommand>;
    noAnalysisReport?: boolean;
    composition?: Array<WasteComposition>;
    isSampleAvailable?: boolean | null;
    selectedLegislationOptions?: Array<WasteLegislationOption>;
    svhcExtra?: SvhcExtraOption | null;
    legislationRemarks?: string | null;
    selectedPropertyOptions?: Array<WastePropertyOption>;
    propertyRemarks?: string | null;
    expectedYearlyVolumeAmount?: number | null;
    expectedYearlyVolumeUnit?: WasteMeasurementUnit | null;
    expectedPerCollectionQuantity?: number | null;
    expectedPerCollectionUnit?: WasteMeasurementUnit | null;
    dischargeFrequency?: WasteDischargeFrequency | null;
    firstCollectionDate?: string | null;
    expectedEndDate?: string | null;
    collectionRemarks?: string | null;
    isTransportByIndaver?: boolean | null;
    isLoadingByIndaver?: boolean | null;
    isRegulatedTransport?: RegulatedTransportOption | null;
    unNumbers?: Array<UnNumber>;
    packaging?: Array<WastePackaging>;
    transportType?: WasteTransportType | null;
    loadingType?: WasteLoadingType | null;
    transportIn?: WasteTransportInOption | null;
    loadingMethod?: WasteLoadingMethod | null;
    storedIn?: WasteStoredInOption | null;
    containerLoadingType?: ContainerLoadingType | null;
    transportVolumeAmount?: number | null;
    transportVolumeUnit?: WasteMeasurementUnit | null;
    isTankOwnedByCustomer?: boolean | null;
    collectionRequirements?: CollectionRequirementOption | null;
    remarks?: string | null;
    sendCopyToContacts?: Array<Contact>;
    additionalFiles?: Array<CreateFileLinkCommand>;
};

export type UpdateWasteInquiryResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type WasteInquiryAlreadySubmitted = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '409';
    code: 'waste_inquiry_already_submitted';
};

export type SubmitWasteInquiryResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    submittedOn: string;
    inquiryNumber: string;
};

export type ViewWasteInquiryIndexFilterQuery = {
    statuses: Array<WasteInquiryStatus>;
};

export type ViewWasteInquiryIndexSapQueryKey = {
    skipToken: string;
};

export type ViewWasteInquiryIndexDbQueryKey = {
    firstCollectionDate: string | null;
    uuid: string;
};

export type ViewWasteInquiryIndexPaginationQuery = {
    limit: number;
    key?: unknown | null;
};

export type WasteInquiryResponse = {
    /**
     * UUID is null when retrieved from SAP
     */
    uuid: string | null;
    inquiryNumber: string | null;
    wasteStreamName: string | null;
    date: string | null;
    contractId: string | null;
    contractItem: string | null;
    customerId: string | null;
    customerName: string | null;
    salesOrganisationId: string | null;
    salesOrganisationName: string | null;
    wasteProducerId: string | null;
    wasteProducerName: string | null;
    pickUpAddressId: string | null;
    pickUpAddressName: string | null;
    requestorName: string | null;
    status: WasteInquiryStatus;
    ewcLevel1: string | null;
    ewcLevel2: string | null;
    ewcLevel3: string | null;
};

export type ViewWasteInquiryIndexResponseMeta = {
    next: unknown | null;
};

export type ViewWasteInquiryIndexResponse = {
    items: Array<WasteInquiryResponse>;
    meta: ViewWasteInquiryIndexResponseMeta;
};

export type ViewWasteInquirySapResponse = {
    status: WasteInquiryStatus;
    customer: CustomerResponse | null;
    wasteProducer: WasteProducerResponse | null;
    pickUpAddress: PickUpAddressResponse | null;
    wasteStreamName: string | null;
    wasteStreamDescription: string | null;
    ewcLevel1Name: string | null;
    ewcLevel2Name: string | null;
    ewcLevel3Name: string | null;
    stateOfMatter: StateOfMatter | null;
    packagingType: WastePackagingType | null;
    flashpoint: WasteFlashpointOption | null;
    ph: WastePhOption | null;
    specificGravity: number | null;
    stableTemperatureType: StableTemperatureType | null;
    minStableTemperature: number | null;
    maxStableTemperature: number | null;
    averageStableTemperature: number | null;
    sdsFiles: Array<FileLinkResponse>;
    noSds: boolean;
    analysisReportFiles: Array<FileLinkResponse>;
    noAnalysisReport: boolean;
    composition: Array<WasteCompositionResponse>;
    isSampleAvailable: boolean | null;
    selectedLegislationOptions: Array<WasteLegislationOption>;
    svhcExtra: SvhcExtraOption | null;
    legislationRemarks: string | null;
    selectedPropertyOptions: Array<WastePropertyOption>;
    propertyRemarks: string | null;
    expectedYearlyVolumeAmount: number | null;
    expectedYearlyVolumeUnit: WasteMeasurementUnit | null;
    expectedPerCollectionQuantity: number | null;
    expectedPerCollectionUnit: WasteMeasurementUnit | null;
    dischargeFrequency: WasteDischargeFrequency | null;
    firstCollectionDate: string | null;
    expectedEndDate: string | null;
    collectionRemarks: string | null;
    isTransportByIndaver: boolean | null;
    isLoadingByIndaver: boolean | null;
    isRegulatedTransport: RegulatedTransportOption | null;
    unNumbers: Array<WasteInquiryUnNumberResponse>;
    packaging: Array<WastePackagingResponse>;
    transportType: WasteTransportType | null;
    containerLoadingType: ContainerLoadingType | null;
    loadingType: WasteLoadingType | null;
    transportIn: WasteTransportInOption | null;
    loadingMethod: WasteLoadingMethod | null;
    storedIn: WasteStoredInOption | null;
    transportVolumeAmount: number | null;
    transportVolumeUnit: WasteMeasurementUnit | null;
    isTankOwnedByCustomer: boolean | null;
    collectionRequirements: CollectionRequirementOption | null;
    remarks: string | null;
    sendCopyToContacts: Array<ContactTypeResponse>;
    additionalFiles: Array<FileLinkResponse>;
    submittedOn: string | null;
    inquiryNumber: string | null;
    createdBy: string | null;
    contractNumber: string | null;
    contractItem: string | null;
};

export type CreateFileSapCommand = {
    fileUuid: string;
};

export type AddDocumentToWasteInquirySapCommand = {
    sdsFiles?: Array<CreateFileSapCommand>;
    analysisReportFiles?: Array<CreateFileSapCommand>;
    additionalFiles?: Array<CreateFileSapCommand>;
};

export type BulkDeleteWasteInquiryCommand = {
    wasteInquiryUuids: Array<string>;
};

export type CopyWasteInquirySapResponse = {
    uuid: string;
};

export type CreateWeeklyPlanningRequestCommand = {
    [key: string]: unknown;
};

export type CreateWeeklyPlanningRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type CustomerNotProvidedError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'customer_not_provided';
};

export type UpdateWeeklyPlanningRequestCommand = {
    customerId?: string | null;
    wasteProducerId?: string | null;
    pickUpAddressIds?: Array<string>;
    remarks?: string | null;
    additionalFiles?: Array<CreateFileLinkCommand>;
    sendCopyToContacts?: Array<Contact>;
};

export type UpdateWeeklyPlanningRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
};

export type AddWprPickUpRequestCommand = {
    pickUpRequestUuid: string;
};

export type AddWprPickUpRequestResponse = {
    uuid: string;
};

export type ViewWprPickUpRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    contractLineId: string;
    contractNumber: string;
    contractItem: string;
    customerReference: string | null;
    wasteMaterial: string | null;
    materialNumber: string | null;
    treatmentCenterName: string | null;
    installationName: string | null;
    customerId: string | null;
    customerName: string | null;
    wasteProducerId: string | null;
    wasteProducerName: string | null;
    pickUpAddressId: string | null;
    pickUpAddressName: string | null;
    asn: string | null;
    tfs: boolean | null;
    isHazardous: boolean | null;
    packaged: string | null;
    tcNumber: string | null;
    materialAnalysis: string | null;
    ewcCode: string | null;
    endTreatmentCenterId: string | null;
    endTreatmentCenterName: string | null;
    remarks: string | null;
    processCode: string | null;
    esnNumber: string | null;
    deliveryInfo: string | null;
    materialType: string | null;
    packagingIndicator: string | null;
    startDate: string | null;
    startTime: string | null;
};

export type ViewWeeklyPlanningRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    customer: CustomerResponse | null;
    wasteProducer: WasteProducerResponse | null;
    pickUpAddresses: Array<PickUpAddressResponse>;
    remarks: string | null;
    sendCopyToContacts: Array<ContactTypeResponse>;
    additionalFiles: Array<FileLinkResponse>;
    pickUpRequests: Array<ViewWprPickUpRequestResponse>;
};

export type WeeklyPlanningRequestAlreadySubmittedError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '400';
    source?: ErrorSource;
    code: 'weekly_planning_request_already_submitted_error';
};

export type SubmitWeeklyPlanningRequestResponse = {
    uuid: string;
    createdAt: string;
    updatedAt: string;
    submittedOn: string;
    inquiryNumber: string;
};

export type InternalServerApiError = {
    /**
     * a human-readable explanation specific to this occurrence of the problem
     */
    detail?: string;
    status: '500';
    code: 'internal_server_error';
};

export type MigrateCollectionsV1Data = {
    body?: never;
    path?: never;
    query?: {
        collections?: Array<'user' | 'contact'>;
        fresh?: boolean;
    };
    url: '/api/v1/typesense/migrate';
};

export type MigrateCollectionsV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type MigrateCollectionsV1Error = MigrateCollectionsV1Errors[keyof MigrateCollectionsV1Errors];

export type MigrateCollectionsV1Responses = {
    /**
     * Successfully migrated collections
     */
    200: unknown;
};

export type ImportCollectionsV1Data = {
    body?: never;
    path?: never;
    query?: {
        collections?: Array<'user' | 'contact'>;
    };
    url: '/api/v1/typesense/import';
};

export type ImportCollectionsV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ImportCollectionsV1Error = ImportCollectionsV1Errors[keyof ImportCollectionsV1Errors];

export type ImportCollectionsV1Responses = {
    /**
     * Successfully imported collections
     */
    200: unknown;
};

export type ViewCollectionsV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/typesense/collections';
};

export type ViewCollectionsV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewCollectionsV1Error = ViewCollectionsV1Errors[keyof ViewCollectionsV1Errors];

export type ViewCollectionsV1Responses = {
    /**
     * Successfully returned collections
     */
    200: unknown;
};

export type ViewMeV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/users/me';
};

export type ViewMeV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewMeV1Error = ViewMeV1Errors[keyof ViewMeV1Errors];

export type ViewMeV1Responses = {
    /**
     * User details retrieved
     */
    200: ViewMeResponse;
};

export type ViewMeV1Response = ViewMeV1Responses[keyof ViewMeV1Responses];

export type ViewUserDetailV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/users/{uuid}';
};

export type ViewUserDetailV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUserDetailV1Error = ViewUserDetailV1Errors[keyof ViewUserDetailV1Errors];

export type ViewUserDetailV1Responses = {
    /**
     * User details retrieved
     */
    200: ViewUserDetailResponse;
};

export type ViewUserDetailV1Response = ViewUserDetailV1Responses[keyof ViewUserDetailV1Responses];

export type ViewUserIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        pagination?: PaginatedOffsetQuery;
        search?: string;
    };
    url: '/api/v1/users';
};

export type ViewUserIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUserIndexV1Error = ViewUserIndexV1Errors[keyof ViewUserIndexV1Errors];

export type ViewUserIndexV1Responses = {
    /**
     * Users retrieved
     */
    200: ViewUserIndexResponse;
};

export type ViewUserIndexV1Response = ViewUserIndexV1Responses[keyof ViewUserIndexV1Responses];

export type ViewPermissionIndexV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/permissions';
};

export type ViewPermissionIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPermissionIndexV1Error = ViewPermissionIndexV1Errors[keyof ViewPermissionIndexV1Errors];

export type ViewPermissionIndexV1Responses = {
    200: ViewPermissionIndexResponse;
};

export type ViewPermissionIndexV1Response = ViewPermissionIndexV1Responses[keyof ViewPermissionIndexV1Responses];

export type ViewCustomerIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        search?: string;
        pagination?: ViewCustomerIndexPaginationQuery;
    };
    url: '/api/v1/customers';
};

export type ViewCustomerIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewCustomerIndexV1Error = ViewCustomerIndexV1Errors[keyof ViewCustomerIndexV1Errors];

export type ViewCustomerIndexV1Responses = {
    200: ViewCustomerIndexResponse;
};

export type ViewCustomerIndexV1Response = ViewCustomerIndexV1Responses[keyof ViewCustomerIndexV1Responses];

export type ViewSuggestedCustomersV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewSuggestedCustomersFilterQuery;
    };
    url: '/api/v1/suggested-customers';
};

export type ViewSuggestedCustomersV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewSuggestedCustomersV1Error = ViewSuggestedCustomersV1Errors[keyof ViewSuggestedCustomersV1Errors];

export type ViewSuggestedCustomersV1Responses = {
    200: ViewSuggestedCustomersResponse;
};

export type ViewSuggestedCustomersV1Response = ViewSuggestedCustomersV1Responses[keyof ViewSuggestedCustomersV1Responses];

export type ViewCustomerCountryV1Data = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/v1/customers/{id}/country';
};

export type ViewCustomerCountryV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<CustomerNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewCustomerCountryV1Error = ViewCustomerCountryV1Errors[keyof ViewCustomerCountryV1Errors];

export type ViewCustomerCountryV1Responses = {
    200: ViewCustomerCountryResponse;
};

export type ViewCustomerCountryV1Response = ViewCustomerCountryV1Responses[keyof ViewCustomerCountryV1Responses];

export type ViewDomainEventLogIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        filter?: ViewDomainEventLogIndexFilterQuery;
        pagination?: ViewDomainEventLogIndexPaginationQuery;
    };
    url: '/api/v1/event-logs';
};

export type ViewDomainEventLogIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDomainEventLogIndexV1Error = ViewDomainEventLogIndexV1Errors[keyof ViewDomainEventLogIndexV1Errors];

export type ViewDomainEventLogIndexV1Responses = {
    200: ViewDomainEventLogIndexResponse;
};

export type ViewDomainEventLogIndexV1Response = ViewDomainEventLogIndexV1Responses[keyof ViewDomainEventLogIndexV1Responses];

export type CreateFileV1Data = {
    body: CreateFileCommand;
    path?: never;
    query?: never;
    url: '/api/v1/files';
};

export type CreateFileV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateFileV1Error = CreateFileV1Errors[keyof CreateFileV1Errors];

export type CreateFileV1Responses = {
    201: CreateFileResponse;
};

export type CreateFileV1Response = CreateFileV1Responses[keyof CreateFileV1Responses];

export type ConfirmFileUploadV1Data = {
    body?: never;
    path: {
        file: string;
    };
    query?: never;
    url: '/api/v1/files/{file}/confirm-upload';
};

export type ConfirmFileUploadV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ConfirmFileUploadV1Error = ConfirmFileUploadV1Errors[keyof ConfirmFileUploadV1Errors];

export type ConfirmFileUploadV1Responses = {
    200: unknown;
};

export type DownloadFileV1Data = {
    body?: never;
    path: {
        file: string;
    };
    query?: never;
    url: '/api/v1/files/{file}/download';
};

export type DownloadFileV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DownloadFileV1Error = DownloadFileV1Errors[keyof DownloadFileV1Errors];

export type ProxyExternalFileV1Data = {
    body?: never;
    path?: never;
    query: {
        url: string;
    };
    url: '/api/v1/files/proxy-external';
};

export type ProxyExternalFileV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ProxyExternalFileV1Error = ProxyExternalFileV1Errors[keyof ProxyExternalFileV1Errors];

export type ProxyExternalFileV1Responses = {
    200: unknown;
};

export type SearchCollectionsV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: SearchCollectionsFilterQuery;
        search: string;
    };
    url: '/api/v1/search';
};

export type SearchCollectionsV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SearchCollectionsV1Error = SearchCollectionsV1Errors[keyof SearchCollectionsV1Errors];

export type SearchCollectionsV1Responses = {
    200: SearchCollectionsResponse;
};

export type SearchCollectionsV1Response = SearchCollectionsV1Responses[keyof SearchCollectionsV1Responses];

export type ViewJobsIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        sort?: Array<ViewJobsIndexSortQuery>;
        filter?: ViewJobsIndexFilterQuery;
        pagination?: ViewJobsIndexPaginationQuery;
    };
    url: '/api/v1/jobs';
};

export type ViewJobsIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewJobsIndexV1Error = ViewJobsIndexV1Errors[keyof ViewJobsIndexV1Errors];

export type ViewJobsIndexV1Responses = {
    200: ViewJobsIndexResponse;
};

export type ViewJobsIndexV1Response = ViewJobsIndexV1Responses[keyof ViewJobsIndexV1Responses];

export type ViewJobDetailV1Data = {
    body?: never;
    path: {
        jobId: string;
    };
    query: {
        isArchived: boolean;
    };
    url: '/api/v1/jobs/{jobId}';
};

export type ViewJobDetailV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewJobDetailV1Error = ViewJobDetailV1Errors[keyof ViewJobDetailV1Errors];

export type ViewJobDetailV1Responses = {
    200: ViewJobDetailResponse;
};

export type ViewJobDetailV1Response = ViewJobDetailV1Responses[keyof ViewJobDetailV1Responses];

export type GetMyNotificationPreferencesV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/me/notification-preferences';
};

export type GetMyNotificationPreferencesV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type GetMyNotificationPreferencesV1Error = GetMyNotificationPreferencesV1Errors[keyof GetMyNotificationPreferencesV1Errors];

export type GetMyNotificationPreferencesV1Responses = {
    200: GetMyNotificationPreferencesResponse;
};

export type GetMyNotificationPreferencesV1Response = GetMyNotificationPreferencesV1Responses[keyof GetMyNotificationPreferencesV1Responses];

export type GetNotificationTypesConfigV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/notification-preferences/config';
};

export type GetNotificationTypesConfigV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type GetNotificationTypesConfigV1Error = GetNotificationTypesConfigV1Errors[keyof GetNotificationTypesConfigV1Errors];

export type GetNotificationTypesConfigV1Responses = {
    200: GetNotificationTypesConfigResponse;
};

export type GetNotificationTypesConfigV1Response = GetNotificationTypesConfigV1Responses[keyof GetNotificationTypesConfigV1Responses];

export type UpdateMyChannelNotificationPreferenceV1Data = {
    body: UpdateMyChannelNotificationPreferenceCommand;
    path?: never;
    query?: never;
    url: '/api/v1/me/notification-preferences/channels';
};

export type UpdateMyChannelNotificationPreferenceV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateMyChannelNotificationPreferenceV1Error = UpdateMyChannelNotificationPreferenceV1Errors[keyof UpdateMyChannelNotificationPreferenceV1Errors];

export type UpdateMyChannelNotificationPreferenceV1Responses = {
    204: void;
};

export type UpdateMyChannelNotificationPreferenceV1Response = UpdateMyChannelNotificationPreferenceV1Responses[keyof UpdateMyChannelNotificationPreferenceV1Responses];

export type SendTestNotificationV1Data = {
    body: SendTestNotificationCommand;
    path?: never;
    query?: never;
    url: '/api/v1/notifications/test-notification';
};

export type SendTestNotificationV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SendTestNotificationV1Error = SendTestNotificationV1Errors[keyof SendTestNotificationV1Errors];

export type SendTestNotificationV1Responses = {
    204: void;
};

export type SendTestNotificationV1Response = SendTestNotificationV1Responses[keyof SendTestNotificationV1Responses];

export type GetMyNotificationsV1Data = {
    body?: never;
    path?: never;
    query?: {
        filter?: GetMyNotificationsFilterQuery;
        pagination?: GetMyNotificationsPaginationQuery;
    };
    url: '/api/v1/me/notifications';
};

export type GetMyNotificationsV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type GetMyNotificationsV1Error = GetMyNotificationsV1Errors[keyof GetMyNotificationsV1Errors];

export type GetMyNotificationsV1Responses = {
    200: GetMyNotificationsResponse;
};

export type GetMyNotificationsV1Response = GetMyNotificationsV1Responses[keyof GetMyNotificationsV1Responses];

export type ViewUnreadNotificationsCountV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/me/notifications/unread-count';
};

export type ViewUnreadNotificationsCountV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUnreadNotificationsCountV1Error = ViewUnreadNotificationsCountV1Errors[keyof ViewUnreadNotificationsCountV1Errors];

export type ViewUnreadNotificationsCountV1Responses = {
    200: ViewUnreadNotificationsCountResponse;
};

export type ViewUnreadNotificationsCountV1Response = ViewUnreadNotificationsCountV1Responses[keyof ViewUnreadNotificationsCountV1Responses];

export type ViewUserNotificationDetailV1Data = {
    body?: never;
    path: {
        notificationUuid: string;
    };
    query?: never;
    url: '/api/v1/me/notifications/{notificationUuid}';
};

export type ViewUserNotificationDetailV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUserNotificationDetailV1Error = ViewUserNotificationDetailV1Errors[keyof ViewUserNotificationDetailV1Errors];

export type ViewUserNotificationDetailV1Responses = {
    200: TestNotificationNotification;
};

export type ViewUserNotificationDetailV1Response = ViewUserNotificationDetailV1Responses[keyof ViewUserNotificationDetailV1Responses];

export type MarkAllNotificationAsReadV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/me/notifications/mark-as-read';
};

export type MarkAllNotificationAsReadV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type MarkAllNotificationAsReadV1Error = MarkAllNotificationAsReadV1Errors[keyof MarkAllNotificationAsReadV1Errors];

export type MarkAllNotificationAsReadV1Responses = {
    204: void;
};

export type MarkAllNotificationAsReadV1Response = MarkAllNotificationAsReadV1Responses[keyof MarkAllNotificationAsReadV1Responses];

export type UpdateMyNotificationTypePreferenceV1Data = {
    body: UpdateMyNotificationTypePreferenceCommand;
    path?: never;
    query?: never;
    url: '/api/v1/me/notification-preferences/types';
};

export type UpdateMyNotificationTypePreferenceV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateMyNotificationTypePreferenceV1Error = UpdateMyNotificationTypePreferenceV1Errors[keyof UpdateMyNotificationTypePreferenceV1Errors];

export type UpdateMyNotificationTypePreferenceV1Responses = {
    204: void;
};

export type UpdateMyNotificationTypePreferenceV1Response = UpdateMyNotificationTypePreferenceV1Responses[keyof UpdateMyNotificationTypePreferenceV1Responses];

export type MarkNotificationAsReadV1Data = {
    body?: never;
    path: {
        notificationUuid: string;
    };
    query?: never;
    url: '/api/v1/me/notifications/{notificationUuid}/mark-as-read';
};

export type MarkNotificationAsReadV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<UserNotificationNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type MarkNotificationAsReadV1Error = MarkNotificationAsReadV1Errors[keyof MarkNotificationAsReadV1Errors];

export type MarkNotificationAsReadV1Responses = {
    204: void;
};

export type MarkNotificationAsReadV1Response = MarkNotificationAsReadV1Responses[keyof MarkNotificationAsReadV1Responses];

export type MarkNotificationAsUnreadV1Data = {
    body?: never;
    path: {
        notificationUuid: string;
    };
    query?: never;
    url: '/api/v1/me/notifications/{notificationUuid}/mark-as-unread';
};

export type MarkNotificationAsUnreadV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<UserNotificationNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type MarkNotificationAsUnreadV1Error = MarkNotificationAsUnreadV1Errors[keyof MarkNotificationAsUnreadV1Errors];

export type MarkNotificationAsUnreadV1Responses = {
    204: void;
};

export type MarkNotificationAsUnreadV1Response = MarkNotificationAsUnreadV1Responses[keyof MarkNotificationAsUnreadV1Responses];

export type UpdateMyNotificationPreferencePresetV1Data = {
    body: UpdateMyNotificationPreferencePresetCommand;
    path?: never;
    query?: never;
    url: '/api/v1/me/notification-preferences/preset';
};

export type UpdateMyNotificationPreferencePresetV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateMyNotificationPreferencePresetV1Error = UpdateMyNotificationPreferencePresetV1Errors[keyof UpdateMyNotificationPreferencePresetV1Errors];

export type UpdateMyNotificationPreferencePresetV1Responses = {
    204: void;
};

export type UpdateMyNotificationPreferencePresetV1Response = UpdateMyNotificationPreferencePresetV1Responses[keyof UpdateMyNotificationPreferencePresetV1Responses];

export type MigrateNotificationTypesV1Data = {
    body: MigrateNotificationTypesCommand;
    path?: never;
    query?: never;
    url: '/api/v1/notifications/migrate';
};

export type MigrateNotificationTypesV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<MigrationAlreadyPerformedError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type MigrateNotificationTypesV1Error = MigrateNotificationTypesV1Errors[keyof MigrateNotificationTypesV1Errors];

export type MigrateNotificationTypesV1Responses = {
    204: void;
};

export type MigrateNotificationTypesV1Response = MigrateNotificationTypesV1Responses[keyof MigrateNotificationTypesV1Responses];

export type ViewRoleIndexV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/roles';
};

export type ViewRoleIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewRoleIndexV1Error = ViewRoleIndexV1Errors[keyof ViewRoleIndexV1Errors];

export type ViewRoleIndexV1Responses = {
    /**
     * The roles has been successfully received.
     */
    200: ViewRoleIndexResponse;
};

export type ViewRoleIndexV1Response = ViewRoleIndexV1Responses[keyof ViewRoleIndexV1Responses];

export type UpdateRolesPermissionsV1Data = {
    body: UpdateRolesPermissionsCommand;
    path?: never;
    query?: never;
    url: '/api/v1/roles';
};

export type UpdateRolesPermissionsV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<RoleNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateRolesPermissionsV1Error = UpdateRolesPermissionsV1Errors[keyof UpdateRolesPermissionsV1Errors];

export type UpdateRolesPermissionsV1Responses = {
    204: void;
};

export type UpdateRolesPermissionsV1Response = UpdateRolesPermissionsV1Responses[keyof UpdateRolesPermissionsV1Responses];

export type CreateRoleV1Data = {
    body: CreateRoleCommand;
    path?: never;
    query?: never;
    url: '/api/v1/roles';
};

export type CreateRoleV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateRoleV1Error = CreateRoleV1Errors[keyof CreateRoleV1Errors];

export type CreateRoleV1Responses = {
    201: CreateRoleResponse;
};

export type CreateRoleV1Response = CreateRoleV1Responses[keyof CreateRoleV1Responses];

export type ClearRolePermissionsCacheV1Data = {
    body: ClearRolePermissionsCacheCommand;
    path?: never;
    query?: never;
    url: '/api/v1/roles/clear-cache';
};

export type ClearRolePermissionsCacheV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ClearRolePermissionsCacheV1Error = ClearRolePermissionsCacheV1Errors[keyof ClearRolePermissionsCacheV1Errors];

export type ClearRolePermissionsCacheV1Responses = {
    204: void;
};

export type ClearRolePermissionsCacheV1Response = ClearRolePermissionsCacheV1Responses[keyof ClearRolePermissionsCacheV1Responses];

export type DeleteRoleV1Data = {
    body?: never;
    path: {
        role: string;
    };
    query?: never;
    url: '/api/v1/roles/{role}';
};

export type DeleteRoleV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DeleteRoleV1Error = DeleteRoleV1Errors[keyof DeleteRoleV1Errors];

export type DeleteRoleV1Responses = {
    204: void;
};

export type DeleteRoleV1Response = DeleteRoleV1Responses[keyof DeleteRoleV1Responses];

export type ViewRoleDetailV1Data = {
    body?: never;
    path: {
        role: string;
    };
    query?: never;
    url: '/api/v1/roles/{role}';
};

export type ViewRoleDetailV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewRoleDetailV1Error = ViewRoleDetailV1Errors[keyof ViewRoleDetailV1Errors];

export type ViewRoleDetailV1Responses = {
    /**
     * The role has been successfully received.
     */
    200: ViewRoleDetailResponse;
};

export type ViewRoleDetailV1Response = ViewRoleDetailV1Responses[keyof ViewRoleDetailV1Responses];

export type UpdateRoleV1Data = {
    body: UpdateRoleCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/roles/{uuid}';
};

export type UpdateRoleV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateRoleV1Error = UpdateRoleV1Errors[keyof UpdateRoleV1Errors];

export type UpdateRoleV1Responses = {
    201: unknown;
};

export type GetApiInfoData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api';
};

export type GetApiInfoErrors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type GetApiInfoError = GetApiInfoErrors[keyof GetApiInfoErrors];

export type GetApiInfoResponses = {
    /**
     * API info retrieved
     */
    200: GetApiInfoResponse;
};

export type GetApiInfoResponse2 = GetApiInfoResponses[keyof GetApiInfoResponses];

export type SwaggerData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/oauth2-redirect';
};

export type SwaggerErrors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SwaggerError = SwaggerErrors[keyof SwaggerErrors];

export type SwaggerResponses = {
    200: unknown;
};

export type ViewUiPreferencesV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/me/ui-preferences';
};

export type ViewUiPreferencesV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUiPreferencesV1Error = ViewUiPreferencesV1Errors[keyof ViewUiPreferencesV1Errors];

export type ViewUiPreferencesV1Responses = {
    200: ViewUiPreferencesResponse;
};

export type ViewUiPreferencesV1Response = ViewUiPreferencesV1Responses[keyof ViewUiPreferencesV1Responses];

export type UpdateUiPreferencesV1Data = {
    body: UpdateUiPreferencesCommand;
    path?: never;
    query?: never;
    url: '/api/v1/me/ui-preferences';
};

export type UpdateUiPreferencesV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateUiPreferencesV1Error = UpdateUiPreferencesV1Errors[keyof UpdateUiPreferencesV1Errors];

export type UpdateUiPreferencesV1Responses = {
    200: unknown;
};

export type ViewAnnouncementIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        pagination?: PaginatedOffsetQuery;
    };
    url: '/api/v1/announcements';
};

export type ViewAnnouncementIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewAnnouncementIndexV1Error = ViewAnnouncementIndexV1Errors[keyof ViewAnnouncementIndexV1Errors];

export type ViewAnnouncementIndexV1Responses = {
    200: ViewAnnouncementIndexResponse;
};

export type ViewAnnouncementIndexV1Response = ViewAnnouncementIndexV1Responses[keyof ViewAnnouncementIndexV1Responses];

export type CreateAnnouncementV1Data = {
    body: CreateAnnouncementCommand;
    path?: never;
    query?: never;
    url: '/api/v1/announcements';
};

export type CreateAnnouncementV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateAnnouncementV1Error = CreateAnnouncementV1Errors[keyof CreateAnnouncementV1Errors];

export type CreateAnnouncementV1Responses = {
    201: CreateAnnouncementResponse;
};

export type CreateAnnouncementV1Response = CreateAnnouncementV1Responses[keyof CreateAnnouncementV1Responses];

export type DeleteAnnouncementV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/announcements/{uuid}';
};

export type DeleteAnnouncementV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DeleteAnnouncementV1Error = DeleteAnnouncementV1Errors[keyof DeleteAnnouncementV1Errors];

export type DeleteAnnouncementV1Responses = {
    200: unknown;
};

export type ViewAnnouncementV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/announcements/{uuid}';
};

export type ViewAnnouncementV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewAnnouncementV1Error = ViewAnnouncementV1Errors[keyof ViewAnnouncementV1Errors];

export type ViewAnnouncementV1Responses = {
    200: ViewAnnouncementResponse;
};

export type ViewAnnouncementV1Response = ViewAnnouncementV1Responses[keyof ViewAnnouncementV1Responses];

export type UpdateAnnouncementV1Data = {
    body: UpdateAnnouncementCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/announcements/{uuid}';
};

export type UpdateAnnouncementV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<MissingRequiredFieldError | DateMustBeAfterError | FieldMustBeNullError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateAnnouncementV1Error = UpdateAnnouncementV1Errors[keyof UpdateAnnouncementV1Errors];

export type UpdateAnnouncementV1Responses = {
    200: UpdateAnnouncementResponse;
};

export type UpdateAnnouncementV1Response = UpdateAnnouncementV1Responses[keyof UpdateAnnouncementV1Responses];

export type ViewDashboardAnnouncementIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        pagination?: PaginatedOffsetQuery;
    };
    url: '/api/v1/dashboard-announcements';
};

export type ViewDashboardAnnouncementIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDashboardAnnouncementIndexV1Error = ViewDashboardAnnouncementIndexV1Errors[keyof ViewDashboardAnnouncementIndexV1Errors];

export type ViewDashboardAnnouncementIndexV1Responses = {
    200: ViewDashboardAnnouncementIndexResponse;
};

export type ViewDashboardAnnouncementIndexV1Response = ViewDashboardAnnouncementIndexV1Responses[keyof ViewDashboardAnnouncementIndexV1Responses];

export type ViewDashboardAnnouncementV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/dashboard-announcements/{uuid}';
};

export type ViewDashboardAnnouncementV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDashboardAnnouncementV1Error = ViewDashboardAnnouncementV1Errors[keyof ViewDashboardAnnouncementV1Errors];

export type ViewDashboardAnnouncementV1Responses = {
    200: ViewDashboardAnnouncementResponse;
};

export type ViewDashboardAnnouncementV1Response = ViewDashboardAnnouncementV1Responses[keyof ViewDashboardAnnouncementV1Responses];

export type ViewContactIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        pagination?: PaginatedOffsetQuery;
        search?: string;
    };
    url: '/api/v1/contacts';
};

export type ViewContactIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewContactIndexV1Error = ViewContactIndexV1Errors[keyof ViewContactIndexV1Errors];

export type ViewContactIndexV1Responses = {
    200: ViewContactIndexResponse;
};

export type ViewContactIndexV1Response = ViewContactIndexV1Responses[keyof ViewContactIndexV1Responses];

export type CreateContactV1Data = {
    body: CreateContactCommand;
    path?: never;
    query?: never;
    url: '/api/v1/contacts';
};

export type CreateContactV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateContactV1Error = CreateContactV1Errors[keyof CreateContactV1Errors];

export type CreateContactV1Responses = {
    201: CreateContactResponse;
};

export type CreateContactV1Response = CreateContactV1Responses[keyof CreateContactV1Responses];

export type DeleteContactV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/contacts/{uuid}';
};

export type DeleteContactV1Errors = {
    401: unknown;
    404: {
        traceId?: string | null;
        errors?: Array<unknown>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DeleteContactV1Error = DeleteContactV1Errors[keyof DeleteContactV1Errors];

export type DeleteContactV1Responses = {
    204: void;
};

export type DeleteContactV1Response = DeleteContactV1Responses[keyof DeleteContactV1Responses];

export type UpdateContactV1Data = {
    body: UpdateContactCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/contacts/{uuid}';
};

export type UpdateContactV1Errors = {
    401: unknown;
    404: {
        traceId?: string | null;
        errors?: Array<unknown>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateContactV1Error = UpdateContactV1Errors[keyof UpdateContactV1Errors];

export type UpdateContactV1Responses = {
    204: void;
};

export type UpdateContactV1Response = UpdateContactV1Responses[keyof UpdateContactV1Responses];

export type ViewContainerTypeIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewContainerTypeIndexFilterQuery;
        search?: string;
    };
    url: '/api/v1/container-types';
};

export type ViewContainerTypeIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewContainerTypeIndexV1Error = ViewContainerTypeIndexV1Errors[keyof ViewContainerTypeIndexV1Errors];

export type ViewContainerTypeIndexV1Responses = {
    200: ViewContainerTypeIndexResponse;
};

export type ViewContainerTypeIndexV1Response = ViewContainerTypeIndexV1Responses[keyof ViewContainerTypeIndexV1Responses];

export type ViewContractLineIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewContractLineIndexFilterQuery;
        search?: string;
        pagination?: ViewContractLineIndexPaginationQuery;
    };
    url: '/api/v1/contract-lines';
};

export type ViewContractLineIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewContractLineIndexV1Error = ViewContractLineIndexV1Errors[keyof ViewContractLineIndexV1Errors];

export type ViewContractLineIndexV1Responses = {
    200: ViewContractLineIndexResponse;
};

export type ViewContractLineIndexV1Response = ViewContractLineIndexV1Responses[keyof ViewContractLineIndexV1Responses];

export type ViewWprContractLineIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        pagination?: PaginatedOffsetQuery;
        filter: ViewWprContractLineIndexFilterQuery;
    };
    url: '/api/v1/contract-lines/weekly-planning-requests';
};

export type ViewWprContractLineIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewWprContractLineIndexV1Error = ViewWprContractLineIndexV1Errors[keyof ViewWprContractLineIndexV1Errors];

export type ViewWprContractLineIndexV1Responses = {
    200: ViewWprContractLineIndexResponse;
};

export type ViewWprContractLineIndexV1Response = ViewWprContractLineIndexV1Responses[keyof ViewWprContractLineIndexV1Responses];

export type ViewPackagingRequestContractLineIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewPackagingRequestContractLineIndexFilterQuery;
        pagination?: ViewPackagingRequestContractLineIndexPaginationQuery;
    };
    url: '/api/v1/contract-lines/packaging-requests';
};

export type ViewPackagingRequestContractLineIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPackagingRequestContractLineIndexV1Error = ViewPackagingRequestContractLineIndexV1Errors[keyof ViewPackagingRequestContractLineIndexV1Errors];

export type ViewPackagingRequestContractLineIndexV1Responses = {
    200: ViewPackagingRequestContractLineIndexResponse;
};

export type ViewPackagingRequestContractLineIndexV1Response = ViewPackagingRequestContractLineIndexV1Responses[keyof ViewPackagingRequestContractLineIndexV1Responses];

export type GenerateContractLinesPdfV1Data = {
    body: GenerateContractLinesPdfCommand;
    path?: never;
    query?: never;
    url: '/api/v1/contract-lines/generate-pdf';
};

export type GenerateContractLinesPdfV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<ContractLineNotAccessibleError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type GenerateContractLinesPdfV1Error = GenerateContractLinesPdfV1Errors[keyof GenerateContractLinesPdfV1Errors];

export type GenerateContractLinesPdfV1Responses = {
    200: GenerateContractLinesPdfResponse;
};

export type GenerateContractLinesPdfV1Response = GenerateContractLinesPdfV1Responses[keyof GenerateContractLinesPdfV1Responses];

export type DownloadDocumentV1Data = {
    body: DownloadDocumentCommand;
    path?: never;
    query?: never;
    url: '/api/v1/documents/download';
};

export type DownloadDocumentV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<DocumentNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DownloadDocumentV1Error = DownloadDocumentV1Errors[keyof DownloadDocumentV1Errors];

export type ViewDocumentIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        pagination?: ViewDocumentIndexPaginationQuery;
        filter: ViewDocumentIndexFilterQuery;
        /**
         * Searches on document name
         */
        search?: string;
    };
    url: '/api/v1/documents';
};

export type ViewDocumentIndexV1Errors = {
    403: {
        traceId?: string | null;
        errors?: Array<ForbiddenError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDocumentIndexV1Error = ViewDocumentIndexV1Errors[keyof ViewDocumentIndexV1Errors];

export type ViewDocumentIndexV1Responses = {
    200: ViewDocumentIndexResponse;
};

export type ViewDocumentIndexV1Response = ViewDocumentIndexV1Responses[keyof ViewDocumentIndexV1Responses];

export type ViewUserSiteIndexV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/documents/sites';
};

export type ViewUserSiteIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUserSiteIndexV1Error = ViewUserSiteIndexV1Errors[keyof ViewUserSiteIndexV1Errors];

export type ViewUserSiteIndexV1Responses = {
    200: Array<ViewUserSiteIndexResponse>;
};

export type ViewUserSiteIndexV1Response = ViewUserSiteIndexV1Responses[keyof ViewUserSiteIndexV1Responses];

export type ViewDynamicTableColumnIndexV1Data = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/v1/dynamic-tables/{name}/columns';
};

export type ViewDynamicTableColumnIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDynamicTableColumnIndexV1Error = ViewDynamicTableColumnIndexV1Errors[keyof ViewDynamicTableColumnIndexV1Errors];

export type ViewDynamicTableColumnIndexV1Responses = {
    200: DynamicTableIndexColumnResponse;
};

export type ViewDynamicTableColumnIndexV1Response = ViewDynamicTableColumnIndexV1Responses[keyof ViewDynamicTableColumnIndexV1Responses];

export type ViewDynamicTableViewIndexV1Data = {
    body?: never;
    path: {
        name: string;
    };
    query?: {
        pagination?: PaginatedOffsetQuery;
    };
    url: '/api/v1/dynamic-tables/{name}/views';
};

export type ViewDynamicTableViewIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDynamicTableViewIndexV1Error = ViewDynamicTableViewIndexV1Errors[keyof ViewDynamicTableViewIndexV1Errors];

export type ViewDynamicTableViewIndexV1Responses = {
    200: DynamicTableViewIndexResponse;
};

export type ViewDynamicTableViewIndexV1Response = ViewDynamicTableViewIndexV1Responses[keyof ViewDynamicTableViewIndexV1Responses];

export type CreateDynamicTableViewV1Data = {
    body: CreateDynamicTableViewCommand;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/v1/dynamic-tables/{name}/views';
};

export type CreateDynamicTableViewV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<ColumnNotFoundError | ColumnNotFilterableError | ColumnNotSortableError | DuplicateColumnError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateDynamicTableViewV1Error = CreateDynamicTableViewV1Errors[keyof CreateDynamicTableViewV1Errors];

export type CreateDynamicTableViewV1Responses = {
    201: CreateDynamicTableViewResponse;
};

export type CreateDynamicTableViewV1Response = CreateDynamicTableViewV1Responses[keyof CreateDynamicTableViewV1Responses];

export type ViewDefaultDynamicTableViewV1Data = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/v1/dynamic-tables/{name}/default-view';
};

export type ViewDefaultDynamicTableViewV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDefaultDynamicTableViewV1Error = ViewDefaultDynamicTableViewV1Errors[keyof ViewDefaultDynamicTableViewV1Errors];

export type ViewDefaultDynamicTableViewV1Responses = {
    200: ViewDefaultDynamicTableViewResponse;
};

export type ViewDefaultDynamicTableViewV1Response = ViewDefaultDynamicTableViewV1Responses[keyof ViewDefaultDynamicTableViewV1Responses];

export type DeleteDynamicTableViewV1Data = {
    body?: never;
    path: {
        name: string;
        uuid: string;
    };
    query?: never;
    url: '/api/v1/dynamic-tables/{name}/views/{uuid}';
};

export type DeleteDynamicTableViewV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<GlobalDefaultViewNotDeletable | LastGlobalViewNotDeletable>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DeleteDynamicTableViewV1Error = DeleteDynamicTableViewV1Errors[keyof DeleteDynamicTableViewV1Errors];

export type DeleteDynamicTableViewV1Responses = {
    200: unknown;
};

export type UpdateDynamicTableViewV1Data = {
    body: UpdateDynamicTableViewCommand;
    path: {
        name: string;
        uuid: string;
    };
    query?: never;
    url: '/api/v1/dynamic-tables/{name}/views/{uuid}';
};

export type UpdateDynamicTableViewV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<ColumnNotFoundError | ColumnNotFilterableError | ColumnNotSortableError | DuplicateColumnError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateDynamicTableViewV1Error = UpdateDynamicTableViewV1Errors[keyof UpdateDynamicTableViewV1Errors];

export type UpdateDynamicTableViewV1Responses = {
    200: UpdateDynamicTableViewResponse;
};

export type UpdateDynamicTableViewV1Response = UpdateDynamicTableViewV1Responses[keyof UpdateDynamicTableViewV1Responses];

export type ViewEwcCodeIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        search?: string;
    };
    url: '/api/v1/ewc-codes';
};

export type ViewEwcCodeIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewEwcCodeIndexV1Error = ViewEwcCodeIndexV1Errors[keyof ViewEwcCodeIndexV1Errors];

export type ViewEwcCodeIndexV1Responses = {
    200: ViewEwcCodeIndexResponse;
};

export type ViewEwcCodeIndexV1Response = ViewEwcCodeIndexV1Responses[keyof ViewEwcCodeIndexV1Responses];

export type ViewInvoiceIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewInvoiceIndexFilterQuery;
        pagination?: ViewInvoiceIndexQueryKey;
    };
    url: '/api/v1/invoices';
};

export type ViewInvoiceIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewInvoiceIndexV1Error = ViewInvoiceIndexV1Errors[keyof ViewInvoiceIndexV1Errors];

export type ViewInvoiceIndexV1Responses = {
    200: ViewInvoiceIndexResponse;
};

export type ViewInvoiceIndexV1Response = ViewInvoiceIndexV1Responses[keyof ViewInvoiceIndexV1Responses];

export type ViewDraftInvoiceIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewDraftInvoiceIndexFilterQuery;
        pagination?: ViewDraftInvoiceIndexQueryKey;
    };
    url: '/api/v1/invoices/draft';
};

export type ViewDraftInvoiceIndexV1Errors = {
    403: {
        traceId?: string | null;
        errors?: Array<ForbiddenError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDraftInvoiceIndexV1Error = ViewDraftInvoiceIndexV1Errors[keyof ViewDraftInvoiceIndexV1Errors];

export type ViewDraftInvoiceIndexV1Responses = {
    200: ViewDraftInvoiceIndexResponse;
};

export type ViewDraftInvoiceIndexV1Response = ViewDraftInvoiceIndexV1Responses[keyof ViewDraftInvoiceIndexV1Responses];

export type ApproveDraftInvoiceV1Data = {
    body: ApproveDraftInvoiceCommand;
    path: {
        invoiceNumber: string;
    };
    query?: never;
    url: '/api/v1/invoices/draft/{invoiceNumber}/approve';
};

export type ApproveDraftInvoiceV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<InvoiceNotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<NonApproveOrRejectableDraftInvoiceError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ApproveDraftInvoiceV1Error = ApproveDraftInvoiceV1Errors[keyof ApproveDraftInvoiceV1Errors];

export type ApproveDraftInvoiceV1Responses = {
    200: unknown;
};

export type RejectDraftInvoiceV1Data = {
    body: RejectDraftInvoiceCommand;
    path: {
        invoiceNumber: string;
    };
    query?: never;
    url: '/api/v1/invoices/draft/{invoiceNumber}/reject';
};

export type RejectDraftInvoiceV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<InvoiceNotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<NonApproveOrRejectableDraftInvoiceError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type RejectDraftInvoiceV1Error = RejectDraftInvoiceV1Errors[keyof RejectDraftInvoiceV1Errors];

export type RejectDraftInvoiceV1Responses = {
    200: unknown;
};

export type SubscribeToNewsletterV1Data = {
    body: SubscribeToNewsletterCommand;
    path?: never;
    query?: never;
    url: '/api/v1/newsletters/subscribe';
};

export type SubscribeToNewsletterV1Errors = {
    409: {
        traceId?: string | null;
        errors?: Array<AlreadySubscribedError | OptInAlreadyRequestedError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SubscribeToNewsletterV1Error = SubscribeToNewsletterV1Errors[keyof SubscribeToNewsletterV1Errors];

export type SubscribeToNewsletterV1Responses = {
    204: void;
};

export type SubscribeToNewsletterV1Response = SubscribeToNewsletterV1Responses[keyof SubscribeToNewsletterV1Responses];

export type ViewNewsItemIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        pagination?: PaginatedOffsetQuery;
    };
    url: '/api/v1/news-items';
};

export type ViewNewsItemIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewNewsItemIndexV1Error = ViewNewsItemIndexV1Errors[keyof ViewNewsItemIndexV1Errors];

export type ViewNewsItemIndexV1Responses = {
    200: ViewNewsIndexResponse;
};

export type ViewNewsItemIndexV1Response = ViewNewsItemIndexV1Responses[keyof ViewNewsItemIndexV1Responses];

export type CreateNewsItemV1Data = {
    body: CreateNewsItemCommand;
    path?: never;
    query?: never;
    url: '/api/v1/news-items';
};

export type CreateNewsItemV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateNewsItemV1Error = CreateNewsItemV1Errors[keyof CreateNewsItemV1Errors];

export type CreateNewsItemV1Responses = {
    201: CreateNewsItemResponse;
};

export type CreateNewsItemV1Response = CreateNewsItemV1Responses[keyof CreateNewsItemV1Responses];

export type DeleteNewsItemV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/news-items/{uuid}';
};

export type DeleteNewsItemV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type DeleteNewsItemV1Error = DeleteNewsItemV1Errors[keyof DeleteNewsItemV1Errors];

export type DeleteNewsItemV1Responses = {
    200: unknown;
};

export type ViewNewsItemV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/news-items/{uuid}';
};

export type ViewNewsItemV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewNewsItemV1Error = ViewNewsItemV1Errors[keyof ViewNewsItemV1Errors];

export type ViewNewsItemV1Responses = {
    200: ViewNewsItemResponse;
};

export type ViewNewsItemV1Response = ViewNewsItemV1Responses[keyof ViewNewsItemV1Responses];

export type UpdateNewsItemV1Data = {
    body: UpdateNewsItemCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/news-items/{uuid}';
};

export type UpdateNewsItemV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<NewsItemTranslationExistsError | NoStartDateOrEndDateExpectedError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateNewsItemV1Error = UpdateNewsItemV1Errors[keyof UpdateNewsItemV1Errors];

export type UpdateNewsItemV1Responses = {
    200: UpdateNewsItemResponse;
};

export type UpdateNewsItemV1Response = UpdateNewsItemV1Responses[keyof UpdateNewsItemV1Responses];

export type ViewDashboardNewsItemIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        pagination?: PaginatedOffsetQuery;
        filter?: ViewDashboardNewItemFilterQuery;
    };
    url: '/api/v1/dashboard-news-items';
};

export type ViewDashboardNewsItemIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDashboardNewsItemIndexV1Error = ViewDashboardNewsItemIndexV1Errors[keyof ViewDashboardNewsItemIndexV1Errors];

export type ViewDashboardNewsItemIndexV1Responses = {
    200: ViewDashboardNewsIndexResponse;
};

export type ViewDashboardNewsItemIndexV1Response = ViewDashboardNewsItemIndexV1Responses[keyof ViewDashboardNewsItemIndexV1Responses];

export type ViewDashboardNewsItemV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/dashboard-news-items/{uuid}';
};

export type ViewDashboardNewsItemV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewDashboardNewsItemV1Error = ViewDashboardNewsItemV1Errors[keyof ViewDashboardNewsItemV1Errors];

export type ViewDashboardNewsItemV1Responses = {
    200: ViewDashboardNewsItemResponse;
};

export type ViewDashboardNewsItemV1Response = ViewDashboardNewsItemV1Responses[keyof ViewDashboardNewsItemV1Responses];

export type CreatePackagingRequestV1Data = {
    body: CreatePackagingRequestCommand;
    path?: never;
    query?: never;
    url: '/api/v1/packaging-requests';
};

export type CreatePackagingRequestV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreatePackagingRequestV1Error = CreatePackagingRequestV1Errors[keyof CreatePackagingRequestV1Errors];

export type CreatePackagingRequestV1Responses = {
    201: CreatePackagingRequestResponse;
};

export type CreatePackagingRequestV1Response = CreatePackagingRequestV1Responses[keyof CreatePackagingRequestV1Responses];

export type ViewPackagingRequestV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/packaging-requests/{uuid}';
};

export type ViewPackagingRequestV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<CustomerNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPackagingRequestV1Error = ViewPackagingRequestV1Errors[keyof ViewPackagingRequestV1Errors];

export type ViewPackagingRequestV1Responses = {
    200: ViewPackagingRequestResponse;
};

export type ViewPackagingRequestV1Response = ViewPackagingRequestV1Responses[keyof ViewPackagingRequestV1Responses];

export type UpdatePackagingRequestV1Data = {
    body: UpdatePackagingRequestCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/packaging-requests/{uuid}';
};

export type UpdatePackagingRequestV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<CustomerNotAccessibleError | FieldMustBeNullError | MissingRequiredFieldError | WasteProducerNotAccessibleError | PickUpAddressNotAccessibleError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdatePackagingRequestV1Error = UpdatePackagingRequestV1Errors[keyof UpdatePackagingRequestV1Errors];

export type UpdatePackagingRequestV1Responses = {
    200: UpdatePackagingRequestResponse;
};

export type UpdatePackagingRequestV1Response = UpdatePackagingRequestV1Responses[keyof UpdatePackagingRequestV1Responses];

export type SubmitPackagingRequestV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/packaging-requests/{uuid}/submit';
};

export type SubmitPackagingRequestV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<ContractLineNotAccessibleError | ContractLineNotOfCustomerError | ContractLineNotOfPickUpAddressesError | UpdateOnlyPackagingRequestError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<PackagingRequestAlreadySubmitted>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SubmitPackagingRequestV1Error = SubmitPackagingRequestV1Errors[keyof SubmitPackagingRequestV1Errors];

export type SubmitPackagingRequestV1Responses = {
    /**
     * Packaging request submitted
     */
    200: SubmitPackagingRequestResponse;
};

export type SubmitPackagingRequestV1Response = SubmitPackagingRequestV1Responses[keyof SubmitPackagingRequestV1Responses];

export type BulkDeletePackagingRequestV1Data = {
    body: BulkDeletePackagingRequestCommand;
    path?: never;
    query?: never;
    url: '/api/v1/packaging-requests/bulk';
};

export type BulkDeletePackagingRequestV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type BulkDeletePackagingRequestV1Error = BulkDeletePackagingRequestV1Errors[keyof BulkDeletePackagingRequestV1Errors];

export type BulkDeletePackagingRequestV1Responses = {
    200: unknown;
};

export type CopyPackagingRequestSapV1Data = {
    body?: never;
    path: {
        requestNumber: string;
    };
    query?: never;
    url: '/api/v1/packaging-requests/sap/{requestNumber}/copy';
};

export type CopyPackagingRequestSapV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<PickUpRequestNotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<CopyNonSubmittedPickUpRequestError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CopyPackagingRequestSapV1Error = CopyPackagingRequestSapV1Errors[keyof CopyPackagingRequestSapV1Errors];

export type CopyPackagingRequestSapV1Responses = {
    201: CopyPackagingRequestSapResponse;
};

export type CopyPackagingRequestSapV1Response = CopyPackagingRequestSapV1Responses[keyof CopyPackagingRequestSapV1Responses];

export type ViewPackagingTypeIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewPackagingTypeIndexFilterQuery;
        search?: string;
    };
    url: '/api/v1/packaging-types';
};

export type ViewPackagingTypeIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPackagingTypeIndexV1Error = ViewPackagingTypeIndexV1Errors[keyof ViewPackagingTypeIndexV1Errors];

export type ViewPackagingTypeIndexV1Responses = {
    200: ViewPackagingTypeIndexResponse;
};

export type ViewPackagingTypeIndexV1Response = ViewPackagingTypeIndexV1Responses[keyof ViewPackagingTypeIndexV1Responses];

export type ViewPickUpAddressIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewPickUpAddressIndexFilterQuery;
        search?: string;
        pagination?: ViewPickUpAddressIndexPaginationQuery;
    };
    url: '/api/v1/pick-up-addresses';
};

export type ViewPickUpAddressIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPickUpAddressIndexV1Error = ViewPickUpAddressIndexV1Errors[keyof ViewPickUpAddressIndexV1Errors];

export type ViewPickUpAddressIndexV1Responses = {
    200: ViewPickUpAddressIndexResponse;
};

export type ViewPickUpAddressIndexV1Response = ViewPickUpAddressIndexV1Responses[keyof ViewPickUpAddressIndexV1Responses];

export type ViewSuggestedPickUpAddressesV1Data = {
    body?: never;
    path?: never;
    query?: {
        filter?: ViewSuggestedPickUpAddressesFilterQuery;
    };
    url: '/api/v1/suggested-pick-up-addresses';
};

export type ViewSuggestedPickUpAddressesV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewSuggestedPickUpAddressesV1Error = ViewSuggestedPickUpAddressesV1Errors[keyof ViewSuggestedPickUpAddressesV1Errors];

export type ViewSuggestedPickUpAddressesV1Responses = {
    200: ViewSuggestedPickUpAddressesResponse;
};

export type ViewSuggestedPickUpAddressesV1Response = ViewSuggestedPickUpAddressesV1Responses[keyof ViewSuggestedPickUpAddressesV1Responses];

export type GetIsPoNumberAndCostCenterRequiredV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: GetIsPoNumberAndCostCenterRequiredFilterQuery;
    };
    url: '/api/v1/pick-up-requests/is-po-number-and-cost-center-required';
};

export type GetIsPoNumberAndCostCenterRequiredV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type GetIsPoNumberAndCostCenterRequiredV1Error = GetIsPoNumberAndCostCenterRequiredV1Errors[keyof GetIsPoNumberAndCostCenterRequiredV1Errors];

export type GetIsPoNumberAndCostCenterRequiredV1Responses = {
    /**
     * Returns whether PO number and cost center are required
     */
    200: GetIsPoNumberAndCostCenterRequiredResponse;
};

export type GetIsPoNumberAndCostCenterRequiredV1Response = GetIsPoNumberAndCostCenterRequiredV1Responses[keyof GetIsPoNumberAndCostCenterRequiredV1Responses];

export type ViewPickUpRequestIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        pagination?: ViewPickUpRequestIndexPaginationQuery;
        filter: ViewPickUpRequestIndexFilterQuery;
    };
    url: '/api/v1/pick-up-requests';
};

export type ViewPickUpRequestIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPickUpRequestIndexV1Error = ViewPickUpRequestIndexV1Errors[keyof ViewPickUpRequestIndexV1Errors];

export type ViewPickUpRequestIndexV1Responses = {
    200: ViewPickUpRequestIndexResponse;
};

export type ViewPickUpRequestIndexV1Response = ViewPickUpRequestIndexV1Responses[keyof ViewPickUpRequestIndexV1Responses];

export type CreatePickUpRequestV1Data = {
    body: CreatePickUpRequestCommand;
    path?: never;
    query?: never;
    url: '/api/v1/pick-up-requests';
};

export type CreatePickUpRequestV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreatePickUpRequestV1Error = CreatePickUpRequestV1Errors[keyof CreatePickUpRequestV1Errors];

export type CreatePickUpRequestV1Responses = {
    201: CreatePickUpRequestResponse;
};

export type CreatePickUpRequestV1Response = CreatePickUpRequestV1Responses[keyof CreatePickUpRequestV1Responses];

export type ViewPickUpRequestV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/{uuid}';
};

export type ViewPickUpRequestV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPickUpRequestV1Error = ViewPickUpRequestV1Errors[keyof ViewPickUpRequestV1Errors];

export type ViewPickUpRequestV1Responses = {
    200: ViewPickUpRequestResponse;
};

export type ViewPickUpRequestV1Response = ViewPickUpRequestV1Responses[keyof ViewPickUpRequestV1Responses];

export type UpdatePickUpRequestV1Data = {
    body: UpdatePickUpRequestCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/{uuid}';
};

export type UpdatePickUpRequestV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<CustomerNotAccessibleError | MissingEwcLevelsError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<PickUpRequestNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdatePickUpRequestV1Error = UpdatePickUpRequestV1Errors[keyof UpdatePickUpRequestV1Errors];

export type UpdatePickUpRequestV1Responses = {
    200: UpdatePickUpRequestResponse;
};

export type UpdatePickUpRequestV1Response = UpdatePickUpRequestV1Responses[keyof UpdatePickUpRequestV1Responses];

export type ViewWasteProducerIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewWasteProducerIndexFilterQuery;
        search?: string;
        pagination?: ViewWasteProducerIndexPaginationQuery;
    };
    url: '/api/v1/waste-producers';
};

export type ViewWasteProducerIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewWasteProducerIndexV1Error = ViewWasteProducerIndexV1Errors[keyof ViewWasteProducerIndexV1Errors];

export type ViewWasteProducerIndexV1Responses = {
    200: ViewWasteProducerIndexResponse;
};

export type ViewWasteProducerIndexV1Response = ViewWasteProducerIndexV1Responses[keyof ViewWasteProducerIndexV1Responses];

export type ViewSuggestedWasteProducersV1Data = {
    body?: never;
    path?: never;
    query?: {
        filter?: ViewSuggestedWasteProducersFilterQuery;
    };
    url: '/api/v1/suggested-waste-producers';
};

export type ViewSuggestedWasteProducersV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewSuggestedWasteProducersV1Error = ViewSuggestedWasteProducersV1Errors[keyof ViewSuggestedWasteProducersV1Errors];

export type ViewSuggestedWasteProducersV1Responses = {
    200: ViewSuggestedWasteProducersResponse;
};

export type ViewSuggestedWasteProducersV1Response = ViewSuggestedWasteProducersV1Responses[keyof ViewSuggestedWasteProducersV1Responses];

export type SubmitPickUpRequestV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/{uuid}/submit';
};

export type SubmitPickUpRequestV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<ContractLineNotAccessibleError | ContractLineNotOfCustomerError | ContractLineNotOfPickUpAddressesError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<PickUpRequestAlreadySubmitted>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SubmitPickUpRequestV1Error = SubmitPickUpRequestV1Errors[keyof SubmitPickUpRequestV1Errors];

export type SubmitPickUpRequestV1Responses = {
    /**
     * Pick up request submitted
     */
    200: SubmitPickUpRequestResponse;
};

export type SubmitPickUpRequestV1Response = SubmitPickUpRequestV1Responses[keyof SubmitPickUpRequestV1Responses];

export type BulkDeletePickUpRequestV1Data = {
    body: BulkDeletePickUpRequestCommand;
    path?: never;
    query?: never;
    url: '/api/v1/pick-up-requests/bulk';
};

export type BulkDeletePickUpRequestV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type BulkDeletePickUpRequestV1Error = BulkDeletePickUpRequestV1Errors[keyof BulkDeletePickUpRequestV1Errors];

export type BulkDeletePickUpRequestV1Responses = {
    200: unknown;
};

export type ViewPickUpRequestSapV1Data = {
    body?: never;
    path: {
        requestNumber: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/sap/{requestNumber}';
};

export type ViewPickUpRequestSapV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<unknown>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewPickUpRequestSapV1Error = ViewPickUpRequestSapV1Errors[keyof ViewPickUpRequestSapV1Errors];

export type ViewPickUpRequestSapV1Responses = {
    200: ViewPickUpRequestSapResponse;
};

export type ViewPickUpRequestSapV1Response = ViewPickUpRequestSapV1Responses[keyof ViewPickUpRequestSapV1Responses];

export type UpdatePickUpRequestSapV1Data = {
    body: UpdatePickUpRequestSapCommand;
    path: {
        requestNumber: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/sap/{requestNumber}';
};

export type UpdatePickUpRequestSapV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<InvalidUpdateSapPickUpRequestError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<PickUpRequestNotFoundError | PickUpRequestContractLineNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdatePickUpRequestSapV1Error = UpdatePickUpRequestSapV1Errors[keyof UpdatePickUpRequestSapV1Errors];

export type UpdatePickUpRequestSapV1Responses = {
    200: unknown;
};

export type CopyPickUpRequestSapV1Data = {
    body?: never;
    path: {
        requestNumber: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/sap/{requestNumber}/copy';
};

export type CopyPickUpRequestSapV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<PickUpRequestNotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<CopyNonSubmittedPickUpRequestError | InvalidPickUpRequestCopyError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CopyPickUpRequestSapV1Error = CopyPickUpRequestSapV1Errors[keyof CopyPickUpRequestSapV1Errors];

export type CopyPickUpRequestSapV1Responses = {
    201: CopyPickUpRequestSapResponse;
};

export type CopyPickUpRequestSapV1Response = CopyPickUpRequestSapV1Responses[keyof CopyPickUpRequestSapV1Responses];

export type SubmitPickUpRequestSapV1Data = {
    body?: never;
    path: {
        requestNumber: string;
    };
    query?: never;
    url: '/api/v1/pick-up-requests/sap/{requestNumber}/submit';
};

export type SubmitPickUpRequestSapV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<PickUpRequestNotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<InvalidIndascanSubmitStatusError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SubmitPickUpRequestSapV1Error = SubmitPickUpRequestSapV1Errors[keyof SubmitPickUpRequestSapV1Errors];

export type SubmitPickUpRequestSapV1Responses = {
    200: unknown;
};

export type ViewTankerTypeIndexV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/tanker-types';
};

export type ViewTankerTypeIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewTankerTypeIndexV1Error = ViewTankerTypeIndexV1Errors[keyof ViewTankerTypeIndexV1Errors];

export type ViewTankerTypeIndexV1Responses = {
    200: ViewTankerTypeIndexResponse;
};

export type ViewTankerTypeIndexV1Response = ViewTankerTypeIndexV1Responses[keyof ViewTankerTypeIndexV1Responses];

export type ViewTransportTypeIndexV1Data = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/transport-types';
};

export type ViewTransportTypeIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewTransportTypeIndexV1Error = ViewTransportTypeIndexV1Errors[keyof ViewTransportTypeIndexV1Errors];

export type ViewTransportTypeIndexV1Responses = {
    200: ViewTransportTypeIndexResponse;
};

export type ViewTransportTypeIndexV1Response = ViewTransportTypeIndexV1Responses[keyof ViewTransportTypeIndexV1Responses];

export type ViewSalesOrganizationIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        search?: string;
    };
    url: '/api/v1/sales-organisations';
};

export type ViewSalesOrganizationIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewSalesOrganizationIndexV1Error = ViewSalesOrganizationIndexV1Errors[keyof ViewSalesOrganizationIndexV1Errors];

export type ViewSalesOrganizationIndexV1Response = {
    items: Array<SalesOrganization>;
};

export type ViewSalesOrganizationIndexV1Responses = {
    200: ViewSalesOrganizationIndexV1Response;
};

export type ViewUnNumberIndexV1Data = {
    body?: never;
    path?: never;
    query?: {
        search?: string;
        pagination?: ViewUnNumberIndexPaginationQuery;
    };
    url: '/api/v1/un-numbers';
};

export type ViewUnNumberIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUnNumberIndexV1Error = ViewUnNumberIndexV1Errors[keyof ViewUnNumberIndexV1Errors];

export type ViewUnNumberIndexV1Responses = {
    200: ViewUnNumberIndexResponse;
};

export type ViewUnNumberIndexV1Response = ViewUnNumberIndexV1Responses[keyof ViewUnNumberIndexV1Responses];

export type ViewUnNumberIndexForPickUpRequestV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewUnNumberIndexForPickUpRequestFilterQuery;
        search?: string;
    };
    url: '/api/v1/un-numbers-pick-up-request';
};

export type ViewUnNumberIndexForPickUpRequestV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewUnNumberIndexForPickUpRequestV1Error = ViewUnNumberIndexForPickUpRequestV1Errors[keyof ViewUnNumberIndexForPickUpRequestV1Errors];

export type ViewUnNumberIndexForPickUpRequestV1Responses = {
    200: ViewUnNumberIndexForPickUpRequestResponse;
};

export type ViewUnNumberIndexForPickUpRequestV1Response = ViewUnNumberIndexForPickUpRequestV1Responses[keyof ViewUnNumberIndexForPickUpRequestV1Responses];

export type ViewWasteInquiryIndexV1Data = {
    body?: never;
    path?: never;
    query: {
        filter: ViewWasteInquiryIndexFilterQuery;
        pagination?: ViewWasteInquiryIndexPaginationQuery;
    };
    url: '/api/v1/waste-inquiries';
};

export type ViewWasteInquiryIndexV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewWasteInquiryIndexV1Error = ViewWasteInquiryIndexV1Errors[keyof ViewWasteInquiryIndexV1Errors];

export type ViewWasteInquiryIndexV1Responses = {
    200: ViewWasteInquiryIndexResponse;
};

export type ViewWasteInquiryIndexV1Response = ViewWasteInquiryIndexV1Responses[keyof ViewWasteInquiryIndexV1Responses];

export type CreateWasteInquiryV1Data = {
    body: CreateWasteInquiryCommand;
    path?: never;
    query?: never;
    url: '/api/v1/waste-inquiries';
};

export type CreateWasteInquiryV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateWasteInquiryV1Error = CreateWasteInquiryV1Errors[keyof CreateWasteInquiryV1Errors];

export type CreateWasteInquiryV1Responses = {
    201: CreateWasteInquiryResponse;
};

export type CreateWasteInquiryV1Response = CreateWasteInquiryV1Responses[keyof CreateWasteInquiryV1Responses];

export type ViewWasteInquiryV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/waste-inquiries/{uuid}';
};

export type ViewWasteInquiryV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewWasteInquiryV1Error = ViewWasteInquiryV1Errors[keyof ViewWasteInquiryV1Errors];

export type ViewWasteInquiryV1Responses = {
    200: ViewWasteInquiryResponse;
};

export type ViewWasteInquiryV1Response = ViewWasteInquiryV1Responses[keyof ViewWasteInquiryV1Responses];

export type UpdateWasteInquiryV1Data = {
    body: UpdateWasteInquiryCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/waste-inquiries/{uuid}';
};

export type UpdateWasteInquiryV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<CustomerNotAccessibleError | MissingEwcLevelsError | EwcCodeNotFound | InvalidStableTemperatureError | FileNotAccessibleError | NoSdsFilesExpected | NoAnalysisReportFilesExpected | NoOptionExpectedWhenNoneSelected | NoSvhcExtraExpected>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<PickUpRequestNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateWasteInquiryV1Error = UpdateWasteInquiryV1Errors[keyof UpdateWasteInquiryV1Errors];

export type UpdateWasteInquiryV1Responses = {
    200: UpdateWasteInquiryResponse;
};

export type UpdateWasteInquiryV1Response = UpdateWasteInquiryV1Responses[keyof UpdateWasteInquiryV1Responses];

export type SubmitWasteInquiryV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/waste-inquiries/{uuid}/submit';
};

export type SubmitWasteInquiryV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<WasteInquiryAlreadySubmitted>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SubmitWasteInquiryV1Error = SubmitWasteInquiryV1Errors[keyof SubmitWasteInquiryV1Errors];

export type SubmitWasteInquiryV1Responses = {
    /**
     * Waste inquiry submitted
     */
    200: SubmitWasteInquiryResponse;
};

export type SubmitWasteInquiryV1Response = SubmitWasteInquiryV1Responses[keyof SubmitWasteInquiryV1Responses];

export type ViewWasteInquirySapV1Data = {
    body?: never;
    path: {
        inquiryNumber: string;
    };
    query?: never;
    url: '/api/v1/waste-inquiries/sap/{inquiryNumber}';
};

export type ViewWasteInquirySapV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<unknown>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewWasteInquirySapV1Error = ViewWasteInquirySapV1Errors[keyof ViewWasteInquirySapV1Errors];

export type ViewWasteInquirySapV1Responses = {
    200: ViewWasteInquirySapResponse;
};

export type ViewWasteInquirySapV1Response = ViewWasteInquirySapV1Responses[keyof ViewWasteInquirySapV1Responses];

export type AddDocumentToWasteInquirySapV1Data = {
    body: AddDocumentToWasteInquirySapCommand;
    path: {
        inquiryNumber: string;
    };
    query?: never;
    url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/add-documents';
};

export type AddDocumentToWasteInquirySapV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<unknown>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type AddDocumentToWasteInquirySapV1Error = AddDocumentToWasteInquirySapV1Errors[keyof AddDocumentToWasteInquirySapV1Errors];

export type AddDocumentToWasteInquirySapV1Responses = {
    204: void;
};

export type AddDocumentToWasteInquirySapV1Response = AddDocumentToWasteInquirySapV1Responses[keyof AddDocumentToWasteInquirySapV1Responses];

export type BulkDeleteWasteInquiryV1Data = {
    body: BulkDeleteWasteInquiryCommand;
    path?: never;
    query?: never;
    url: '/api/v1/waste-inquiries/bulk';
};

export type BulkDeleteWasteInquiryV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    409: {
        traceId?: string | null;
        errors?: Array<WasteInquiryAlreadySubmitted>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type BulkDeleteWasteInquiryV1Error = BulkDeleteWasteInquiryV1Errors[keyof BulkDeleteWasteInquiryV1Errors];

export type BulkDeleteWasteInquiryV1Responses = {
    200: unknown;
};

export type CopyWasteInquirySapV1Data = {
    body?: never;
    path: {
        inquiryNumber: string;
    };
    query?: never;
    url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/copy';
};

export type CopyWasteInquirySapV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CopyWasteInquirySapV1Error = CopyWasteInquirySapV1Errors[keyof CopyWasteInquirySapV1Errors];

export type CopyWasteInquirySapV1Responses = {
    201: CopyWasteInquirySapResponse;
};

export type CopyWasteInquirySapV1Response = CopyWasteInquirySapV1Responses[keyof CopyWasteInquirySapV1Responses];

export type CreateWeeklyPlanningRequestV1Data = {
    body: CreateWeeklyPlanningRequestCommand;
    path?: never;
    query?: never;
    url: '/api/v1/weekly-planning-requests';
};

export type CreateWeeklyPlanningRequestV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type CreateWeeklyPlanningRequestV1Error = CreateWeeklyPlanningRequestV1Errors[keyof CreateWeeklyPlanningRequestV1Errors];

export type CreateWeeklyPlanningRequestV1Responses = {
    201: CreateWeeklyPlanningRequestResponse;
};

export type CreateWeeklyPlanningRequestV1Response = CreateWeeklyPlanningRequestV1Responses[keyof CreateWeeklyPlanningRequestV1Responses];

export type ViewWeeklyPlanningRequestV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/weekly-planning-requests/{uuid}';
};

export type ViewWeeklyPlanningRequestV1Errors = {
    404: {
        traceId?: string | null;
        errors?: Array<CustomerNotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type ViewWeeklyPlanningRequestV1Error = ViewWeeklyPlanningRequestV1Errors[keyof ViewWeeklyPlanningRequestV1Errors];

export type ViewWeeklyPlanningRequestV1Responses = {
    200: ViewWeeklyPlanningRequestResponse;
};

export type ViewWeeklyPlanningRequestV1Response = ViewWeeklyPlanningRequestV1Responses[keyof ViewWeeklyPlanningRequestV1Responses];

export type UpdateWeeklyPlanningRequestV1Data = {
    body: UpdateWeeklyPlanningRequestCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/weekly-planning-requests/{uuid}';
};

export type UpdateWeeklyPlanningRequestV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<CustomerNotAccessibleError | WasteProducerNotAccessibleError | PickUpAddressNotAccessibleError | FieldMustBeNullError | FileNotAccessibleError | CustomerNotProvidedError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type UpdateWeeklyPlanningRequestV1Error = UpdateWeeklyPlanningRequestV1Errors[keyof UpdateWeeklyPlanningRequestV1Errors];

export type UpdateWeeklyPlanningRequestV1Responses = {
    200: UpdateWeeklyPlanningRequestResponse;
};

export type UpdateWeeklyPlanningRequestV1Response = UpdateWeeklyPlanningRequestV1Responses[keyof UpdateWeeklyPlanningRequestV1Responses];

export type AddWprPickUpRequestV1Data = {
    body: AddWprPickUpRequestCommand;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/weekly-planning-requests/{uuid}/add-pick-up-request';
};

export type AddWprPickUpRequestV1Errors = {
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type AddWprPickUpRequestV1Error = AddWprPickUpRequestV1Errors[keyof AddWprPickUpRequestV1Errors];

export type AddWprPickUpRequestV1Responses = {
    201: AddWprPickUpRequestResponse;
};

export type AddWprPickUpRequestV1Response = AddWprPickUpRequestV1Responses[keyof AddWprPickUpRequestV1Responses];

export type SubmitWeeklyPlanningRequestV1Data = {
    body?: never;
    path: {
        uuid: string;
    };
    query?: never;
    url: '/api/v1/weekly-planning-requests/{uuid}/submit';
};

export type SubmitWeeklyPlanningRequestV1Errors = {
    400: {
        traceId?: string | null;
        errors?: Array<WeeklyPlanningRequestAlreadySubmittedError | ContractLineNotAccessibleError | ContractLineNotOfCustomerError | ContractLineNotOfPickUpAddressesError>;
    };
    404: {
        traceId?: string | null;
        errors?: Array<NotFoundError>;
    };
    500: {
        traceId?: string | null;
        errors?: Array<InternalServerApiError>;
    };
};

export type SubmitWeeklyPlanningRequestV1Error = SubmitWeeklyPlanningRequestV1Errors[keyof SubmitWeeklyPlanningRequestV1Errors];

export type SubmitWeeklyPlanningRequestV1Responses = {
    /**
     * Weekly planning request submitted
     */
    200: SubmitWeeklyPlanningRequestResponse;
};

export type SubmitWeeklyPlanningRequestV1Response = SubmitWeeklyPlanningRequestV1Responses[keyof SubmitWeeklyPlanningRequestV1Responses];